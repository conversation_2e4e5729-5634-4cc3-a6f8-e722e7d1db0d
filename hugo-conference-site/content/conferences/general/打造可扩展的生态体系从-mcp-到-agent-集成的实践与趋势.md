---
title: "打造可扩展的生态体系：从 MCP 到 Agent 集成的实践与趋势"
date: "2025-07-02T14:12:25.990982+00:00"
seminar: "202503 AICon Shanghai"
category: "general"
url: "https://aicon.infoq.cn/2025/shanghai/presentation/6499"
pdf_url: "http://ppt.geekbang.org/slide/download?cid=158&pid=4989"
summary: "本次演讲将深入探讨端侧智能领域中，如何设计适用于资源受限环境的大模型架构，并探讨基础算法的改进思路。
随着人工智能技术的飞速发展，将强大的模型能力部署到端侧设备（如手机、嵌入式设备等）的需求日益增长。然而，端侧设备在计算资源、存储空间和功耗等方面存在诸多限制，给传统的大模型带来了巨大的挑战。本次演讲..."
conference_id: "109c5ade-2a25-438f-b680-75d774442675"
---

## 會議概述

本次演讲将深入探讨端侧智能领域中，如何设计适用于资源受限环境的大模型架构，并探讨基础算法的改进思路。
随着人工智能技术的飞速发展，将强大的模型能力部署到端侧设备（如手机、嵌入式设备等）的需求日益增长。然而，端侧设备在计算资源、存储空间和功耗等方面存在诸多限制，给传统的大模型带来了巨大的挑战。本次演讲...

## 會議描述

本次演讲将深入探讨端侧智能领域中，如何设计适用于资源受限环境的大模型架构，并探讨基础算法的改进思路。
随着人工智能技术的飞速发展，将强大的模型能力部署到端侧设备（如手机、嵌入式设备等）的需求日益增长。然而，端侧设备在计算资源、存储空间和功耗等方面存在诸多限制，给传统的大模型带来了巨大的挑战。本次演讲将首先分析端侧智能面临的关键问题，然后重点介绍当前主流的端侧大模型架构设计方向。同时，也将探讨针对端侧场景的基础算法改进思路。最后，将展望端侧智能未来的发展趋势，并探讨其在各个领域的潜在应用价值。
演讲提纲：
端侧智能的兴起与挑战
端侧大模型架构设计方向
端侧场景的基础算法改进思路
端侧智能的未来发展趋势展望
端侧智能的潜在应用价值
听众收益：
了解端侧智能面临的核心技术难题
掌握当前主流的端侧大模型架构设计方法
理解针对端侧场景进行算法优化的基本思路
洞悉端侧智能未来的发展方向和潜在机遇

## 相關資源

- [會議連結](https://aicon.infoq.cn/2025/shanghai/presentation/6499)
- [簡報下載](http://ppt.geekbang.org/slide/download?cid=158&pid=4989)

## 關鍵詞



---

*本內容由 TrendScope 系統自動生成*
