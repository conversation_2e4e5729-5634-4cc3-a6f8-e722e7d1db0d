---
title: "超越算力瓶颈，大模型推理的跨层优化前沿实践"
date: "2025-07-02T14:12:25.990982+00:00"
seminar: "202503 AICon Shanghai"
category: "general"
url: "https://aicon.infoq.cn/2025/shanghai/presentation/6418"
pdf_url: "http://ppt.geekbang.org/slide/download?cid=158&pid=4983"
summary: "快手自研代码大模型 Kwaipilot 正式发布，截止到目前，新增代码的AI生成率达20%。通过构建代码形式化表征和逻辑推理，实践 MoE 架构、“编码即标注”数据飞轮，在快手私域代码的理解和生成场景上，表现优于外部模型，自研代码续写模型 KwaiCoder-23BA4-v1 达到行业 SOTA 水..."
conference_id: "1bec62d8-bb41-4884-8033-7795e68e32d8"
---

## 會議概述

快手自研代码大模型 Kwaipilot 正式发布，截止到目前，新增代码的AI生成率达20%。通过构建代码形式化表征和逻辑推理，实践 MoE 架构、“编码即标注”数据飞轮，在快手私域代码的理解和生成场景上，表现优于外部模型，自研代码续写模型 KwaiCoder-23BA4-v1 达到行业 SOTA 水...

## 會議描述

快手自研代码大模型 Kwaipilot 正式发布，截止到目前，新增代码的AI生成率达20%。通过构建代码形式化表征和逻辑推理，实践 MoE 架构、“编码即标注”数据飞轮，在快手私域代码的理解和生成场景上，表现优于外部模型，自研代码续写模型 KwaiCoder-23BA4-v1 达到行业 SOTA 水平。
本次演讲将从数据收集、模型训练、产品交互、落地实践多个维度介绍在快手研发领域落地代码生成大模型的实践经历。以及如何激发与结合大模型的推理能力，有效利用研发环节中的暗知识，完成研发场景下的端到端需求生存任务。
演讲提纲：
Kwaipiot在哪些研发场景下进行了AI研发提效？
Kwaipiot在代码生成，端到端需求交付领域的应用
Kwaipilot在智能cr，测试生成，智能 oncall 场景的应用
 Kwaipilot 如何协调模型和产品的关系，打造最佳用处体验的研发提效工具
 如何平衡模型的推理成本与性能
 如何结合使用场景构造大模型训练需要的语料数据
 哪些因素在影响产品最终的用户体验？
总结与展望
听众收益：
了解大模型可以在哪些研发流程赋能
了解如何转起数据飞轮不断提升模型能力
学习如何使模型具备自我反思与推理能力

## 相關資源

- [會議連結](https://aicon.infoq.cn/2025/shanghai/presentation/6418)
- [簡報下載](http://ppt.geekbang.org/slide/download?cid=158&pid=4983)

## 關鍵詞



---

*本內容由 TrendScope 系統自動生成*
