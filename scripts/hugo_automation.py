#!/usr/bin/env python3
"""
Hugo 會議報告系統 - 自動化流程腳本
端到端自動化：從 BigQuery 資料提取到 Hugo 網站部署
"""

import os
import sys
import json
import subprocess
import argparse
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# 添加專案根目錄到 Python 路徑
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from scripts.hugo_data_extractor import HugoDataExtractor
from scripts.hugo_content_generator import HugoContentGenerator
from scripts.hugo_site_builder import HugoSiteBuilder


class HugoAutomation:
    """Hugo 自動化流程管理器"""
    
    def __init__(self, 
                 site_path: str = "hugo-conference-site",
                 gemini_api_key: Optional[str] = None,
                 include_translation: bool = True,
                 limit_sessions: Optional[int] = None):
        """初始化自動化管理器"""
        self.site_path = Path(site_path)
        self.include_translation = include_translation
        self.limit_sessions = limit_sessions
        
        # 初始化各個模組
        self.data_extractor = HugoDataExtractor()
        self.content_generator = HugoContentGenerator(gemini_api_key)
        self.site_builder = HugoSiteBuilder(str(site_path))
        
        # 創建日誌目錄
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 設置日誌文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"hugo_automation_{timestamp}.log"
    
    def log(self, message: str, level: str = "INFO"):
        """記錄日誌"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + "\n")
    
    def step_1_extract_data(self) -> bool:
        """步驟 1: 從 BigQuery 提取資料"""
        self.log("=== 步驟 1: 從 BigQuery 提取資料 ===")
        
        try:
            # 提取完整資料集
            self.dataset = self.data_extractor.extract_complete_dataset(self.limit_sessions)
            
            if not self.dataset.sessions:
                self.log("沒有找到會議資料", "ERROR")
                return False
            
            self.log(f"成功提取 {len(self.dataset.sessions)} 個會議資料")
            self.log(f"分類數量: {len(self.dataset.categories)}")
            self.log(f"統計資料: {self.dataset.statistics}")
            
            return True
            
        except Exception as e:
            self.log(f"資料提取失敗: {e}", "ERROR")
            return False
    
    def step_2_generate_content(self) -> bool:
        """步驟 2: 使用 LLM 生成內容"""
        self.log("=== 步驟 2: 使用 LLM 生成內容 ===")
        
        try:
            # 處理所有會議資料
            self.generated_contents = self.content_generator.process_dataset(
                self.dataset, 
                self.include_translation
            )
            
            if not self.generated_contents:
                self.log("內容生成失敗", "ERROR")
                return False
            
            self.log(f"成功生成 {len(self.generated_contents)} 個內容")
            
            return True
            
        except Exception as e:
            self.log(f"內容生成失敗: {e}", "ERROR")
            return False
    
    def step_3_build_site(self) -> bool:
        """步驟 3: 建構 Hugo 網站"""
        self.log("=== 步驟 3: 建構 Hugo 網站 ===")
        
        try:
            # 建構完整網站
            self.site_builder.build_complete_site(self.dataset, self.generated_contents)
            
            self.log(f"Hugo 網站建構完成: {self.site_path}")
            
            return True
            
        except Exception as e:
            self.log(f"網站建構失敗: {e}", "ERROR")
            return False
    
    def step_4_build_hugo(self) -> bool:
        """步驟 4: 執行 Hugo 建構"""
        self.log("=== 步驟 4: 執行 Hugo 建構 ===")
        
        try:
            # 檢查 Hugo 是否安裝
            result = subprocess.run(['hugo', 'version'], 
                                  capture_output=True, text=True, cwd=self.site_path)
            
            if result.returncode != 0:
                self.log("Hugo 未安裝或不在 PATH 中", "ERROR")
                self.log("請安裝 Hugo: https://gohugo.io/getting-started/installing/")
                return False
            
            self.log(f"Hugo 版本: {result.stdout.strip()}")
            
            # 執行 Hugo 建構
            result = subprocess.run(['hugo', '--minify'], 
                                  capture_output=True, text=True, cwd=self.site_path)
            
            if result.returncode != 0:
                self.log(f"Hugo 建構失敗: {result.stderr}", "ERROR")
                return False
            
            self.log("Hugo 建構成功")
            self.log(f"靜態網站生成在: {self.site_path / 'public'}")
            
            return True
            
        except Exception as e:
            self.log(f"Hugo 建構失敗: {e}", "ERROR")
            return False
    
    def step_5_create_shortcodes(self):
        """步驟 5: 創建 Hugo Shortcodes"""
        self.log("=== 步驟 5: 創建 Hugo Shortcodes ===")
        
        # 最新會議 shortcode
        recent_sessions_shortcode = '''{{ $recent := where .Site.RegularPages "Type" "conferences" | first 6 }}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {{ range $recent }}
    <article class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
        <h3 class="text-lg font-semibold mb-2">
            <a href="{{ .RelPermalink }}" class="text-blue-600 hover:text-blue-800">
                {{ .Title }}
            </a>
        </h3>
        
        {{ if .Params.summary }}
        <p class="text-gray-600 text-sm mb-3">{{ .Params.summary | truncate 100 }}</p>
        {{ end }}
        
        <div class="flex justify-between items-center text-xs text-gray-500">
            <span>{{ .Date.Format "2006-01-02" }}</span>
            {{ if .Params.category }}
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">{{ .Params.category }}</span>
            {{ end }}
        </div>
    </article>
    {{ end }}
</div>'''
        
        # 分類圖表 shortcode
        category_chart_shortcode = '''<div id="categoryChart" class="w-full h-64 mb-8"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('categoryChart').getContext('2d');
    
    // 從 Hugo 資料中獲取分類統計
    const categories = {{ .Site.Data.statistics.category_distribution | jsonify }};
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(categories),
            datasets: [{
                data: Object.values(categories),
                backgroundColor: [
                    '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
                    '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: '會議分類分布'
                }
            }
        }
    });
});
</script>'''
        
        # 關聯圖表 shortcode
        relationship_chart_shortcode = '''<div id="relationshipChart" class="w-full h-96 mb-8"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 使用 D3.js 創建關聯圖
    const width = document.getElementById('relationshipChart').offsetWidth;
    const height = 400;
    
    const svg = d3.select('#relationshipChart')
        .append('svg')
        .attr('width', width)
        .attr('height', height);
    
    // TODO: 實作關聯圖邏輯
    // 這裡可以根據會議標籤、分類等建立關聯關係
    
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', height / 2)
        .attr('text-anchor', 'middle')
        .text('關聯圖表 (開發中)')
        .style('font-size', '18px')
        .style('fill', '#666');
});
</script>'''
        
        # 創建 shortcodes 目錄和文件
        shortcodes_dir = self.site_path / "layouts" / "shortcodes"
        shortcodes_dir.mkdir(parents=True, exist_ok=True)
        
        with open(shortcodes_dir / "recent-sessions.html", 'w', encoding='utf-8') as f:
            f.write(recent_sessions_shortcode)
        
        with open(shortcodes_dir / "category-chart.html", 'w', encoding='utf-8') as f:
            f.write(category_chart_shortcode)
        
        with open(shortcodes_dir / "relationship-chart.html", 'w', encoding='utf-8') as f:
            f.write(relationship_chart_shortcode)
        
        self.log("Hugo Shortcodes 創建完成")
    
    def step_6_create_data_files(self):
        """步驟 6: 創建 Hugo 資料文件"""
        self.log("=== 步驟 6: 創建 Hugo 資料文件 ===")
        
        # 創建統計資料文件
        data_dir = self.site_path / "data"
        data_dir.mkdir(parents=True, exist_ok=True)
        
        with open(data_dir / "statistics.json", 'w', encoding='utf-8') as f:
            json.dump(self.dataset.statistics, f, ensure_ascii=False, indent=2)
        
        with open(data_dir / "categories.json", 'w', encoding='utf-8') as f:
            json.dump(self.dataset.categories, f, ensure_ascii=False, indent=2)
        
        self.log("Hugo 資料文件創建完成")
    
    def run_complete_pipeline(self) -> bool:
        """執行完整的自動化流程"""
        self.log("開始執行 Hugo 會議報告系統自動化流程")
        self.log(f"網站路徑: {self.site_path}")
        self.log(f"包含翻譯: {self.include_translation}")
        self.log(f"會議限制: {self.limit_sessions or '無限制'}")
        
        # 執行各個步驟
        steps = [
            ("提取資料", self.step_1_extract_data),
            ("生成內容", self.step_2_generate_content),
            ("建構網站", self.step_3_build_site),
            ("創建 Shortcodes", self.step_5_create_shortcodes),
            ("創建資料文件", self.step_6_create_data_files),
            ("Hugo 建構", self.step_4_build_hugo),
        ]
        
        for step_name, step_func in steps:
            self.log(f"開始執行: {step_name}")
            
            if callable(step_func):
                if not step_func():
                    self.log(f"步驟失敗: {step_name}", "ERROR")
                    return False
            else:
                step_func()
            
            self.log(f"步驟完成: {step_name}")
        
        self.log("=== 自動化流程完成 ===")
        self.log(f"網站已生成在: {self.site_path}")
        self.log(f"靜態文件在: {self.site_path / 'public'}")
        self.log("下一步:")
        self.log(f"1. cd {self.site_path}")
        self.log("2. hugo server -D")
        self.log("3. 訪問 http://localhost:1313")
        
        return True


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="Hugo 會議報告系統自動化")
    parser.add_argument("--site-path", default="hugo-conference-site", 
                       help="Hugo 網站路徑")
    parser.add_argument("--no-translation", action="store_true", 
                       help="不包含英文翻譯")
    parser.add_argument("--limit", type=int, 
                       help="限制處理的會議數量（用於測試）")
    parser.add_argument("--gemini-api-key", 
                       help="Gemini API Key（或使用環境變數 GEMINI_API_KEY）")
    
    args = parser.parse_args()
    
    # 檢查 API Key
    api_key = args.gemini_api_key or os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("錯誤: 請提供 Gemini API Key")
        print("方法 1: 設置環境變數 GEMINI_API_KEY")
        print("方法 2: 使用 --gemini-api-key 參數")
        sys.exit(1)
    
    # 創建自動化管理器
    automation = HugoAutomation(
        site_path=args.site_path,
        gemini_api_key=api_key,
        include_translation=not args.no_translation,
        limit_sessions=args.limit
    )
    
    # 執行自動化流程
    success = automation.run_complete_pipeline()
    
    if success:
        print("\n✅ 自動化流程成功完成！")
        sys.exit(0)
    else:
        print("\n❌ 自動化流程失敗，請檢查日誌")
        sys.exit(1)


if __name__ == "__main__":
    main()
