#!/usr/bin/env python3
"""
Hugo 會議報告系統 - 關聯圖表整合模組
整合現有的關聯圖表功能到 Hugo 網站中
"""

import os
import sys
import json
import networkx as nx
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

# 添加專案根目錄到 Python 路徑
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from scripts.hugo_data_extractor import ConferenceSession, ConferenceData


@dataclass
class RelationshipNode:
    """關聯圖節點"""
    id: str
    name: str
    type: str  # 'session', 'category', 'keyword', 'seminar'
    size: int = 1
    color: str = "#3B82F6"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class RelationshipEdge:
    """關聯圖邊"""
    source: str
    target: str
    weight: float = 1.0
    type: str = "related"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class RelationshipGraph:
    """關聯圖數據結構"""
    nodes: List[RelationshipNode]
    edges: List[RelationshipEdge]
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class HugoRelationshipAnalyzer:
    """Hugo 關聯分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.graph = nx.Graph()
        self.color_map = {
            'session': '#3B82F6',      # 藍色 - 會議
            'category': '#10B981',     # 綠色 - 分類
            'keyword': '#F59E0B',      # 黃色 - 關鍵詞
            'seminar': '#EF4444',      # 紅色 - 研討會
            'speaker': '#8B5CF6'       # 紫色 - 講者
        }
    
    def analyze_keyword_relationships(self, sessions: List[ConferenceSession]) -> Dict[str, Any]:
        """分析關鍵詞關聯性"""
        keyword_cooccurrence = defaultdict(int)
        keyword_sessions = defaultdict(set)
        
        # 收集關鍵詞共現數據
        for session in sessions:
            keywords = session.keywords if hasattr(session, 'keywords') and session.keywords else session.tags
            if not keywords:
                continue
            
            # 記錄關鍵詞出現的會議
            for keyword in keywords:
                keyword_sessions[keyword].add(session.conference_id)
            
            # 計算關鍵詞共現
            for i, kw1 in enumerate(keywords):
                for kw2 in keywords[i+1:]:
                    pair = tuple(sorted([kw1, kw2]))
                    keyword_cooccurrence[pair] += 1
        
        # 計算關鍵詞重要性
        keyword_importance = {kw: len(sessions) for kw, sessions in keyword_sessions.items()}
        
        return {
            'cooccurrence': dict(keyword_cooccurrence),
            'importance': keyword_importance,
            'sessions': {kw: list(sessions) for kw, sessions in keyword_sessions.items()}
        }
    
    def analyze_category_relationships(self, sessions: List[ConferenceSession]) -> Dict[str, Any]:
        """分析分類關聯性"""
        category_sessions = defaultdict(list)
        category_keywords = defaultdict(set)
        
        for session in sessions:
            category = session.category
            category_sessions[category].append(session.conference_id)
            
            keywords = session.keywords if hasattr(session, 'keywords') and session.keywords else session.tags
            if keywords:
                category_keywords[category].update(keywords)
        
        # 計算分類間的關鍵詞重疊
        category_similarity = {}
        categories = list(category_keywords.keys())
        
        for i, cat1 in enumerate(categories):
            for cat2 in categories[i+1:]:
                kw1 = category_keywords[cat1]
                kw2 = category_keywords[cat2]
                
                if kw1 and kw2:
                    intersection = len(kw1 & kw2)
                    union = len(kw1 | kw2)
                    similarity = intersection / union if union > 0 else 0
                    
                    if similarity > 0.1:  # 只保留相似度 > 0.1 的關聯
                        category_similarity[(cat1, cat2)] = similarity
        
        return {
            'sessions': dict(category_sessions),
            'keywords': {cat: list(kws) for cat, kws in category_keywords.items()},
            'similarity': category_similarity
        }
    
    def analyze_seminar_relationships(self, sessions: List[ConferenceSession]) -> Dict[str, Any]:
        """分析研討會關聯性"""
        seminar_categories = defaultdict(set)
        seminar_keywords = defaultdict(set)
        
        for session in sessions:
            seminar = session.seminar
            seminar_categories[seminar].add(session.category)
            
            keywords = session.keywords if hasattr(session, 'keywords') and session.keywords else session.tags
            if keywords:
                seminar_keywords[seminar].update(keywords)
        
        return {
            'categories': {sem: list(cats) for sem, cats in seminar_categories.items()},
            'keywords': {sem: list(kws) for sem, kws in seminar_keywords.items()}
        }
    
    def build_relationship_graph(self, dataset: ConferenceData) -> RelationshipGraph:
        """建構完整的關聯圖"""
        nodes = []
        edges = []
        
        # 分析各種關聯性
        keyword_analysis = self.analyze_keyword_relationships(dataset.sessions)
        category_analysis = self.analyze_category_relationships(dataset.sessions)
        seminar_analysis = self.analyze_seminar_relationships(dataset.sessions)
        
        # 創建會議節點
        for session in dataset.sessions:
            node = RelationshipNode(
                id=f"session_{session.conference_id}",
                name=session.name,
                type="session",
                size=1,
                color=self.color_map['session'],
                metadata={
                    'category': session.category,
                    'seminar': session.seminar,
                    'url': session.url
                }
            )
            nodes.append(node)
        
        # 創建分類節點
        for category, session_ids in dataset.categories.items():
            node = RelationshipNode(
                id=f"category_{category}",
                name=category.replace('-', ' ').title(),
                type="category",
                size=len(session_ids),
                color=self.color_map['category'],
                metadata={'session_count': len(session_ids)}
            )
            nodes.append(node)
            
            # 創建分類到會議的邊
            for session_id in session_ids:
                edge = RelationshipEdge(
                    source=f"category_{category}",
                    target=f"session_{session_id}",
                    weight=1.0,
                    type="belongs_to"
                )
                edges.append(edge)
        
        # 創建關鍵詞節點和邊
        top_keywords = sorted(keyword_analysis['importance'].items(), 
                            key=lambda x: x[1], reverse=True)[:20]  # 只取前20個關鍵詞
        
        for keyword, importance in top_keywords:
            node = RelationshipNode(
                id=f"keyword_{keyword}",
                name=keyword,
                type="keyword",
                size=importance,
                color=self.color_map['keyword'],
                metadata={'importance': importance}
            )
            nodes.append(node)
            
            # 創建關鍵詞到會議的邊
            for session_id in keyword_analysis['sessions'][keyword]:
                edge = RelationshipEdge(
                    source=f"keyword_{keyword}",
                    target=f"session_{session_id}",
                    weight=0.5,
                    type="tagged_with"
                )
                edges.append(edge)
        
        # 創建關鍵詞共現邊
        for (kw1, kw2), count in keyword_analysis['cooccurrence'].items():
            if count >= 2:  # 只保留共現次數 >= 2 的關聯
                edge = RelationshipEdge(
                    source=f"keyword_{kw1}",
                    target=f"keyword_{kw2}",
                    weight=count / 10.0,  # 標準化權重
                    type="cooccurs_with",
                    metadata={'cooccurrence_count': count}
                )
                edges.append(edge)
        
        # 創建分類相似性邊
        for (cat1, cat2), similarity in category_analysis['similarity'].items():
            edge = RelationshipEdge(
                source=f"category_{cat1}",
                target=f"category_{cat2}",
                weight=similarity,
                type="similar_to",
                metadata={'similarity': similarity}
            )
            edges.append(edge)
        
        return RelationshipGraph(
            nodes=nodes,
            edges=edges,
            metadata={
                'total_nodes': len(nodes),
                'total_edges': len(edges),
                'analysis_date': dataset.statistics.get('last_updated', ''),
                'keyword_analysis': keyword_analysis,
                'category_analysis': category_analysis,
                'seminar_analysis': seminar_analysis
            }
        )
    
    def export_for_d3js(self, graph: RelationshipGraph) -> Dict[str, Any]:
        """導出為 D3.js 格式"""
        d3_data = {
            'nodes': [
                {
                    'id': node.id,
                    'name': node.name,
                    'type': node.type,
                    'size': node.size,
                    'color': node.color,
                    'metadata': node.metadata
                }
                for node in graph.nodes
            ],
            'links': [
                {
                    'source': edge.source,
                    'target': edge.target,
                    'weight': edge.weight,
                    'type': edge.type,
                    'metadata': edge.metadata
                }
                for edge in graph.edges
            ],
            'metadata': graph.metadata
        }
        
        return d3_data
    
    def export_for_chartjs(self, graph: RelationshipGraph) -> Dict[str, Any]:
        """導出為 Chart.js 格式（用於簡單圖表）"""
        # 統計各類型節點數量
        node_type_counts = Counter(node.type for node in graph.nodes)
        
        # 統計邊類型
        edge_type_counts = Counter(edge.type for edge in graph.edges)
        
        chartjs_data = {
            'node_distribution': {
                'labels': list(node_type_counts.keys()),
                'data': list(node_type_counts.values()),
                'backgroundColor': [self.color_map.get(t, '#666666') for t in node_type_counts.keys()]
            },
            'edge_distribution': {
                'labels': list(edge_type_counts.keys()),
                'data': list(edge_type_counts.values())
            },
            'category_stats': graph.metadata.get('category_analysis', {}).get('sessions', {}),
            'keyword_importance': graph.metadata.get('keyword_analysis', {}).get('importance', {})
        }
        
        return chartjs_data


def create_hugo_chart_shortcodes(site_path: str, graph: RelationshipGraph):
    """創建 Hugo 圖表 shortcodes"""
    from pathlib import Path
    
    analyzer = HugoRelationshipAnalyzer()
    d3_data = analyzer.export_for_d3js(graph)
    chartjs_data = analyzer.export_for_chartjs(graph)
    
    # 創建 D3.js 關聯圖 shortcode
    d3_shortcode = f'''<div id="relationshipNetwork" class="w-full h-96 border border-gray-200 rounded-lg"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {{
    const data = {json.dumps(d3_data, ensure_ascii=False, indent=2)};
    
    const width = document.getElementById('relationshipNetwork').offsetWidth;
    const height = 400;
    
    const svg = d3.select('#relationshipNetwork')
        .append('svg')
        .attr('width', width)
        .attr('height', height);
    
    const simulation = d3.forceSimulation(data.nodes)
        .force('link', d3.forceLink(data.links).id(d => d.id).distance(100))
        .force('charge', d3.forceManyBody().strength(-300))
        .force('center', d3.forceCenter(width / 2, height / 2));
    
    const link = svg.append('g')
        .selectAll('line')
        .data(data.links)
        .enter().append('line')
        .attr('stroke', '#999')
        .attr('stroke-opacity', 0.6)
        .attr('stroke-width', d => Math.sqrt(d.weight * 5));
    
    const node = svg.append('g')
        .selectAll('circle')
        .data(data.nodes)
        .enter().append('circle')
        .attr('r', d => Math.sqrt(d.size) * 3 + 5)
        .attr('fill', d => d.color)
        .call(d3.drag()
            .on('start', dragstarted)
            .on('drag', dragged)
            .on('end', dragended));
    
    node.append('title')
        .text(d => d.name);
    
    simulation.on('tick', () => {{
        link
            .attr('x1', d => d.source.x)
            .attr('y1', d => d.source.y)
            .attr('x2', d => d.target.x)
            .attr('y2', d => d.target.y);
        
        node
            .attr('cx', d => d.x)
            .attr('cy', d => d.y);
    }});
    
    function dragstarted(event, d) {{
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }}
    
    function dragged(event, d) {{
        d.fx = event.x;
        d.fy = event.y;
    }}
    
    function dragended(event, d) {{
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }}
}});
</script>'''
    
    # 創建關鍵詞重要性圖表 shortcode
    keyword_chart_shortcode = f'''<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div>
        <h3 class="text-lg font-semibold mb-4">關鍵詞重要性</h3>
        <canvas id="keywordChart" width="400" height="300"></canvas>
    </div>
    <div>
        <h3 class="text-lg font-semibold mb-4">節點類型分布</h3>
        <canvas id="nodeTypeChart" width="400" height="300"></canvas>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {{
    const chartData = {json.dumps(chartjs_data, ensure_ascii=False, indent=2)};
    
    // 關鍵詞重要性圖表
    const keywordCtx = document.getElementById('keywordChart').getContext('2d');
    const topKeywords = Object.entries(chartData.keyword_importance)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10);
    
    new Chart(keywordCtx, {{
        type: 'bar',
        data: {{
            labels: topKeywords.map(([kw, _]) => kw),
            datasets: [{{
                label: '出現次數',
                data: topKeywords.map(([_, count]) => count),
                backgroundColor: '#F59E0B'
            }}]
        }},
        options: {{
            responsive: true,
            scales: {{
                y: {{
                    beginAtZero: true
                }}
            }}
        }}
    }});
    
    // 節點類型分布圖表
    const nodeTypeCtx = document.getElementById('nodeTypeChart').getContext('2d');
    new Chart(nodeTypeCtx, {{
        type: 'doughnut',
        data: chartData.node_distribution,
        options: {{
            responsive: true,
            plugins: {{
                legend: {{
                    position: 'bottom'
                }}
            }}
        }}
    }});
}});
</script>'''
    
    # 寫入 shortcode 文件
    shortcodes_dir = Path(site_path) / "layouts" / "shortcodes"
    shortcodes_dir.mkdir(parents=True, exist_ok=True)
    
    with open(shortcodes_dir / "relationship-network.html", 'w', encoding='utf-8') as f:
        f.write(d3_shortcode)
    
    with open(shortcodes_dir / "keyword-charts.html", 'w', encoding='utf-8') as f:
        f.write(keyword_chart_shortcode)


def main():
    """主函數 - 測試關聯圖表功能"""
    from scripts.hugo_data_extractor import HugoDataExtractor
    
    print("測試關聯圖表分析...")
    
    # 提取測試資料
    extractor = HugoDataExtractor()
    dataset = extractor.extract_complete_dataset(limit=20)
    
    if not dataset.sessions:
        print("沒有找到測試資料")
        return
    
    # 分析關聯性
    analyzer = HugoRelationshipAnalyzer()
    graph = analyzer.build_relationship_graph(dataset)
    
    print(f"關聯圖統計:")
    print(f"  - 節點數量: {len(graph.nodes)}")
    print(f"  - 邊數量: {len(graph.edges)}")
    
    # 導出數據
    d3_data = analyzer.export_for_d3js(graph)
    chartjs_data = analyzer.export_for_chartjs(graph)
    
    print(f"  - D3.js 數據大小: {len(json.dumps(d3_data))} 字符")
    print(f"  - Chart.js 數據: {list(chartjs_data.keys())}")


if __name__ == "__main__":
    main()
