#!/usr/bin/env python3
"""
Hugo 會議報告系統 - 網站結構建構器
創建 Hugo 網站目錄結構、配置文件和模板
"""

import os
import sys
import yaml
import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import asdict

# 添加專案根目錄到 Python 路徑
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from scripts.hugo_data_extractor import ConferenceSession, ConferenceData
from scripts.hugo_content_generator import GeneratedContent


class HugoSiteBuilder:
    """Hugo 網站建構器"""
    
    def __init__(self, site_path: str = "hugo-conference-site"):
        """初始化建構器"""
        self.site_path = Path(site_path)
        self.content_path = self.site_path / "content"
        self.layouts_path = self.site_path / "layouts"
        self.static_path = self.site_path / "static"
        self.data_path = self.site_path / "data"
        self.config_path = self.site_path / "config"
        self.assets_path = self.site_path / "assets"
    
    def create_directory_structure(self):
        """創建 Hugo 目錄結構"""
        print("創建 Hugo 目錄結構...")
        
        # 主要目錄
        directories = [
            self.site_path,
            self.content_path,
            self.content_path / "conferences",
            self.content_path / "about",
            self.layouts_path,
            self.layouts_path / "_default",
            self.layouts_path / "conferences",
            self.layouts_path / "partials",
            self.layouts_path / "shortcodes",
            self.static_path,
            self.static_path / "css",
            self.static_path / "js",
            self.static_path / "images",
            self.static_path / "charts",
            self.data_path,
            self.config_path,
            self.config_path / "_default",
            self.config_path / "production",
            self.config_path / "development",
            self.assets_path,
            self.assets_path / "scss",
            self.assets_path / "js"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        print(f"目錄結構已創建在: {self.site_path}")
    
    def create_config_files(self):
        """創建 Hugo 配置文件"""
        print("創建 Hugo 配置文件...")
        
        # 主配置文件
        main_config = {
            'baseURL': 'https://your-domain.com',
            'languageCode': 'zh-tw',
            'title': 'TrendScope 會議報告',
            'theme': 'conference-theme',
            'defaultContentLanguage': 'zh',
            'enableRobotsTXT': True,
            'enableGitInfo': True,
            'enableEmoji': True,
            'hasCJKLanguage': True,
            'paginate': 20,
            'summaryLength': 100,
            'params': {
                'description': '技術會議報告與分析平台',
                'author': 'TrendScope',
                'keywords': ['技術會議', '報告', '分析', 'AI', '科技'],
                'social': {
                    'github': 'https://github.com/your-repo',
                    'email': '<EMAIL>'
                },
                'analytics': {
                    'google': 'G-XXXXXXXXXX'
                }
            },
            'markup': {
                'goldmark': {
                    'renderer': {
                        'unsafe': True
                    }
                },
                'highlight': {
                    'style': 'github',
                    'lineNos': True,
                    'codeFences': True
                }
            },
            'taxonomies': {
                'tag': 'tags',
                'category': 'categories',
                'seminar': 'seminars'
            }
        }
        
        # 語言配置
        languages_config = {
            'zh': {
                'languageName': '中文',
                'weight': 1,
                'title': 'TrendScope 會議報告',
                'params': {
                    'description': '技術會議報告與分析平台'
                }
            },
            'en': {
                'languageName': 'English',
                'weight': 2,
                'title': 'TrendScope Conference Reports',
                'params': {
                    'description': 'Technical Conference Reports and Analysis Platform'
                }
            }
        }
        
        # 選單配置
        menus_config = {
            'main': [
                {
                    'name': '首頁',
                    'url': '/',
                    'weight': 10
                },
                {
                    'name': '會議報告',
                    'url': '/conferences/',
                    'weight': 20
                },
                {
                    'name': '關於',
                    'url': '/about/',
                    'weight': 30
                }
            ]
        }
        
        # 寫入配置文件
        with open(self.config_path / "_default" / "config.yaml", 'w', encoding='utf-8') as f:
            yaml.dump(main_config, f, default_flow_style=False, allow_unicode=True)
        
        with open(self.config_path / "_default" / "languages.yaml", 'w', encoding='utf-8') as f:
            yaml.dump({'languages': languages_config}, f, default_flow_style=False, allow_unicode=True)
        
        with open(self.config_path / "_default" / "menus.yaml", 'w', encoding='utf-8') as f:
            yaml.dump({'menus': menus_config}, f, default_flow_style=False, allow_unicode=True)
    
    def create_base_templates(self):
        """創建基礎模板"""
        print("創建 Hugo 模板...")
        
        # 基礎模板
        baseof_template = '''<!DOCTYPE html>
<html lang="{{ .Site.Language.Lang }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ if .Title }}{{ .Title }} - {{ end }}{{ .Site.Title }}</title>
    <meta name="description" content="{{ .Description | default .Site.Params.description }}">
    <meta name="keywords" content="{{ delimit .Keywords "," }}">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ "css/custom.css" | relURL }}">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body class="bg-gray-50">
    {{ partial "header.html" . }}
    
    <main class="container mx-auto px-4 py-8">
        {{ block "main" . }}{{ end }}
    </main>
    
    {{ partial "footer.html" . }}
    
    <!-- JavaScript -->
    <script src="{{ "js/main.js" | relURL }}"></script>
</body>
</html>'''
        
        # 列表頁模板
        list_template = '''{{ define "main" }}
<div class="max-w-6xl mx-auto">
    <header class="mb-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ .Title }}</h1>
        {{ if .Content }}
        <div class="prose max-w-none">
            {{ .Content }}
        </div>
        {{ end }}
    </header>
    
    {{ if .Data.Pages }}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {{ range .Data.Pages }}
        <article class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <h2 class="text-xl font-semibold mb-3">
                <a href="{{ .RelPermalink }}" class="text-blue-600 hover:text-blue-800">
                    {{ .Title }}
                </a>
            </h2>
            
            {{ if .Params.summary }}
            <p class="text-gray-600 mb-4">{{ .Params.summary }}</p>
            {{ end }}
            
            <div class="flex flex-wrap gap-2 mb-4">
                {{ range .Params.keywords }}
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">{{ . }}</span>
                {{ end }}
            </div>
            
            <div class="text-sm text-gray-500">
                <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "2006-01-02" }}</time>
                {{ if .Params.seminar }}
                <span class="ml-2">{{ .Params.seminar }}</span>
                {{ end }}
            </div>
        </article>
        {{ end }}
    </div>
    {{ end }}
</div>
{{ end }}'''
        
        # 單頁模板
        single_template = '''{{ define "main" }}
<article class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
    <header class="mb-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ .Title }}</h1>
        
        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
            <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "2006-01-02" }}</time>
            {{ if .Params.seminar }}
            <span class="bg-gray-100 px-2 py-1 rounded">{{ .Params.seminar }}</span>
            {{ end }}
            {{ if .Params.category }}
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">{{ .Params.category }}</span>
            {{ end }}
        </div>
        
        {{ if .Params.keywords }}
        <div class="flex flex-wrap gap-2 mb-6">
            {{ range .Params.keywords }}
            <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full">{{ . }}</span>
            {{ end }}
        </div>
        {{ end }}
    </header>
    
    <div class="prose max-w-none">
        {{ .Content }}
    </div>
    
    {{ if or .Params.url .Params.pdf_url }}
    <footer class="mt-8 pt-6 border-t border-gray-200">
        <h3 class="text-lg font-semibold mb-4">相關資源</h3>
        <div class="flex gap-4">
            {{ if .Params.url }}
            <a href="{{ .Params.url }}" target="_blank" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                查看會議
            </a>
            {{ end }}
            {{ if .Params.pdf_url }}
            <a href="{{ .Params.pdf_url }}" target="_blank" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                下載簡報
            </a>
            {{ end }}
        </div>
    </footer>
    {{ end }}
</article>
{{ end }}'''
        
        # 寫入模板文件
        with open(self.layouts_path / "_default" / "baseof.html", 'w', encoding='utf-8') as f:
            f.write(baseof_template)
        
        with open(self.layouts_path / "_default" / "list.html", 'w', encoding='utf-8') as f:
            f.write(list_template)
        
        with open(self.layouts_path / "_default" / "single.html", 'w', encoding='utf-8') as f:
            f.write(single_template)
    
    def create_partial_templates(self):
        """創建部分模板"""
        # Header 模板
        header_template = '''<header class="bg-white shadow-sm">
    <nav class="container mx-auto px-4 py-4">
        <div class="flex justify-between items-center">
            <a href="{{ .Site.BaseURL }}" class="text-2xl font-bold text-blue-600">
                {{ .Site.Title }}
            </a>
            
            <ul class="flex space-x-6">
                {{ range .Site.Menus.main }}
                <li>
                    <a href="{{ .URL }}" class="text-gray-700 hover:text-blue-600 transition-colors">
                        {{ .Name }}
                    </a>
                </li>
                {{ end }}
            </ul>
            
            <!-- 語言切換 -->
            <div class="flex space-x-2">
                {{ range .Site.Languages }}
                <a href="{{ .BaseURL }}" class="text-sm text-gray-600 hover:text-blue-600">
                    {{ .LanguageName }}
                </a>
                {{ end }}
            </div>
        </div>
    </nav>
</header>'''
        
        # Footer 模板
        footer_template = '''<footer class="bg-gray-800 text-white mt-16">
    <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
                <h3 class="text-lg font-semibold mb-4">{{ .Site.Title }}</h3>
                <p class="text-gray-300">{{ .Site.Params.description }}</p>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold mb-4">快速連結</h3>
                <ul class="space-y-2">
                    {{ range .Site.Menus.main }}
                    <li>
                        <a href="{{ .URL }}" class="text-gray-300 hover:text-white">{{ .Name }}</a>
                    </li>
                    {{ end }}
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold mb-4">聯絡我們</h3>
                {{ if .Site.Params.social.email }}
                <p class="text-gray-300">{{ .Site.Params.social.email }}</p>
                {{ end }}
            </div>
        </div>
        
        <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
            <p>&copy; {{ now.Year }} {{ .Site.Title }}. All rights reserved.</p>
        </div>
    </div>
</footer>'''
        
        # 寫入部分模板
        with open(self.layouts_path / "partials" / "header.html", 'w', encoding='utf-8') as f:
            f.write(header_template)
        
        with open(self.layouts_path / "partials" / "footer.html", 'w', encoding='utf-8') as f:
            f.write(footer_template)
    
    def create_content_files(self, dataset: ConferenceData, generated_contents: Dict[str, GeneratedContent]):
        """創建內容文件"""
        print("創建 Hugo 內容文件...")

        # 創建中文首頁內容
        homepage_content_zh = f'''---
title: "TrendScope 會議報告"
date: {dataset.statistics.get("last_updated", "")}
draft: false
---

# 歡迎來到 TrendScope 會議報告平台

## 統計概覽

- **總會議數**: {dataset.statistics.get("total_sessions", 0)}
- **研討會數**: {dataset.statistics.get("total_seminars", 0)}
- **分類數**: {dataset.statistics.get("total_categories", 0)}

## 最新會議報告

{{{{< recent-sessions >}}}}

## 熱門分類

{{{{< category-chart >}}}}

## 關聯圖表

{{{{< relationship-network >}}}}
'''

        # 創建英文首頁內容
        homepage_content_en = f'''---
title: "TrendScope Conference Reports"
date: {dataset.statistics.get("last_updated", "")}
draft: false
---

# Welcome to TrendScope Conference Reports Platform

## Statistics Overview

- **Total Sessions**: {dataset.statistics.get("total_sessions", 0)}
- **Total Seminars**: {dataset.statistics.get("total_seminars", 0)}
- **Total Categories**: {dataset.statistics.get("total_categories", 0)}

## Latest Conference Reports

{{{{< recent-sessions >}}}}

## Popular Categories

{{{{< category-chart >}}}}

## Relationship Network

{{{{< relationship-network >}}}}
'''

        with open(self.content_path / "_index.md", 'w', encoding='utf-8') as f:
            f.write(homepage_content_zh)

        with open(self.content_path / "_index.en.md", 'w', encoding='utf-8') as f:
            f.write(homepage_content_en)
        
        # 為每個分類創建目錄和內容
        for category, session_ids in dataset.categories.items():
            category_path = self.content_path / "conferences" / category
            category_path.mkdir(parents=True, exist_ok=True)

            # 創建中文分類首頁
            category_content_zh = f'''---
title: "{category.replace('-', ' ').title()}"
date: {dataset.statistics.get("last_updated", "")}
draft: false
type: "conferences"
---

# {category.replace('-', ' ').title()}

本分類包含 {len(session_ids)} 個會議。

{{{{< keyword-charts >}}}}
'''

            # 創建英文分類首頁
            category_content_en = f'''---
title: "{category.replace('-', ' ').title()}"
date: {dataset.statistics.get("last_updated", "")}
draft: false
type: "conferences"
---

# {category.replace('-', ' ').title()}

This category contains {len(session_ids)} sessions.

{{{{< keyword-charts >}}}}
'''

            with open(category_path / "_index.md", 'w', encoding='utf-8') as f:
                f.write(category_content_zh)

            with open(category_path / "_index.en.md", 'w', encoding='utf-8') as f:
                f.write(category_content_en)
        
        # 創建會議內容文件
        for session in dataset.sessions:
            if session.conference_id in generated_contents:
                content = generated_contents[session.conference_id]

                # 確保分類目錄存在
                category_path = self.content_path / "conferences" / session.category
                category_path.mkdir(parents=True, exist_ok=True)

                # 創建中文會議文件
                filename = f"{session.slug}.md"
                file_path = category_path / filename

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content.markdown_content)

                # 創建英文會議文件（如果有翻譯）
                if content.english_translation:
                    filename_en = f"{session.slug}.en.md"
                    file_path_en = category_path / filename_en

                    with open(file_path_en, 'w', encoding='utf-8') as f:
                        f.write(content.english_translation)

        print(f"已創建 {len(dataset.sessions)} 個會議內容文件（包含多語言版本）")
    
    def build_complete_site(self, dataset: ConferenceData, generated_contents: Dict[str, GeneratedContent]):
        """建構完整的 Hugo 網站"""
        print("開始建構完整的 Hugo 網站...")
        
        # 創建目錄結構
        self.create_directory_structure()
        
        # 創建配置文件
        self.create_config_files()
        
        # 創建模板
        self.create_base_templates()
        self.create_partial_templates()
        
        # 創建內容文件
        self.create_content_files(dataset, generated_contents)
        
        print(f"Hugo 網站建構完成: {self.site_path}")
        print("下一步:")
        print(f"1. cd {self.site_path}")
        print("2. hugo server -D")
        print("3. 訪問 http://localhost:1313")


def main():
    """主函數 - 測試網站建構功能"""
    from scripts.hugo_data_extractor import HugoDataExtractor
    from scripts.hugo_content_generator import HugoContentGenerator
    
    print("測試 Hugo 網站建構...")
    
    # 創建測試資料
    builder = HugoSiteBuilder("test-hugo-site")
    builder.create_directory_structure()
    builder.create_config_files()
    builder.create_base_templates()
    builder.create_partial_templates()
    
    print("測試網站結構已創建")


if __name__ == "__main__":
    main()
