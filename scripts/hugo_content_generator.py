#!/usr/bin/env python3
"""
Hugo 會議報告系統 - LLM 內容生成模組
使用 Gemini API 生成結構化的 Markdown 內容
"""

import os
import sys
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

# 載入 .env 文件
def load_env_file():
    """載入 .env 文件中的環境變數"""
    env_path = Path(project_root) / '.env'
    if env_path.exists():
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

# 載入環境變數
load_env_file()

try:
    import google.generativeai as genai
except ImportError:
    print("請安裝 google-generativeai: pip install google-generativeai")
    sys.exit(1)

from scripts.hugo_data_extractor import ConferenceSession, ConferenceData


@dataclass
class GeneratedContent:
    """生成的內容結構"""
    summary: str
    keywords: List[str]
    category: str
    markdown_content: str
    english_translation: Optional[str] = None


class HugoContentGenerator:
    """Hugo 內容生成器"""
    
    def __init__(self, api_key: Optional[str] = None):
        """初始化內容生成器"""
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("請設置 GEMINI_API_KEY 環境變數")
        
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-pro')
    
    def generate_session_summary(self, session: ConferenceSession) -> str:
        """生成會議摘要"""
        prompt = f"""
請為以下會議生成一個簡潔的摘要（100-150字）：

會議名稱：{session.name}
研討會：{session.seminar}
描述：{session.description}

要求：
1. 突出會議的核心主題和價值
2. 使用專業但易懂的語言
3. 保持客觀和準確
4. 字數控制在100-150字之間

摘要：
"""
        
        try:
            response = self.model.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            print(f"生成摘要時發生錯誤: {e}")
            return session.description[:150] + "..." if len(session.description) > 150 else session.description
    
    def extract_keywords(self, session: ConferenceSession) -> List[str]:
        """提取關鍵詞"""
        prompt = f"""
請從以下會議資訊中提取5-8個最重要的關鍵詞：

會議名稱：{session.name}
描述：{session.description}
現有標籤：{', '.join(session.tags) if session.tags else '無'}

要求：
1. 關鍵詞應該是技術術語、概念或主題
2. 優先選擇具有搜索價值的詞彙
3. 包含中英文技術術語
4. 每個關鍵詞用逗號分隔
5. 只返回關鍵詞列表，不要其他文字

關鍵詞：
"""
        
        try:
            response = self.model.generate_content(prompt)
            keywords_text = response.text.strip()
            # 解析關鍵詞
            keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
            return keywords[:8]  # 限制最多8個關鍵詞
        except Exception as e:
            print(f"提取關鍵詞時發生錯誤: {e}")
            return session.tags[:5] if session.tags else []
    
    def categorize_session(self, session: ConferenceSession) -> str:
        """智能分類會議"""
        prompt = f"""
請將以下會議分類到最合適的類別中：

會議名稱：{session.name}
描述：{session.description}

可選類別：
- ai-tech: 人工智能、機器學習、深度學習
- fintech: 金融科技、區塊鏈、數字貨幣
- cloud-computing: 雲端計算、容器化、微服務
- data-science: 數據科學、大數據、數據分析
- web-development: 前端開發、後端開發、全棧開發
- mobile-development: 移動開發、iOS、Android
- devops: DevOps、CI/CD、自動化部署
- cybersecurity: 網絡安全、信息安全
- iot: 物聯網、嵌入式系統
- general: 其他技術主題

只返回最合適的類別名稱（英文），不要其他文字：
"""
        
        try:
            response = self.model.generate_content(prompt)
            category = response.text.strip().lower()
            # 驗證類別是否有效
            valid_categories = [
                'ai-tech', 'fintech', 'cloud-computing', 'data-science',
                'web-development', 'mobile-development', 'devops',
                'cybersecurity', 'iot', 'general'
            ]
            return category if category in valid_categories else 'general'
        except Exception as e:
            print(f"分類會議時發生錯誤: {e}")
            return session.category or 'general'
    
    def generate_markdown_content(self, session: ConferenceSession, summary: str, keywords: List[str]) -> str:
        """生成 Hugo Markdown 內容"""
        # 生成 Front Matter
        front_matter = {
            'title': session.name,
            'date': session.created_at,
            'draft': False,
            'seminar': session.seminar,
            'category': session.category,
            'tags': session.tags,
            'keywords': keywords,
            'external_url': session.url,  # 避免與 Hugo 內建 url 字段衝突
            'pdf_url': session.pdf_url,
            'summary': summary,
            'conference_id': session.conference_id
        }
        
        # 生成 YAML Front Matter
        yaml_front_matter = "---\n"
        for key, value in front_matter.items():
            if isinstance(value, list):
                if value:  # 只有非空列表才添加
                    yaml_front_matter += f"{key}:\n"
                    for item in value:
                        yaml_front_matter += f"  - \"{item}\"\n"
            elif value:  # 只有非空值才添加
                yaml_front_matter += f"{key}: \"{value}\"\n"
        yaml_front_matter += "---\n\n"
        
        # 生成 Markdown 內容
        content = f"""## 會議概述

{summary}

## 會議描述

{session.description}

## 相關資源

"""
        
        if session.url:
            content += f"- [會議連結]({session.url})\n"
        if session.pdf_url:
            content += f"- [簡報下載]({session.pdf_url})\n"
        
        if session.ppt_context:
            content += f"""
## 簡報內容

{session.ppt_context[:1000]}...

"""
        
        content += f"""
## 關鍵詞

{', '.join(keywords)}

---

*本內容由 TrendScope 系統自動生成*
"""
        
        return yaml_front_matter + content
    
    def translate_to_english(self, chinese_content: str) -> str:
        """翻譯內容為英文"""
        prompt = f"""
請將以下中文技術會議內容翻譯為英文，保持專業性和準確性：

{chinese_content}

要求：
1. 保持技術術語的準確性
2. 使用專業的英文表達
3. 保持原文的結構和格式
4. 確保翻譯自然流暢

英文翻譯：
"""

        try:
            response = self.model.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            print(f"翻譯內容時發生錯誤: {e}")
            return ""

    def generate_english_markdown(self, session: ConferenceSession, summary: str, keywords: List[str]) -> str:
        """生成英文版 Hugo Markdown 內容"""
        # 翻譯基本資訊
        english_title = self.translate_to_english(session.name)
        english_summary = self.translate_to_english(summary)
        english_description = self.translate_to_english(session.description)

        # 生成英文 Front Matter
        front_matter = {
            'title': english_title,
            'date': session.created_at,
            'draft': False,
            'seminar': session.seminar,  # 保持原文
            'category': session.category,
            'tags': session.tags,  # 保持原文標籤
            'keywords': keywords,
            'url': session.url,
            'pdf_url': session.pdf_url,
            'summary': english_summary,
            'conference_id': session.conference_id
        }

        # 生成 YAML Front Matter
        yaml_front_matter = "---\n"
        for key, value in front_matter.items():
            if isinstance(value, list):
                if value:
                    yaml_front_matter += f"{key}:\n"
                    for item in value:
                        yaml_front_matter += f"  - \"{item}\"\n"
            elif value:
                yaml_front_matter += f"{key}: \"{value}\"\n"
        yaml_front_matter += "---\n\n"

        # 生成英文 Markdown 內容
        content = f"""## Conference Overview

{english_summary}

## Conference Description

{english_description}

## Related Resources

"""

        if session.url:
            content += f"- [Conference Link]({session.url})\n"
        if session.pdf_url:
            content += f"- [Download Slides]({session.pdf_url})\n"

        if session.ppt_context:
            english_ppt_context = self.translate_to_english(session.ppt_context[:1000])
            content += f"""
## Presentation Content

{english_ppt_context}...

"""

        content += f"""
## Keywords

{', '.join(keywords)}

---

*This content is automatically generated by TrendScope system*
"""

        return yaml_front_matter + content
    
    def process_session(self, session: ConferenceSession, include_translation: bool = True) -> GeneratedContent:
        """處理單個會議，生成完整內容"""
        print(f"正在處理會議: {session.name}")
        
        # 生成摘要
        summary = self.generate_session_summary(session)
        
        # 提取關鍵詞
        keywords = self.extract_keywords(session)
        
        # 智能分類
        category = self.categorize_session(session)
        session.category = category  # 更新分類
        
        # 生成 Markdown 內容
        markdown_content = self.generate_markdown_content(session, summary, keywords)
        
        # 翻譯為英文（可選）
        english_translation = None
        if include_translation:
            english_translation = self.translate_to_english(markdown_content)
        
        return GeneratedContent(
            summary=summary,
            keywords=keywords,
            category=category,
            markdown_content=markdown_content,
            english_translation=english_translation
        )
    
    def process_dataset(self, dataset: ConferenceData, include_translation: bool = True) -> Dict[str, GeneratedContent]:
        """處理整個資料集"""
        print(f"開始處理 {len(dataset.sessions)} 個會議...")
        
        generated_contents = {}
        
        for i, session in enumerate(dataset.sessions, 1):
            print(f"進度: {i}/{len(dataset.sessions)}")
            
            try:
                content = self.process_session(session, include_translation)
                generated_contents[session.conference_id] = content
            except Exception as e:
                print(f"處理會議 {session.name} 時發生錯誤: {e}")
                continue
        
        print(f"完成處理，成功生成 {len(generated_contents)} 個內容")
        return generated_contents


def main():
    """主函數 - 測試內容生成功能"""
    # 檢查 API Key
    if not os.getenv('GEMINI_API_KEY'):
        print("請設置 GEMINI_API_KEY 環境變數")
        return
    
    # 創建測試會議資料
    test_session = ConferenceSession(
        conference_id="test-001",
        name="AI 驅動的智能客服系統設計與實踐",
        seminar="2025 AI 技術大會",
        description="本次演講將深入探討如何設計和實現一個高效的 AI 驅動智能客服系統，包括自然語言處理、意圖識別、知識圖譜構建等關鍵技術。",
        url="https://example.com/session",
        pdf_url="https://example.com/slides.pdf",
        ppt_context="介紹了 AI 客服系統的架構設計...",
        tags=["AI", "NLP", "客服系統"],
        created_at=datetime.now().isoformat()
    )
    
    # 測試內容生成
    generator = HugoContentGenerator()
    
    print("測試內容生成...")
    content = generator.process_session(test_session, include_translation=False)
    
    print("\n生成的內容:")
    print(f"摘要: {content.summary}")
    print(f"關鍵詞: {content.keywords}")
    print(f"分類: {content.category}")
    print(f"Markdown 內容長度: {len(content.markdown_content)} 字符")


if __name__ == "__main__":
    main()
