#!/usr/bin/env python3
"""
Hugo 會議報告系統 - BigQuery 資料提取模組
從 BigQuery 提取會議資料並準備用於 Hugo 靜態網站生成
"""

import os
import sys
import json
import yaml
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict

# 添加專案根目錄到 Python 路徑
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from backend.bigquery.client import BigQueryClient


@dataclass
class ConferenceSession:
    """會議資料結構"""
    conference_id: str
    name: str
    seminar: str
    description: str
    url: str
    pdf_url: str
    ppt_context: Optional[str]
    tags: List[str]
    created_at: str
    category: str = ""
    slug: str = ""
    summary: str = ""
    keywords: List[str] = None
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        # 生成 URL slug
        if not self.slug:
            self.slug = self._generate_slug()
        # 生成分類
        if not self.category:
            self.category = self._generate_category()
    
    def _generate_slug(self) -> str:
        """生成 URL slug"""
        import re
        # 移除特殊字符，保留中英文和數字
        slug = re.sub(r'[^\w\s-]', '', self.name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug[:50]  # 限制長度
    
    def _generate_category(self) -> str:
        """根據會議名稱和標籤生成分類"""
        # 簡單的分類邏輯，可以後續優化
        name_lower = self.name.lower()
        if any(keyword in name_lower for keyword in ['ai', 'artificial intelligence', '人工智能', '機器學習']):
            return 'ai-tech'
        elif any(keyword in name_lower for keyword in ['fintech', '金融科技', '區塊鏈', 'blockchain']):
            return 'fintech'
        elif any(keyword in name_lower for keyword in ['cloud', '雲端', '雲計算']):
            return 'cloud-computing'
        elif any(keyword in name_lower for keyword in ['data', '數據', '大數據']):
            return 'data-science'
        else:
            return 'general'


@dataclass
class Speaker:
    """講者資料結構"""
    name: str
    title: str = ""
    company: str = ""
    bio: str = ""


@dataclass
class ConferenceData:
    """完整的會議資料集合"""
    sessions: List[ConferenceSession]
    speakers: List[Speaker]
    categories: Dict[str, List[str]]
    relationships: Dict[str, Any]
    statistics: Dict[str, Any]


class HugoDataExtractor:
    """Hugo 資料提取器"""
    
    def __init__(self, credentials_path: Optional[str] = None, project_id: Optional[str] = None):
        """初始化資料提取器"""
        self.bq_client = BigQueryClient(credentials_path, project_id)
        self.dataset_id = "conference_data"
        self.table_id = "sessions"
    
    def extract_all_sessions(self, limit: Optional[int] = None) -> List[ConferenceSession]:
        """提取所有會議資料"""
        try:
            project_id = self.bq_client.project_id
            
            # 構建查詢
            limit_clause = f"LIMIT {limit}" if limit else ""
            query = f"""
            SELECT 
                conference_id,
                name,
                seminar,
                description,
                url,
                pdf_url,
                ppt_context,
                tags,
                created_at
            FROM `{project_id}.{self.dataset_id}.{self.table_id}`
            WHERE name IS NOT NULL AND name != ''
            ORDER BY created_at DESC
            {limit_clause}
            """
            
            results = self.bq_client.query(query)
            sessions = []
            
            for row in results:
                session = ConferenceSession(
                    conference_id=row['conference_id'],
                    name=row['name'],
                    seminar=row['seminar'],
                    description=row['description'] or '',
                    url=row['url'] or '',
                    pdf_url=row['pdf_url'] or '',
                    ppt_context=row['ppt_context'],
                    tags=row['tags'] or [],
                    created_at=row['created_at'].isoformat() if hasattr(row['created_at'], 'isoformat') else str(row['created_at'])
                )
                sessions.append(session)
            
            print(f"成功提取 {len(sessions)} 個會議資料")
            return sessions
            
        except Exception as e:
            print(f"提取會議資料時發生錯誤: {e}")
            return []
    
    def extract_sessions_by_seminar(self, seminar: str) -> List[ConferenceSession]:
        """根據研討會名稱提取會議資料"""
        try:
            project_id = self.bq_client.project_id
            query = f"""
            SELECT 
                conference_id,
                name,
                seminar,
                description,
                url,
                pdf_url,
                ppt_context,
                tags,
                created_at
            FROM `{project_id}.{self.dataset_id}.{self.table_id}`
            WHERE seminar = @seminar AND name IS NOT NULL AND name != ''
            ORDER BY name
            """
            
            job_config = self.bq_client.client.query_job_config()
            job_config.query_parameters = [
                self.bq_client.client.scalar_query_parameter("seminar", "STRING", seminar)
            ]
            
            results = self.bq_client.client.query(query, job_config=job_config)
            sessions = []
            
            for row in results:
                session = ConferenceSession(
                    conference_id=row['conference_id'],
                    name=row['name'],
                    seminar=row['seminar'],
                    description=row['description'] or '',
                    url=row['url'] or '',
                    pdf_url=row['pdf_url'] or '',
                    ppt_context=row['ppt_context'],
                    tags=row['tags'] or [],
                    created_at=row['created_at'].isoformat() if hasattr(row['created_at'], 'isoformat') else str(row['created_at'])
                )
                sessions.append(session)
            
            return sessions
            
        except Exception as e:
            print(f"提取研討會 {seminar} 資料時發生錯誤: {e}")
            return []
    
    def get_seminars_list(self) -> List[Dict[str, Any]]:
        """獲取所有研討會列表"""
        try:
            project_id = self.bq_client.project_id
            query = f"""
            SELECT 
                seminar,
                COUNT(*) as session_count,
                MIN(created_at) as first_session,
                MAX(created_at) as last_session
            FROM `{project_id}.{self.dataset_id}.{self.table_id}`
            WHERE seminar IS NOT NULL AND name IS NOT NULL AND name != ''
            GROUP BY seminar
            ORDER BY session_count DESC
            """
            
            results = self.bq_client.query(query)
            seminars = []
            
            for row in results:
                seminar_info = {
                    'name': row['seminar'],
                    'session_count': row['session_count'],
                    'first_session': row['first_session'].isoformat() if hasattr(row['first_session'], 'isoformat') else str(row['first_session']),
                    'last_session': row['last_session'].isoformat() if hasattr(row['last_session'], 'isoformat') else str(row['last_session'])
                }
                seminars.append(seminar_info)
            
            return seminars
            
        except Exception as e:
            print(f"獲取研討會列表時發生錯誤: {e}")
            return []
    
    def generate_categories_mapping(self, sessions: List[ConferenceSession]) -> Dict[str, List[str]]:
        """生成分類映射"""
        categories = defaultdict(list)
        
        for session in sessions:
            categories[session.category].append(session.conference_id)
        
        return dict(categories)
    
    def generate_statistics(self, sessions: List[ConferenceSession]) -> Dict[str, Any]:
        """生成統計資料"""
        if not sessions:
            return {}
        
        # 基本統計
        total_sessions = len(sessions)
        seminars = set(session.seminar for session in sessions)
        categories = set(session.category for session in sessions)
        
        # 按分類統計
        category_stats = defaultdict(int)
        for session in sessions:
            category_stats[session.category] += 1
        
        # 按研討會統計
        seminar_stats = defaultdict(int)
        for session in sessions:
            seminar_stats[session.seminar] += 1
        
        return {
            'total_sessions': total_sessions,
            'total_seminars': len(seminars),
            'total_categories': len(categories),
            'category_distribution': dict(category_stats),
            'seminar_distribution': dict(seminar_stats),
            'last_updated': datetime.now().isoformat()
        }
    
    def extract_complete_dataset(self, limit: Optional[int] = None) -> ConferenceData:
        """提取完整的資料集"""
        print("開始提取完整的會議資料集...")
        
        # 提取會議資料
        sessions = self.extract_all_sessions(limit)
        
        # 生成分類映射
        categories = self.generate_categories_mapping(sessions)
        
        # 生成統計資料
        statistics = self.generate_statistics(sessions)
        
        # TODO: 提取講者資料和關聯關係
        speakers = []  # 暫時為空，後續實作
        relationships = {}  # 暫時為空，後續實作
        
        return ConferenceData(
            sessions=sessions,
            speakers=speakers,
            categories=categories,
            relationships=relationships,
            statistics=statistics
        )


def main():
    """主函數 - 測試資料提取功能"""
    extractor = HugoDataExtractor()
    
    # 測試提取資料
    print("測試 BigQuery 資料提取...")
    
    # 獲取研討會列表
    seminars = extractor.get_seminars_list()
    print(f"找到 {len(seminars)} 個研討會:")
    for seminar in seminars[:5]:  # 只顯示前5個
        print(f"  - {seminar['name']}: {seminar['session_count']} 個會議")
    
    # 提取完整資料集（限制10個用於測試）
    dataset = extractor.extract_complete_dataset(limit=10)
    print(f"\n提取的資料集統計:")
    print(f"  - 會議數量: {len(dataset.sessions)}")
    print(f"  - 分類數量: {len(dataset.categories)}")
    print(f"  - 統計資料: {dataset.statistics}")


if __name__ == "__main__":
    main()
