# AI 重构全球数字基建：从统一底座到多云协同的技术跃迁
主題演講
[會議影片連結](https://aicon.infoq.cn/2025/shanghai/presentation/6506)
AI 重構全球數字基建：從統一底座到多雲協同的技術躍遷

### 1. 核心觀點

本次演講深入探討了AI如何重塑全球數字基礎設施，其核心是從傳統的統一底座演進到多雲協同的技術躍遷。講者提出企業在數字化底座建設上面臨「自建難搞好，多雲難駕馭」的兩難困境，並闡述了「中立雲」作為全球多雲統一數字化底座的解決方案。該解決方案旨在透過軟硬體解耦，實現業務零改造遷移、高可用穩定性、成本效益與強大能力整合。演講強調了AI算力平台、自動化运维、全棧監控、大數據開發、數據庫管理、研發效能、機密管理、攻擊模擬、零信任終端安全等一系列創新平台的建設，並將AI能力深度嵌入其中，以實現降本增效、能力提升，特別是應對出海業務面臨的安全合規挑戰。

### 2. 詳細內容

簡報首先指出，企業自建數字底座常面臨品質差、投入大但TCO高的問題，而多雲雖能滿足出海、降本、穩定的需求，卻因適配與聯動困難而難以駕馭。「中立雲」應運而生，它能同時納管客戶多個數據中心與公有雲，形成全球一張網，實現業務零改造遷移、多雲多活高可用、降低成本並聚合所有公有雲與中立雲能力。實踐證明，中立雲顯著降低了运维人力成本，提升了業務穩定性與資訊安全性。

接著，演講詳細介紹了各個核心平台的建設與其AI賦能。

**AI算力平台**：解決了GPU資源利用率低（平均不到10%）的問題，透過細粒度虛擬化（vGPU）、固定/爭搶/混合模式、多種調度方法（裝箱/分散），顯著提升資源利用率。平台還具備故障快速發現與自癒、多租戶管理、精細化計量、推訓混部與動態擴縮容、以及跨雲統一調度本地與公有雲GPU的能力，充分利用Spot模式節省成本。

**自動化运维平台OpSpace**：旨在解決防火牆手工管理易錯、成本高、難追溯的問題，提供自動化開通、下發與在線化管理。同時，透過CMDB數據自動對賬，解決資產數據準確性不足導致的运维效率與資源分配問題，實現異常資源自動發現與可視化分析。

**全棧監控平台MonitorSpace**：整合硬體、網路、系統、應用、日誌、端側等多源監控數據，解決傳統監控能力分散、效率低下、成本高昂的痛點。核心在於「Monitor As Code」平台化能力，實現告警規則、數據採集、展示與解析的可編程。同時提供端到端全鏈路監控、慢SQL監控及巡檢平台，確保故障及時發現、智能定位。

**大數據開發平台DataMax**：解決大數據開發流程中數據集成、開發、运维環節的風險、成本與效率問題。平台透過DataOps理念實現測試、投產、运维全流程在線化可視化；透過FinOps在一個集群同時支持測試與生產環境，降低成本。在數據安全方面，提供細粒度權限管控、敏感數據自動識別與脫敏、安全風險監控與行為審計。數據治理模塊則強調元數據全面採集與血緣自動生成，以及透過結構化規則與自動化檢查提升數據品質。亮點是內嵌AI大模型，實現自然語言智能問數與圖表繪製。

**大數據引擎平台DataEngine與數據庫管理平台DBSpace/數據庫引擎平台DBEngine**：DataEngine提供開源大數據組件的部署、运维、監控與全球多雲管理。DBSpace解決DBA/開發/运维直連數據庫帶來的安全、穩定性與效率問題，提供白屏化在線查詢與變更，並強化安全管控（權限、脫敏、審計）、數據傳輸（同步、歸檔、訂閱）與數據校驗。特別地，DBSpace內嵌AI大模型，支援SQL生成、轉換、解釋與根因分析，並提供慢SQL智能診斷與優化。DBEngine則為數據庫提供自動安裝部署、高可用（自動/人工切換）、高可靠（自動備份恢復）與彈性擴縮容能力。

**研發效能平台DevSpace**：打造一站式全流程（需求→開發→測試→發布→運營）平台。AI大模型能力貫穿其中，輔助項目經理智能拆分用戶故事、分析迭代進度；協助研發管理平台進行AI代碼評審、日誌分析；提升測試平台的精準測試能力（用例推薦、覆蓋度量、影響分析）。度量平台也運用AI進行指標智能分析與報告自動生成。CloudIDE實現一鍵開發環境配置、代碼不落盤與環境共享。此外，還涵蓋了電控軟體研發的專業需求管理與測試。

**機密管理平台KeySpace**：針對開發/运维人員、團隊協作、個人數據在密碼管理上的痛點，提供統一管理、安全共享、自動更新、風險監控、審計日誌等能力，防範密碼洩露與濫用。

**攻擊模擬平台AttackSpace**：解決安全設備驗證、攻防演練、郵件釣魚與攻擊面管理中存在的盲點與效率問題。平台透過攻擊插件、自動化滲透測試、釣魚郵件發送等功能，實現安全防護有效性評估、應急響應驗證與攻擊面可視化管理。

**零信任終端安全ZeroSpace**：整合DLP、桌管、網路准入與零信任，解決傳統方案數據孤島、暴露面大、運營困難、管控不足與穩定性差等問題。提供基於API行為與流量特徵的數據防洩漏、只進不出原則、詳細審計記錄，並實現統一的終端管理與安全訪問。

最後，簡報重點提及**出海的安全合規建設**。美的集團秉持「八板斧」資訊安全體系，採行集團統一方案、全球各分支落地執行原則。強調SASE方案保障辦公安全，與海外CIO/法務聯合制定終端安全方案。建立全球兩級運營中心（總部+區域），構築縱深防禦。面對各國日益嚴苛的跨境數據合規要求（如GDPR），美的採納多項技術（數據本地存儲、分類分級、加密、嚴控跨境傳輸與遠程訪問、數據脫敏）與管理（隱私組織、標準合同、定期評估、法務協作）措施，以規避高額罰款與法律風險。

### 3. 重要結論

本次演講清晰呈現了美的集團在構建全球數字基礎設施方面的深遠願景與務實策略。透過「中立雲」的創新理念，成功地整合了企業自有數據中心與全球多個公有雲資源，有效解決了傳統數字底座建設的痛點。演講的核心亮點在於將AI能力深度融合到研發、运维、數據、安全等各個業務板塊的平台之中，不僅顯著提升了營運效率、降低了成本，更全面強化了系統的穩定性與安全性。特別是針對出海業務，其在跨境數據合規與全球資訊安全體系建設上的細緻規劃與多維度方案，展現了作為全球化企業的成熟與前瞻性。整體而言，美的的數字化實踐為AI重塑數字基建、實現多雲協同提供了一個全面且富有啟發性的範例。