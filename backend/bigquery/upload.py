"""
BigQuery 上傳模組
提供將爬蟲資料上傳到 BigQuery 的功能
"""
from datetime import datetime
import uuid
from google.cloud import bigquery

from backend.bigquery.client import BigQueryClient
from backend.bigquery.schemas.conferences import CONFERENCE_SCHEMA

class ConferenceUploader:
    """
    會議資料上傳器
    用於將爬蟲獲取的會議資料上傳到 BigQuery
    """
    
    def __init__(self, credentials_path=None, project_id=None):
        """
        初始化上傳器
        
        Args:
            credentials_path (str, optional): Google Cloud 服務帳戶憑證路徑
            project_id (str, optional): Google Cloud 項目 ID
        """
        self.bq_client = BigQueryClient(credentials_path, project_id)
        self.dataset_id = "conference_data"
        self.table_id = "sessions"
        
        # 確保資料集和資料表存在
        self.bq_client.create_dataset_if_not_exists(self.dataset_id)
        self.bq_client.create_table_if_not_exists(self.dataset_id, self.table_id, CONFERENCE_SCHEMA)
        
    def upload_sessions(self, sessions, source):
        """
        上傳會議資料到 BigQuery，使用MERGE語句處理重複數據
        
        Args:
            sessions (list): 會議資料列表，每個元素為一個字典
            source (str): 資料來源，例如 "AWS London Summit"
            
        Returns:
            bool: 上傳是否成功
        """
        if not sessions:
            print("沒有資料可上傳")
            return False
        
        # 轉換資料格式
        bq_data = []
        current_time = datetime.now().isoformat()
        
        for session in sessions:
            # 使用提供的ID或生成新ID
            session_id = session.get("conference_id", str(uuid.uuid4()))
            
            # 轉換為 BigQuery 格式
            bq_session = {
                "conference_id": session_id,
                "seminar": session.get("seminar", source),
                "name": session.get("name", session.get("會議名稱", "")),
                "description": session.get("description", session.get("描述", "")),
                "url": session.get("url", session.get("會議連結", "")),
                "pdf_url": session.get("pdf_url", session.get("PDF 連結", "")),
                "tags": session.get("tags", session.get("標籤", "").split(", ") if session.get("標籤") else []),
                "created_at": current_time,
            }
            
            # 處理講者資訊（如果有）
            if "講者" in session and session["講者"]:
                speakers_list = []
                for speaker in session["講者"].split(", "):
                    speakers_list.append({
                        "name": speaker,
                        "title": "",
                        "company": ""
                    })
                bq_session["speakers"] = speakers_list
            
            bq_data.append(bq_session)
        
        try:
            # 創建臨時表
            temp_table_id = f"temp_{uuid.uuid4().hex}"
            temp_table_ref = f"{self.bq_client.project_id}.{self.dataset_id}.{temp_table_id}"
            
            # 上傳數據到臨時表
            job_config = bigquery.LoadJobConfig(
                schema=CONFERENCE_SCHEMA,
                write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE
            )
            
            job = self.bq_client.client.load_table_from_json(
                bq_data, 
                temp_table_ref,
                job_config=job_config
            )
            job.result()  # 等待完成
            
            # 使用MERGE語句合併數據
            merge_query = f"""
            MERGE `{self.bq_client.project_id}.{self.dataset_id}.{self.table_id}` T
            USING `{temp_table_ref}` S
            ON T.conference_id = S.conference_id
            WHEN MATCHED THEN
              UPDATE SET
                name = S.name,
                description = COALESCE(S.description, T.description),
                url = COALESCE(S.url, T.url),
                pdf_url = COALESCE(S.pdf_url, T.pdf_url),
                tags = COALESCE(S.tags, T.tags)
            WHEN NOT MATCHED THEN
              INSERT (conference_id, seminar, name, description, url, pdf_url, tags, created_at)
              VALUES (
                S.conference_id,
                S.seminar,
                S.name,
                S.description,
                S.url,
                S.pdf_url,
                S.tags,
                S.created_at
              )
            """
            
            merge_job = self.bq_client.client.query(merge_query)
            merge_job.result()
            
            # 刪除臨時表
            self.bq_client.client.delete_table(temp_table_ref)
            
            print(f"成功處理 {len(bq_data)} 條會議資料到 BigQuery")
            return True
        
        except Exception as e:
            print(f"上傳到 BigQuery 失敗: {str(e)}")
            return False
