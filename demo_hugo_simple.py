#!/usr/bin/env python3
"""
Hugo 會議報告系統 - 簡化演示
不依賴 BigQuery，使用模擬資料演示完整流程
"""

import os
import sys
from datetime import datetime
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

from scripts.hugo_data_extractor import ConferenceSession, ConferenceData
from scripts.hugo_site_builder import HugoSiteBuilder
from scripts.hugo_content_generator import GeneratedContent


def create_mock_data():
    """創建模擬會議資料"""
    print("📊 創建模擬會議資料...")
    
    # 模擬會議資料
    sessions = [
        ConferenceSession(
            conference_id="mock-001",
            name="AI 驅動的智能客服系統設計與實踐",
            seminar="2025 AI 技術大會",
            description="本次演講將深入探討如何設計和實現一個高效的 AI 驅動智能客服系統，包括自然語言處理、意圖識別、知識圖譜構建等關鍵技術。我們將分享在實際項目中的經驗和最佳實踐。",
            url="https://example.com/ai-customer-service",
            pdf_url="https://example.com/slides/ai-cs.pdf",
            ppt_context="介紹了 AI 客服系統的架構設計，包括 NLP 模組、對話管理、知識庫等核心組件...",
            tags=["AI", "NLP", "客服系統", "自然語言處理"],
            created_at=datetime.now().isoformat(),
            category="ai-tech"
        ),
        ConferenceSession(
            conference_id="mock-002",
            name="區塊鏈在供應鏈金融中的應用",
            seminar="2025 FinTech 峰會",
            description="探討區塊鏈技術如何革新供應鏈金融，提高透明度和效率。涵蓋智能合約、數字身份驗證、資產數字化等核心概念。",
            url="https://example.com/blockchain-supply-chain",
            pdf_url="https://example.com/slides/blockchain-sc.pdf",
            ppt_context="詳細介紹了區塊鏈在供應鏈金融中的具體應用場景...",
            tags=["區塊鏈", "供應鏈", "金融科技", "智能合約"],
            created_at=datetime.now().isoformat(),
            category="fintech"
        ),
        ConferenceSession(
            conference_id="mock-003",
            name="雲原生架構下的微服務治理",
            seminar="2025 雲計算大會",
            description="分享在雲原生環境下如何有效管理和治理微服務架構，包括服務發現、負載均衡、故障恢復等關鍵技術。",
            url="https://example.com/cloud-native-microservices",
            pdf_url="https://example.com/slides/cloud-native.pdf",
            ppt_context="介紹了雲原生微服務架構的設計原則和最佳實踐...",
            tags=["雲原生", "微服務", "Kubernetes", "容器化"],
            created_at=datetime.now().isoformat(),
            category="cloud-computing"
        ),
        ConferenceSession(
            conference_id="mock-004",
            name="大數據驅動的個性化推薦系統",
            seminar="2025 數據科學會議",
            description="深入分析如何利用大數據技術構建高效的個性化推薦系統，涵蓋協同過濾、深度學習、實時計算等技術。",
            url="https://example.com/big-data-recommendation",
            pdf_url="https://example.com/slides/recommendation.pdf",
            ppt_context="詳細講解了推薦系統的算法原理和工程實踐...",
            tags=["大數據", "推薦系統", "機器學習", "協同過濾"],
            created_at=datetime.now().isoformat(),
            category="data-science"
        ),
        ConferenceSession(
            conference_id="mock-005",
            name="前端性能優化的最佳實踐",
            seminar="2025 前端技術大會",
            description="分享前端性能優化的實戰經驗，包括代碼分割、懶加載、緩存策略、CDN 優化等技術手段。",
            url="https://example.com/frontend-performance",
            pdf_url="https://example.com/slides/frontend-perf.pdf",
            ppt_context="介紹了前端性能優化的各種技術和工具...",
            tags=["前端", "性能優化", "JavaScript", "Web"],
            created_at=datetime.now().isoformat(),
            category="web-development"
        )
    ]
    
    # 生成分類映射
    categories = {}
    for session in sessions:
        if session.category not in categories:
            categories[session.category] = []
        categories[session.category].append(session.conference_id)
    
    # 生成統計資料
    statistics = {
        'total_sessions': len(sessions),
        'total_seminars': len(set(s.seminar for s in sessions)),
        'total_categories': len(categories),
        'category_distribution': {cat: len(ids) for cat, ids in categories.items()},
        'seminar_distribution': {s.seminar: 1 for s in sessions},
        'last_updated': datetime.now().isoformat()
    }
    
    dataset = ConferenceData(
        sessions=sessions,
        speakers=[],  # 簡化，不包含講者資料
        categories=categories,
        relationships={},  # 簡化，不包含關聯關係
        statistics=statistics
    )
    
    print(f"✅ 創建了 {len(sessions)} 個模擬會議")
    print(f"   - 分類數量: {len(categories)}")
    print(f"   - 研討會數量: {statistics['total_seminars']}")
    
    return dataset


def create_mock_generated_content(dataset):
    """創建模擬的生成內容"""
    print("\n🤖 創建模擬生成內容...")
    
    generated_contents = {}
    
    for session in dataset.sessions:
        # 模擬生成的內容
        content = GeneratedContent(
            summary=f"這是關於 {session.name} 的智能生成摘要。本次演講深入探討了相關技術的核心概念和實際應用，為與會者提供了寶貴的見解和實踐經驗。",
            keywords=session.tags[:5],  # 使用前5個標籤作為關鍵詞
            category=session.category,
            markdown_content=f"""---
title: "{session.name}"
date: "{session.created_at}"
draft: false
seminar: "{session.seminar}"
category: "{session.category}"
tags: {session.tags}
keywords: {session.tags[:5]}
url: "{session.url}"
pdf_url: "{session.pdf_url}"
summary: "這是關於 {session.name} 的智能生成摘要。"
conference_id: "{session.conference_id}"
---

## 會議概述

這是關於 {session.name} 的智能生成摘要。本次演講深入探討了相關技術的核心概念和實際應用，為與會者提供了寶貴的見解和實踐經驗。

## 會議描述

{session.description}

## 相關資源

- [會議連結]({session.url})
- [簡報下載]({session.pdf_url})

## 簡報內容

{session.ppt_context}

## 關鍵詞

{', '.join(session.tags[:5])}

---

*本內容由 TrendScope 系統自動生成*
""",
            english_translation=None  # 簡化，不包含英文翻譯
        )
        
        generated_contents[session.conference_id] = content
    
    print(f"✅ 創建了 {len(generated_contents)} 個模擬生成內容")
    
    return generated_contents


def demo_hugo_site_creation():
    """演示 Hugo 網站創建"""
    print("\n🏗️  開始 Hugo 網站創建演示...")
    
    # 1. 創建模擬資料
    dataset = create_mock_data()
    
    # 2. 創建模擬生成內容
    generated_contents = create_mock_generated_content(dataset)
    
    # 3. 建構 Hugo 網站
    print("\n🔨 建構 Hugo 網站...")
    site_path = "demo-hugo-complete"
    builder = HugoSiteBuilder(site_path)
    
    try:
        # 建構完整網站
        builder.build_complete_site(dataset, generated_contents)
        
        print(f"\n✅ Hugo 網站建構完成！")
        print(f"📁 網站路徑: {site_path}")
        
        # 顯示網站結構
        print(f"\n📋 網站結構:")
        site_path_obj = Path(site_path)
        for item in sorted(site_path_obj.rglob("*")):
            if item.is_file() and not item.name.startswith('.'):
                relative_path = item.relative_to(site_path_obj)
                print(f"   📄 {relative_path}")
        
        print(f"\n🚀 下一步:")
        print(f"1. 安裝 Hugo: brew install hugo (macOS)")
        print(f"2. cd {site_path}")
        print(f"3. hugo server -D")
        print(f"4. 訪問 http://localhost:1313")
        
        return True
        
    except Exception as e:
        print(f"❌ 網站建構失敗: {e}")
        return False


def create_usage_guide():
    """創建使用指南"""
    guide_content = """# Hugo 會議報告系統使用指南

## 🚀 快速開始

### 1. 環境準備

```bash
# 安裝 Python 依賴
pip install google-cloud-bigquery google-generativeai pyyaml networkx

# 安裝 Hugo
brew install hugo  # macOS
sudo apt install hugo  # Ubuntu
choco install hugo  # Windows
```

### 2. 配置環境變數

```bash
# 複製環境變數模板
cp .env.example .env

# 編輯 .env 文件，設置您的 API Key
GEMINI_API_KEY=your_actual_api_key_here
```

### 3. 運行演示

```bash
# 簡化演示（使用模擬資料）
python demo_hugo_simple.py

# 完整演示（需要 BigQuery 和 Gemini API）
python demo_hugo_system.py

# 自動化流程
python scripts/hugo_automation.py --limit 10 --no-translation
```

## 📊 功能特色

- ✅ **自動化資料提取**: 從 BigQuery 提取會議資料
- ✅ **AI 內容生成**: 使用 Gemini API 生成摘要和關鍵詞
- ✅ **多語言支援**: 中英文雙語內容
- ✅ **響應式設計**: 現代化 Web 界面
- ✅ **關聯圖表**: 互動式資料視覺化
- ✅ **靜態網站**: 高性能 Hugo 生成

## 🛠️ 自定義配置

### 修改分類邏輯
編輯 `scripts/hugo_content_generator.py` 中的 `categorize_session` 方法

### 調整網站樣式
修改 `layouts/` 目錄下的 HTML 模板

### 添加新功能
在相應的模組中添加新的分析方法

## 📞 支援

如有問題，請查看：
1. README.md 文檔
2. 代碼註釋
3. 錯誤日誌文件

---
*Generated by TrendScope Hugo System*
"""
    
    with open("USAGE_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ 使用指南已創建: USAGE_GUIDE.md")


def main():
    """主函數"""
    print("🎯 Hugo 會議報告系統 - 簡化演示")
    print("=" * 60)
    print("此演示使用模擬資料，不需要 BigQuery 或 Gemini API")
    print("=" * 60)
    
    try:
        # 執行演示
        success = demo_hugo_site_creation()
        
        if success:
            # 創建使用指南
            create_usage_guide()
            
            print("\n" + "=" * 60)
            print("🎉 演示完成！")
            print("📖 查看 USAGE_GUIDE.md 了解更多使用方法")
        else:
            print("\n❌ 演示失敗")
    
    except Exception as e:
        print(f"\n❌ 演示過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
