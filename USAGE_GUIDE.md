# Hugo 會議報告系統使用指南

## 🚀 快速開始

### 1. 環境準備

```bash
# 安裝 Python 依賴
pip install google-cloud-bigquery google-generativeai pyyaml networkx

# 安裝 Hugo
brew install hugo  # macOS
sudo apt install hugo  # Ubuntu
choco install hugo  # Windows
```

### 2. 配置環境變數

```bash
# 複製環境變數模板
cp .env.example .env

# 編輯 .env 文件，設置您的 API Key
GEMINI_API_KEY=your_actual_api_key_here
```

### 3. 運行演示

```bash
# 簡化演示（使用模擬資料）
python demo_hugo_simple.py

# 完整演示（需要 BigQuery 和 Gemini API）
python demo_hugo_system.py

# 自動化流程
python scripts/hugo_automation.py --limit 10 --no-translation
```

## 📊 功能特色

- ✅ **自動化資料提取**: 從 BigQuery 提取會議資料
- ✅ **AI 內容生成**: 使用 Gemini API 生成摘要和關鍵詞
- ✅ **多語言支援**: 中英文雙語內容
- ✅ **響應式設計**: 現代化 Web 界面
- ✅ **關聯圖表**: 互動式資料視覺化
- ✅ **靜態網站**: 高性能 Hugo 生成

## 🛠️ 自定義配置

### 修改分類邏輯
編輯 `scripts/hugo_content_generator.py` 中的 `categorize_session` 方法

### 調整網站樣式
修改 `layouts/` 目錄下的 HTML 模板

### 添加新功能
在相應的模組中添加新的分析方法

## 📞 支援

如有問題，請查看：
1. README.md 文檔
2. 代碼註釋
3. 錯誤日誌文件

---
*Generated by TrendScope Hugo System*
