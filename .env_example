# API 配置
# Gemini API 配置
GEMINI_API_KEY=
GEMINI_MODEL_NAME="gemini-2.5-flash"
MAX_REQUESTS_PER_MINUTE=2000
REQUEST_INTERVAL=4
MAX_RETRIES=3
RETRY_DELAY=10

# 檔案路徑
# 輸入文件配置
DATA_DIR="./data"
DEFAULT_INPUT_DIR="${DATA_DIR}/Google IO"
SHEET_DIR="${DATA_DIR}/sheet"
MEETING_EXCEL_FILENAME="${SHEET_DIR}/Google_IO.csv"
INPUT_CSV_PATH="${MEETING_EXCEL_FILENAME}"

# 輸出目錄配置
BASE_OUTPUT_DIR="./googleio_summary"
DEFAULT_OUTPUT_DIR="${BASE_OUTPUT_DIR}/topic"
OUTPUT_MD_DIR="${BASE_OUTPUT_DIR}/topic_md"
OUTPUT_HTML_DIR="${BASE_OUTPUT_DIR}/topic"
SESSION_HTML_DIR="${DEFAULT_OUTPUT_DIR}/session"

# 處理配置
DEFAULT_OUTPUT_FORMAT=md
DEFAULT_WORKERS=10
MAX_TRANSCRIPT_LENGTH=1048576

# 會議設定
MEETING_COL=Meeting
URL_COL=URL

# 02_
TOP_N_MEETINGS=20
BATCH_MD_TO_HTML_INDEX=1

# 其他配置
CONTEXT_CSV_PATH="gtc_session.csv"
CONTEXT_DIAGRAM_OUTPUT_PATH="google_IO_context_diagram.md"

# BigQuery 配置
GOOGLE_APPLICATION_CREDENTIALS="itr-aimasteryhub-lab-1a116262496d.json"
GOOGLE_CLOUD_PROJECT="itr-aimasteryhub-lab"