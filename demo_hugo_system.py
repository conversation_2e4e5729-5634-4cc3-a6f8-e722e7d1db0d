#!/usr/bin/env python3
"""
Hugo 會議報告系統 - 完整示例
演示從 BigQuery 資料提取到 Hugo 網站生成的完整流程
"""

import os
import sys
import json
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

from scripts.hugo_data_extractor import HugoDataExtractor
from scripts.hugo_content_generator import HugoContentGenerator
from scripts.hugo_site_builder import HugoSiteBuilder
from scripts.hugo_relationship_charts import Hugo<PERSON><PERSON><PERSON>hip<PERSON><PERSON>yzer, create_hugo_chart_shortcodes
from scripts.hugo_automation import Hugo<PERSON>utomation


def demo_data_extraction():
    """演示資料提取功能"""
    print("=== 演示 1: BigQuery 資料提取 ===")
    
    extractor = HugoDataExtractor()
    
    # 獲取研討會列表
    seminars = extractor.get_seminars_list()
    print(f"找到 {len(seminars)} 個研討會:")
    for seminar in seminars[:3]:
        print(f"  - {seminar['name']}: {seminar['session_count']} 個會議")
    
    # 提取完整資料集（限制5個用於演示）
    dataset = extractor.extract_complete_dataset(limit=5)
    print(f"\n資料集統計:")
    print(f"  - 會議數量: {len(dataset.sessions)}")
    print(f"  - 分類數量: {len(dataset.categories)}")
    
    return dataset


def demo_content_generation(dataset):
    """演示內容生成功能"""
    print("\n=== 演示 2: LLM 內容生成 ===")
    
    # 檢查 API Key
    if not os.getenv('GEMINI_API_KEY'):
        print("⚠️  未設置 GEMINI_API_KEY，跳過內容生成演示")
        return {}
    
    generator = HugoContentGenerator()
    
    # 處理第一個會議作為示例
    if dataset.sessions:
        session = dataset.sessions[0]
        print(f"處理會議: {session.name}")
        
        try:
            content = generator.process_session(session, include_translation=False)
            print(f"✅ 生成摘要: {content.summary[:100]}...")
            print(f"✅ 提取關鍵詞: {content.keywords}")
            print(f"✅ 智能分類: {content.category}")
            
            return {session.conference_id: content}
        except Exception as e:
            print(f"❌ 內容生成失敗: {e}")
            return {}
    
    return {}


def demo_relationship_analysis(dataset):
    """演示關聯分析功能"""
    print("\n=== 演示 3: 關聯圖表分析 ===")
    
    analyzer = HugoRelationshipAnalyzer()
    
    # 分析關鍵詞關聯性
    keyword_analysis = analyzer.analyze_keyword_relationships(dataset.sessions)
    print(f"關鍵詞分析:")
    print(f"  - 關鍵詞數量: {len(keyword_analysis['importance'])}")
    print(f"  - 共現關係: {len(keyword_analysis['cooccurrence'])}")
    
    # 分析分類關聯性
    category_analysis = analyzer.analyze_category_relationships(dataset.sessions)
    print(f"分類分析:")
    print(f"  - 分類數量: {len(category_analysis['sessions'])}")
    print(f"  - 相似關係: {len(category_analysis['similarity'])}")
    
    # 建構關聯圖
    graph = analyzer.build_relationship_graph(dataset)
    print(f"關聯圖:")
    print(f"  - 節點數量: {len(graph.nodes)}")
    print(f"  - 邊數量: {len(graph.edges)}")
    
    return graph


def demo_hugo_site_building(dataset, generated_contents, relationship_graph):
    """演示 Hugo 網站建構"""
    print("\n=== 演示 4: Hugo 網站建構 ===")
    
    site_path = "demo-hugo-site"
    builder = HugoSiteBuilder(site_path)
    
    # 建構網站結構
    print("創建目錄結構...")
    builder.create_directory_structure()
    
    print("創建配置文件...")
    builder.create_config_files()
    
    print("創建模板...")
    builder.create_base_templates()
    builder.create_partial_templates()
    
    print("創建內容文件...")
    builder.create_content_files(dataset, generated_contents)
    
    # 創建關聯圖表 shortcodes
    print("創建關聯圖表...")
    create_hugo_chart_shortcodes(site_path, relationship_graph)
    
    print(f"✅ Hugo 網站已創建在: {site_path}")
    print("下一步:")
    print(f"1. cd {site_path}")
    print("2. hugo server -D")
    print("3. 訪問 http://localhost:1313")
    
    return site_path


def demo_complete_automation():
    """演示完整自動化流程"""
    print("\n=== 演示 5: 完整自動化流程 ===")
    
    # 檢查 API Key
    if not os.getenv('GEMINI_API_KEY'):
        print("⚠️  未設置 GEMINI_API_KEY，跳過自動化演示")
        return
    
    automation = HugoAutomation(
        site_path="auto-hugo-site",
        include_translation=False,  # 為了演示速度，不包含翻譯
        limit_sessions=3  # 限制會議數量
    )
    
    print("執行自動化流程...")
    success = automation.run_complete_pipeline()
    
    if success:
        print("✅ 自動化流程完成！")
    else:
        print("❌ 自動化流程失敗")


def create_readme():
    """創建 README 文件"""
    readme_content = """# Hugo 會議報告系統

這是一個基於 Hugo 的靜態網站生成系統，用於自動化生成技術會議報告。

## 系統特色

- 🔄 **自動化流程**: 從 BigQuery 資料提取到網站部署的端到端自動化
- 🤖 **AI 內容生成**: 使用 Gemini API 生成會議摘要和關鍵詞
- 🌐 **多語言支援**: 中英文雙語內容
- 📊 **關聯圖表**: 互動式會議關聯性視覺化
- ⚡ **高性能**: Hugo 靜態網站生成器，快速載入
- 📱 **響應式設計**: 支援多設備訪問

## 系統架構

```
BigQuery 資料庫 → 資料提取 → LLM 內容生成 → Hugo 靜態網站 → 部署
     ↓              ↓              ↓              ↓
  會議原始資料    結構化查詢      Markdown 內容    靜態網站
```

## 快速開始

### 1. 環境設置

```bash
# 安裝 Python 依賴
pip install google-cloud-bigquery google-generativeai pyyaml networkx

# 安裝 Hugo
# macOS: brew install hugo
# Ubuntu: sudo apt install hugo
# Windows: choco install hugo

# 設置環境變數
export GEMINI_API_KEY="your-gemini-api-key"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/bigquery-credentials.json"
```

### 2. 執行演示

```bash
# 運行完整演示
python demo_hugo_system.py

# 或執行自動化流程
python scripts/hugo_automation.py --limit 10 --no-translation
```

### 3. 查看結果

```bash
cd demo-hugo-site  # 或 auto-hugo-site
hugo server -D
# 訪問 http://localhost:1313
```

## 模組說明

### 資料提取模組 (`hugo_data_extractor.py`)
- 從 BigQuery 提取會議資料
- 支援按研討會、分類篩選
- 生成統計資料

### 內容生成模組 (`hugo_content_generator.py`)
- 使用 Gemini API 生成會議摘要
- 智能提取關鍵詞
- 自動分類會議
- 支援多語言翻譯

### 網站建構模組 (`hugo_site_builder.py`)
- 創建 Hugo 目錄結構
- 生成配置文件和模板
- 創建多語言內容文件

### 關聯分析模組 (`hugo_relationship_charts.py`)
- 分析會議關聯性
- 生成互動式圖表
- 支援 D3.js 和 Chart.js

### 自動化模組 (`hugo_automation.py`)
- 端到端自動化流程
- 支援命令行參數
- 詳細日誌記錄

## 網站結構

- **首頁**: 統計概覽、最新會議、關聯圖表
- **分類頁**: 按技術領域分類的會議列表
- **會議詳情頁**: 完整的會議資訊和相關資源

## 自定義配置

### 修改分類邏輯
編輯 `hugo_content_generator.py` 中的 `categorize_session` 方法

### 調整模板樣式
修改 `layouts/` 目錄下的 HTML 模板

### 添加新的圖表類型
在 `hugo_relationship_charts.py` 中添加新的分析方法

## 部署選項

- **GitHub Pages**: 推送到 GitHub 倉庫
- **Netlify**: 連接 Git 倉庫自動部署
- **Vercel**: 支援 Hugo 的無服務器部署
- **自建服務器**: 使用 nginx 或 Apache 託管靜態文件

## 故障排除

### 常見問題

1. **BigQuery 連接失敗**
   - 檢查 `GOOGLE_APPLICATION_CREDENTIALS` 環境變數
   - 確認服務帳號有 BigQuery 讀取權限

2. **Gemini API 錯誤**
   - 檢查 `GEMINI_API_KEY` 環境變數
   - 確認 API 配額和限制

3. **Hugo 建構失敗**
   - 檢查 Hugo 版本 (建議 0.100+)
   - 確認模板語法正確

## 貢獻指南

歡迎提交 Issue 和 Pull Request！

## 授權

MIT License
"""
    
    with open("README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ README.md 已創建")


def main():
    """主函數 - 運行完整演示"""
    print("🚀 Hugo 會議報告系統 - 完整演示")
    print("=" * 50)
    
    try:
        # 1. 資料提取演示
        dataset = demo_data_extraction()
        
        # 2. 內容生成演示
        generated_contents = demo_content_generation(dataset)
        
        # 3. 關聯分析演示
        relationship_graph = demo_relationship_analysis(dataset)
        
        # 4. 網站建構演示
        site_path = demo_hugo_site_building(dataset, generated_contents, relationship_graph)
        
        # 5. 創建 README
        create_readme()
        
        print("\n" + "=" * 50)
        print("🎉 演示完成！")
        print(f"📁 演示網站: {site_path}")
        print("📖 說明文件: README.md")
        
        # 6. 完整自動化演示（可選）
        if input("\n是否執行完整自動化演示？(y/N): ").lower() == 'y':
            demo_complete_automation()
        
    except Exception as e:
        print(f"❌ 演示過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
