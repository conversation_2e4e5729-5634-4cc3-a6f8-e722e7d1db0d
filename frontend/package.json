{"name": "trendscope", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "^1.4.0", "@mui/material": "^5.15.3", "@mui/icons-material": "^5.15.3", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "axios": "^1.6.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.3.5"}, "description": "This contains everything you need to run your app locally.", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}