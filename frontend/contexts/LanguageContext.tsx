
import React, { createContext, useState, ReactNode, useEffect } from 'react';
import { Language, LanguageContextType } from '../types';

const translations: Record<Language, Record<string, Record<string, string>>> = {
  [Language.EN]: {
    general: {
      appName: 'TrendScope',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      noData: 'No data available.',
      search: 'Search...',
      filter: 'Filter',
      sort: 'Sort',
      export: 'Export',
      delete: 'Delete',
      viewDetails: 'View Details',
      save: 'Save',
      cancel: 'Cancel',
      confirm: 'Confirm',
      close: 'Close',
      edit: 'Edit',
      add: 'Add New',
      download: 'Download',
      upload: 'Upload',
      trigger: 'Trigger',
      enable: 'Enable',
      disable: 'Disable',
      status: 'Status',
      action: 'Action',
      settings: 'Settings',
      logout: 'Logout',
      summary: 'Summary',
      notifications: 'Notifications',
      markAsRead: 'Mark as read',
      noNotifications: 'No new notifications.',
      language: 'Language',
      theme: 'Theme',
      lightMode: 'Light Mode',
      darkMode: 'Dark Mode',
      Page: "Page",
      of: "of",
      Previous: "Previous",
      Next: "Next",
      "Mock Chart Area": "Mock Chart Area",
      "Start Date": "Start Date",
      "End Date": "End Date",
      "Data Source Type": "Data Source Type",
      "Analysis Template": "Analysis Template",
      "Report Title": "Report Title",
      "Enter report title": "Enter report title",
      "Report Notes (optional)": "Report Notes (optional)",
      "Add any notes here": "Add any notes here",
      "Oops! Page not found.": "Oops! Page not found.",
      "The page you are looking for doesn't exist or has been moved.": "The page you are looking for doesn't exist or has been moved.",
      "Go to Dashboard": "Go to Dashboard",
    },
    sidebar: {
      dashboard: 'Dashboard',
      databaseManagement: 'Database',
      pptUpload: 'PPT Upload',
      reportManagement: 'Reports',

      batchReports: 'Report Processing',
      batchReportTasks: 'Report Management',

      crawlerManagement: 'Crawlers',
      systemSettings: 'Settings',
      logQuery: 'Log Query',
      // 分組標題
      dataCollection: 'Data Collection',
    },
    dashboard: {
      title: 'Dashboard',
      overview: 'System Overview',
      currentTasks: 'Current Tasks',
      summariesToday: 'Summaries Today',
      pendingReports: 'Pending Reports',
      systemAlerts: 'System Alerts',
      taskStatus: 'Task Status Distribution',
      summaryTrend: 'Summary Output Trend',
      quickAccess: 'Quick Access',
      createTask: 'Create New Task',
      uploadFile: 'Upload File',
      completed: "Completed",
      inProgress: "In Progress",
      pending: "Pending",
      failed: "Failed",
      day: "Day"
    },
    database: {
      title: 'Database Management',
      conference: 'Conference',
      seminar: 'Seminar',
      date: 'Date',
      meeting: 'Meeting',
      url: 'URL',
      abstract: 'Abstract',
      topic: 'Topic',
      ppt: 'PPT',
      other: 'Other',
      trendDataDetails: 'Trend Data Details',
    },


    crawlers: {
      title: 'Crawler Management',
      name: 'Name',
      source: 'Source',
      status: 'Status',
      lastRun: 'Last Run',
      enabled: 'Enabled',
      disabled: 'Disabled',
      error: 'Error',
      viewLogs: 'View Logs',
      newCrawler: 'New Crawler',
      uploadYamlJson: 'Upload YAML/JSON',
      createWithForm: 'Create with Form',
      aiAgent: 'AI Agent - Crawler MCP',
      aiHelper: 'AI agent to assist in building crawler workflows.',
    },
     settings: {
      title: 'System Settings',
      apiKeys: 'API Keys & Parameters',
      bigQueryStorage: 'BigQuery/Cloud Storage',
      userPermissions: 'User Permissions',
      systemParameters: 'System Parameters',
      apiKeyNote: 'API keys for external services (excluding Gemini API key which is environment configured).',
    },
    logs: {
      title: 'Log Query',
      searchLogs: 'Search logs...',
      filterByLevel: 'Filter by level',
      filterByDate: 'Filter by date range',
      downloadLogs: 'Download Logs',
    },
    // ... other sections and keys
  },
  [Language.ZH_TW]: {
    general: {
      appName: 'TrendScope',
      loading: '載入中...',
      error: '錯誤',
      success: '成功',
      noData: '暫無資料。',
      search: '搜尋...',
      filter: '篩選',
      sort: '排序',
      export: '匯出',
      delete: '刪除',
      viewDetails: '查看詳情',
      save: '儲存',
      cancel: '取消',
      confirm: '確認',
      close: '關閉',
      edit: '編輯',
      add: '新增',
      download: '下載',
      upload: '上傳',
      trigger: '觸發',
      enable: '啟用',
      disable: '停用',
      status: '狀態',
      action: '操作',
      settings: '設定',
      logout: '登出',
      summary: '摘要',
      notifications: '通知',
      markAsRead: '標為已讀',
      noNotifications: '沒有新通知。',
      language: '語言',
      theme: '主題',
      lightMode: '淺色模式',
      darkMode: '深色模式',
      Page: "頁數",
      of: "共", // "of" can be translated to "共" for "Page X of Y"
      Previous: "上一頁",
      Next: "下一頁",
      "Mock Chart Area": "模擬圖表區域",
      "Start Date": "開始日期",
      "End Date": "結束日期",
      "Data Source Type": "資料來源類型",
      "Analysis Template": "分析模板",
      "Report Title": "報告標題",
      "Enter report title": "輸入報告標題",
      "Report Notes (optional)": "報告備註（可選）",
      "Add any notes here": "在此處添加任何備註",
      "Oops! Page not found.": "哎呀！找不到頁面。",
      "The page you are looking for doesn't exist or has been moved.": "您正在尋找的頁面不存在或已被移動。",
      "Go to Dashboard": "前往總覽",
    },
    sidebar: {
      dashboard: '總覽',
      databaseManagement: '資料庫管理',
      pptUpload: 'PPT 上傳',
      reportManagement: '報告管理',

      batchReports: '報告處理',
      batchReportTasks: '報告管理',

      crawlerManagement: '爬蟲管理',
      systemSettings: '系統設定',
      logQuery: '日誌查詢',
      // 分組標題
      dataCollection: '數據搜集',
    },
    dashboard: {
      title: '儀表板',
      overview: '系統整體狀態總覽',
      currentTasks: '目前報告數',
      summariesToday: '今日產生摘要數',
      pendingReports: '審核報告數',
      systemAlerts: '系統警示',
      taskStatus: '任務狀態分佈圖',
      summaryTrend: '摘要產出趨勢圖',
      quickAccess: '快速入口',
      createTask: '建立新任務',
      uploadFile: '上傳檔案',
      completed: "已完成",
      inProgress: "進行中",
      pending: "待處理",
      failed: "失敗",
      day: "日"
    },
    database: {
      title: '資料庫管理',
      conference: '研討會',
      seminar: '研討會',
      date: '日期',
      meeting: '會議',
      url: 'URL',
      abstract: '摘要',
      topic: '主題',
      ppt: 'PPT',
      other: '其他',
      trendDataDetails: '趨勢資料詳情',
    },


    crawlers: {
      title: '爬蟲管理',
      name: '名稱',
      source: '來源',
      status: '狀態',
      lastRun: '最後執行時間',
      enabled: '啟用',
      disabled: '停用',
      error: '出錯',
      viewLogs: '檢視日誌',
      newCrawler: '新增爬蟲',
      uploadYamlJson: '上傳 YAML/JSON',
      createWithForm: '透過表單建立',
      aiAgent: 'AI Agent - Crawler MCP',
      aiHelper: 'AI 代理輔助構建爬蟲流程。',
    },
    settings: {
      title: '系統設定',
      apiKeys: 'API 金鑰與參數管理',
      bigQueryStorage: 'BigQuery/Cloud Storage 設定',
      userPermissions: '使用者權限管理',
      systemParameters: '系統參數調整',
      apiKeyNote: '外部服務的 API 金鑰（不包含 Gemini API 金鑰，其由環境變數配置）。',
    },
    logs: {
      title: '日誌查詢',
      searchLogs: '搜尋日誌...',
      filterByLevel: '依等級篩選',
      filterByDate: '依日期範圍篩選',
      downloadLogs: '下載日誌',
    },
    // ... other sections and keys
  },
};


export const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>(() => {
    const storedLang = localStorage.getItem('language');
    if (storedLang && Object.values(Language).includes(storedLang as Language)) {
      return storedLang as Language;
    }
    // Fallback to browser language or default
    const browserLang = navigator.language.toLowerCase();
    if (browserLang.startsWith('zh')) return Language.ZH_TW;
    return Language.EN;
  });

  useEffect(() => {
    localStorage.setItem('language', language);
    document.documentElement.lang = language;
  }, [language]);

  const t = (key: string, section: string = 'general'): string => {
    return translations[language]?.[section]?.[key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};
