# Hugo 會議報告系統

這是一個基於 Hugo 的靜態網站生成系統，用於自動化生成技術會議報告。

## 系統特色

- 🔄 **自動化流程**: 從 BigQuery 資料提取到網站部署的端到端自動化
- 🤖 **AI 內容生成**: 使用 Gemini API 生成會議摘要和關鍵詞
- 🌐 **多語言支援**: 中英文雙語內容
- 📊 **關聯圖表**: 互動式會議關聯性視覺化
- ⚡ **高性能**: Hugo 靜態網站生成器，快速載入
- 📱 **響應式設計**: 支援多設備訪問

## 系統架構

```
BigQuery 資料庫 → 資料提取 → LLM 內容生成 → Hugo 靜態網站 → 部署
     ↓              ↓              ↓              ↓
  會議原始資料    結構化查詢      Markdown 內容    靜態網站
```

## 快速開始

### 1. 環境設置

```bash
# 安裝 Python 依賴
pip install google-cloud-bigquery google-generativeai pyyaml networkx

# 安裝 Hugo
# macOS: brew install hugo
# Ubuntu: sudo apt install hugo
# Windows: choco install hugo

# 設置環境變數
export GEMINI_API_KEY="your-gemini-api-key"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/bigquery-credentials.json"
```

### 2. 執行演示

```bash
# 運行完整演示
python demo_hugo_system.py

# 或執行自動化流程
python scripts/hugo_automation.py --limit 10 --no-translation
```

### 3. 查看結果

```bash
cd demo-hugo-site  # 或 auto-hugo-site
hugo server -D
# 訪問 http://localhost:1313
```

## 模組說明

### 資料提取模組 (`hugo_data_extractor.py`)
- 從 BigQuery 提取會議資料
- 支援按研討會、分類篩選
- 生成統計資料

### 內容生成模組 (`hugo_content_generator.py`)
- 使用 Gemini API 生成會議摘要
- 智能提取關鍵詞
- 自動分類會議
- 支援多語言翻譯

### 網站建構模組 (`hugo_site_builder.py`)
- 創建 Hugo 目錄結構
- 生成配置文件和模板
- 創建多語言內容文件

### 關聯分析模組 (`hugo_relationship_charts.py`)
- 分析會議關聯性
- 生成互動式圖表
- 支援 D3.js 和 Chart.js

### 自動化模組 (`hugo_automation.py`)
- 端到端自動化流程
- 支援命令行參數
- 詳細日誌記錄

## 網站結構

- **首頁**: 統計概覽、最新會議、關聯圖表
- **分類頁**: 按技術領域分類的會議列表
- **會議詳情頁**: 完整的會議資訊和相關資源

## 自定義配置

### 修改分類邏輯
編輯 `hugo_content_generator.py` 中的 `categorize_session` 方法

### 調整模板樣式
修改 `layouts/` 目錄下的 HTML 模板

### 添加新的圖表類型
在 `hugo_relationship_charts.py` 中添加新的分析方法

## 部署選項

- **GitHub Pages**: 推送到 GitHub 倉庫
- **Netlify**: 連接 Git 倉庫自動部署
- **Vercel**: 支援 Hugo 的無服務器部署
- **自建服務器**: 使用 nginx 或 Apache 託管靜態文件

## 故障排除

### 常見問題

1. **BigQuery 連接失敗**
   - 檢查 `GOOGLE_APPLICATION_CREDENTIALS` 環境變數
   - 確認服務帳號有 BigQuery 讀取權限

2. **Gemini API 錯誤**
   - 檢查 `GEMINI_API_KEY` 環境變數
   - 確認 API 配額和限制

3. **Hugo 建構失敗**
   - 檢查 Hugo 版本 (建議 0.100+)
   - 確認模板語法正確

## 貢獻指南

歡迎提交 Issue 和 Pull Request！

## 授權

MIT License
