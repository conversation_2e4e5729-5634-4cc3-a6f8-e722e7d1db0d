# AppSheet Accelerate AI adoption into the flow of work
[會議影片連結](https://www.youtube.com/watch?v=R0KeMD2bP6M)
AppSheet 加速 AI 導入工作流程

## 1. 核心觀點

AppSheet 是一個無需編碼的平台，旨在賦予業務使用者快速構建應用程式和自動化流程的能力，直接在業務資料之上進行。透過 Gemini AI 的整合，AppSheet 能夠自動提取和分類資訊，簡化工作流程，並與企業資料系統連接，使 AI 更容易導入工作流程。

## 2. 詳細內容

AppSheet 是一個 Google 的無程式碼平台，旨在幫助企業使用者快速構建應用程式和自動化流程，而無需編寫任何程式碼。在 Cloud Next 大會上，AppSheet 宣布了更深入的整合、更強大的自動化、增強的治理，以及 Gemini AI 的整合。

一個範例是 AppSheet 建立的 Helpdesk 應用程式，它可以處理從建立支援單到批准、採購和追蹤訂單的整個工作流程。整個自動化流程始於使用者拍攝問題照片。透過上傳照片並使用 Gemini AI 功能，AppSheet 可以自動提取和分類資訊，無需手動輸入。

AppSheet 也能夠與 Google Sites 整合，使整個 Helpdesk 應用程式（包括更新的工單）可以在 TeamSite 上安全地存取。

AppSheet 的三個層次包括：AppSheet 解決方案（應用程式或自動化）、編輯器（用於配置應用程式和自動化），以及資料（AppSheet 的核心）。AppSheet 可以連接到各種資料來源，包括 Sheets 和 Excel。Google Cloud Integration 連接器將在未來幾天內正式推出，允許使用者使用 SAP、JIRA 和 ServiceNow 等企業資料系統構建應用程式和自動化流程。

## 3. 重要結論

AppSheet 透過 Gemini AI 的整合，簡化了工作流程，並使 AI 更容易導入企業的應用程式和自動化流程。透過無程式碼平台，業務使用者可以快速構建解決方案，而無需編寫任何程式碼。AppSheet 的 Google Cloud Integration 連接器將允許使用者使用企業資料系統構建應用程式和自動化流程，進一步擴展了 AppSheet 的功能。
