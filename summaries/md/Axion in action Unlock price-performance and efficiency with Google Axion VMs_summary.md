Axion in action Unlock price-performance and efficiency with Google Axion VMs

[會議影片連結](https://www.youtube.com/watch?v=medSOm7zQU0)
Axion 實戰：解鎖 Google Axion VM 的價格效能與效率

## 1. 核心觀點

本次會議主要介紹 Google 的 Axion 處理器，以及 Databricks 和 Spotify 如何利用 Axion 提升效能、降低成本和減少碳足跡。核心觀點包括：

*   **Axion 的優勢：** Axion 提供 ARM 架構的最佳價格效能，特別是在資料庫和 Web 服務工作負載方面，同時具有出色的每瓦效能。
*   **廣泛應用：** Axion 已被 Google 內部多個應用程式採用，並在 Cloud SQL 和 AlloyDB 中提供支援。
*   **客戶實例：** Spotify 和 Databricks 分享了他們使用 Axion 的經驗，包括顯著的效能提升和成本節省。
*   **軟硬體協同：** 軟體（如 Databricks Photon 引擎）與硬體（如 Axion 處理器和 Hyperdisk 儲存）的結合，能帶來最大的效能提升。

## 2. 詳細內容

*   **Google Axion 介紹：**
    *   Axion 是 Google 首款客製化伺服器處理器，基於 ARM Neoverse v2 運算核心。
    *   C4A 實例家族提供多種通用機器規格，最高可達 72 個 vCPU，並支援高速網路和最新一代 HyperDisk 遠端儲存選項。
    *   Axion 在價格效能和功耗效率方面優於其他 ARM 處理器。
*   **Spotify 的 Axion 之旅：**
    *   Spotify 是一家全球音訊串流服務公司，擁有超過 6.75 億用戶。
    *   Spotify 的主要目標是降低成本、減少碳排放並提升效能。
    *   透過將工作負載遷移到 Axion，Spotify 實現了顯著的效能提升（平均 250%），並節省了高達 40% 的運算成本。
    *   Spotify 建議定期重新評估實例家族，並與 Google Cloud 團隊保持聯繫。
*   **Databricks 的 Axion 之旅：**
    *   Databricks 是一家資料和 AI 公司，其資料智慧平台被超過 12,000 家客戶使用。
    *   Databricks 與 Google Cloud 深度整合，並支援其整個資料平台。
    *   Databricks 選擇 Axion 的主要原因是其卓越的效能和成本效益。
    *   Databricks 發現 Axion 能夠提供高達 50% 的效能提升，並且 Hyperdisk 和 Titanium 本地 SSD 可以顯著提升 IO 密集型工作負載的效能。
    *   Databricks 強調軟硬體協同的重要性，並建議使用較新版本的軟體堆疊。

## 3. 重要結論

Google 的 Axion 處理器為 ARM 架構工作負載提供了卓越的價格效能和效率。Spotify 和 Databricks 的成功案例證明了 Axion 在實際應用中的價值。透過將工作負載遷移到 Axion，企業可以顯著提升效能、降低成本並減少碳足跡。此外，軟硬體協同和定期評估實例家族是充分利用 Axion 優勢的關鍵。
