# Run high-availability multi-region services with Cloud Run
[會議影片連結](https://www.youtube.com/watch?v=tTv-SVkKRTw)
使用 Cloud Run 運行高可用性多區域服務

## 1. 核心觀點

本次會議主要介紹如何使用 Cloud Run 構建高可用性多區域服務。講者分享了 Cloud Run 的優勢，包括彈性擴展、快速部署和高可靠性。此外，還介紹了 Commerce Bank 如何利用 Cloud Run 實現高可用性和可靠的銀行服務，以及 Cloud Run 即將推出的 Service Health 功能，該功能可以自動化跨區域故障轉移，確保業務關鍵服務的高可用性。

## 2. 詳細內容

*   **Cloud Run 簡介：** Cloud Run 是一個完全託管的平台，可以在 Google 的可擴展基礎架構上運行程式碼、函數或容器。其主要優勢包括：
    *   超彈性計算：可以快速擴展和縮減。
    *   更快的開發速度：可以更快地部署程式碼，將更多時間花在應用程式邏輯上。
    *   開箱即用的高可靠性：預設情況下具有區域冗餘。

*   **容錯設計：** 為了實現高可用性，需要進行容錯設計。Cloud Run 提供了一個可靠且高度可擴展的平台，只需最少的配置，即可運行跨多個區域的高可用性業務關鍵服務。這包括：
    *   故障隔離：將應用程式架構為分層應用程式和模組化服務，以隔離熱流量和冷流量，並確保一個服務中的故障不會影響另一個服務。
    *   自動擴展：如果某些實例出現故障，Cloud Run 會在幾秒鐘內擴展新的實例。
    *   關鍵冗餘：Cloud Run 預設具有區域冗餘。
    *   全域負載平衡：Service Health 功能可以實現跨區域故障轉移，以滿足恢復時間目標。
    *   其他恢復機制：可以使用可擴展的多區域資料庫（如 Spanner）來確保滿足其他恢復點目標。

*   **故障場景：** 講者介紹了三種故障場景以及 Cloud Run 如何應對：
    *   控制平面中斷：即使控制平面中斷，資料平面也不會受到影響，請求可以根據需要擴展和縮減，作業可以繼續執行，並且日誌記錄和監控可以正常運行。
    *   區域中斷：Cloud Run 具有 N+1 區域冗餘，流量將自動路由到健康的區域。Cloud Run 還支援區域分離，這意味著資料中心在這些區域內實際上是物理分離的。
    *   區域性中斷：需要設定備份和複製，並將服務部署到多個區域。Service Health 可以自動化跨區域故障轉移。

*   **Commerce Bank 的案例：** Commerce Bank 使用 Cloud Run 實現高可用性和可靠的銀行服務。他們使用 Cloud Run 作為客戶資料的主要搜尋引擎，每天處理超過 70 萬次搜尋。他們的架構包括：
    *   內部區域負載平衡器
    *   代理 VM
    *   Serverless Connector
    *   Cloud Run 搜尋引擎
    *   Cloud SQL (Postgres SQL)

    他們還評估了多種高可用性選項，包括在不同區域中設定讀取副本和使用負載平衡器進行故障轉移。

*   **Service Health 演示：** Taylor 演示了 Cloud Run 即將推出的 Service Health 功能，該功能可以自動化跨區域故障轉移。該功能使用容器就緒探針來確定容器是否健康，並使用全域負載平衡器將流量路由到健康的區域。

## 3. 重要結論

Cloud Run 提供了一個強大的平台，可以用於構建高可用性多區域服務。透過利用 Cloud Run 的彈性擴展、快速部署和高可靠性功能，以及 Service Health 等新功能，可以確保業務關鍵服務始終可用。Commerce Bank 的案例證明了 Cloud Run 在實際應用中的價值。
