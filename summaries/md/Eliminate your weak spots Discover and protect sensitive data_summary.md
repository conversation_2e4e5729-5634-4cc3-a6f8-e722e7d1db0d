```
# Eliminate your weak spots Discover and protect sensitive data
[會議影片連結](https://www.youtube.com/watch?v=tRKP2m2tung)
消除你的弱點，發現並保護敏感資料

## 1. 核心觀點

本次會議主要討論如何消除雲端環境中的弱點，特別是針對敏感資料的理解和保護。講者強調，雲端風險往往來自於對環境中未知因素的缺乏了解，例如未知的專案或資料，這些都可能導致潛在的風險暴露。會議重點在於識別高價值資產、理解攻擊路徑，並利用 Google Cloud 的服務來降低風險。

## 2. 詳細內容

會議首先指出，在雲端環境中，理解高價值資產（如個人身份資訊、憑證、財務或醫療資料）至關重要。接著，需要分析外部或內部使用者可能存取這些資產的所有途徑。透過結合這兩個資訊，可以更深入地了解潛在的風險，並確定資料如何暴露於濫用或攻擊之下。

講者以一個實際案例說明，一個看似不重要的虛擬機器（VM）由於存在漏洞，卻擁有存取包含敏感資料的儲存貯體的高權限。即使該 VM 本身並不重要，但其漏洞卻暴露了儲存貯體，這是一個未知的風險。

Google Cloud 提供的敏感資料保護服務、安全指揮中心和虛擬紅隊技術可以幫助識別資產、分析攻擊路徑，並將未知的風險轉化為可優先處理的風險。

會議還介紹了如何使用這些工具來保護 AI 工作負載。由於 AI 模型依賴大量資料，因此保護這些資料至關重要。講者展示了一個包含憑證和個人身份資訊的訓練資料集，並示範如何使用敏感資料保護服務來發現這些敏感資料，並透過遮罩或移除來進行補救。

此外，會議強調自動化的重要性，包括自動化資料探索、資料遮罩和存取控制。透過自動化標記，可以根據資產的內容自動控制存取權限。

最後，會議強調持續評估風險的重要性，即使已經採取了保護措施，也需要持續監控潛在的攻擊路徑，以確保資料的安全。

Jordana 接著展示了敏感資料保護服務的實際操作，包括如何設定掃描配置、如何使用資料剖析來識別敏感資料，以及如何使用 IAM 條件來控制對敏感資料的存取。她還示範了如何使用 Inspect Jobs 來遮罩敏感資料，並將遮罩後的資料用於 AI 訓練。

Nathaniel 則從合作夥伴的角度，分享了如何協助客戶設定敏感資料保護，包括如何進行資料剖析和檢查，以及如何制定報告和回應策略。他強調，每個企業都是獨一無二的，因此需要客製化的解決方案。

會議最後介紹了資料傳輸中的保護，這是一個新的領域，旨在監控網路流量，並識別在傳輸過程中暴露的敏感資料。

## 3. 重要結論

本次會議強調了在雲端環境中保護敏感資料的重要性，並介紹了 Google Cloud 提供的各種工具和技術。會議強調，理解風險、自動化保護措施和持續評估風險是保護敏感資料的關鍵。透過消除未知因素、遮罩或封鎖不需要的敏感資料、設定適當的存取控制，以及持續評估資料資產的風險，企業可以有效地保護其敏感資料，並降低潛在的風險。
```