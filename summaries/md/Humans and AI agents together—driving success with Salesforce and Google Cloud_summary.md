Humans and AI agents together—driving success with Salesforce and Google Cloud
[會議影片連結](https://www.youtube.com/watch?v=CLnCVdwWwPQ)
Humans and AI agents together—driving success with Salesforce and Google Cloud

## 1. 核心觀點

本次會議主要探討了 Salesforce Agent Force 如何與 Google Cloud 合作，利用多代理系統（Multi-Agent Systems）和代理互操作性（Agent Interoperability）來提升客戶體驗和工作效率。核心觀點包括：

*   **Agent Force 的重要性：** Agent Force 作為 Salesforce 的數位勞動力平台，旨在解決企業工作人員工作量過大和客戶期望更高的問題，透過構建自主代理來處理低價值任務，提升生產力。
*   **多代理系統的優勢：** 多個專業代理協同工作，可以更有效地處理複雜任務，提高效能、彈性和可擴展性。
*   **代理互操作性的必要性：** 解決不同平台（如 Salesforce、Google Cloud、Anthropic、OpenAI）構建的代理之間無法有效溝通的問題，實現無縫協作，提供一致的客戶體驗。
*   **Salesforce 與 Google Cloud 的合作：** 透過 A2A（Agent-to-Agent）協議，實現不同平台代理之間的互操作性，共同推動 AI 創新。

## 2. 詳細內容

*   **Agent Force 101：**
    *   Agent Force 是一個數位勞動力平台，建立在 Salesforce 平台之上，允許構建具有預建技能的自主代理，適用於任何角色和行業。
    *   Agent Force 的五個關鍵屬性：角色、資料存取權限、能力、護欄（限制）和工作管道。
    *   Agent Force 不僅僅是協同助手，而是能夠理解業務、規劃、推理和採取行動的自主系統。

*   **多代理系統：**
    *   單一代理在特定任務中非常強大，但處理複雜任務時容易過載。
    *   多代理系統允許多個專業代理協同工作，提高效能和效率。
    *   多代理系統的應用場景包括軟體工程、銷售和行銷、客戶服務等。
    *   多代理系統的關鍵屬性包括代理間通訊、資源和資料的標準化存取、以及系統的可擴展性。

*   **代理互操作性：**
    *   缺乏代理互操作性會導致客戶體驗碎片化、複雜的客製化整合、有限的上下文共享、安全和合規性問題，並抑制創新。
    *   代理互操作性的關鍵原則包括：標準化的代理資訊（Agent Card）、代理註冊和發現服務、身份共享、上下文共享、代理間通訊、狀態和對話管理、可觀察性、安全性和人為介入。
    *   透過一個智慧手錶購買的例子，展示了客戶服務代理如何與 Google Vertex AI 代理協作，為客戶提供產品推薦。

*   **Salesforce 與 Google Cloud 的合作（A2A）：**
    *   A2A 旨在實現代理之間的互操作性，連接不同的代理系統。
    *   A2A 的關鍵原則包括：企業級就緒、簡單一致、非同步優先和模式不可知、不透明執行。
    *   A2A 與 MCP（Model-as-Code Platform）協同工作，MCP 用於連接代理與工具，A2A 用於連接代理與代理。

*   **Demo 演示：**
    *   展示了客戶服務代理如何與 Google Vertex AI 代理協作，處理客戶的退貨和產品推薦請求。
    *   強調了 API 不足以應對動態場景，需要行業標準的代理間協議。

## 3. 重要結論

Salesforce 與 Google Cloud 透過合作，利用 Agent Force、多代理系統和 A2A 協議，正在推動 AI 在企業應用中的創新。代理互操作性是實現無縫客戶體驗和提高工作效率的關鍵。雙方將繼續合作，推動 A2A 成為行業標準，並探索更多創新應用。
