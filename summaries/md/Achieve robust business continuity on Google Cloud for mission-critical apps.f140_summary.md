```
# Achieve robust business continuity on Google Cloud for mission-critical apps.f140

[會議影片連結]()

在 Google Cloud 上實現關鍵任務應用程式的強大業務持續性

## 1. 核心觀點

本次會議主要探討在 Google Cloud 上，針對關鍵任務應用程式，如何建立強大的業務持續性策略。核心觀點包括：

*   **業務持續性的基石：** 強調架構設計和運營實踐的重要性，兩者缺一不可。
*   **多區域架構：** 針對關鍵任務應用程式，建議採用多區域架構，以確保高可用性和容錯能力。
*   **安全變更管理：** 強調變更管理是導致事故的主要原因之一，因此需要建立健全的變更管理策略。
*   **故障測試：** 鼓勵進行故障測試，以在生產環境之前發現潛在問題。
*   **事件偵測與回應：** 建立快速偵測和回應事件的策略，並設定 SLO 以提高可預測性。
*   **Charles Schwab 的案例：** 分享 Charles Schwab 在 Google Cloud 上實現業務持續性的實際案例，展示如何將這些原則付諸實踐。
*   **多層次彈性：** 強調在架構的每一層都應考慮彈性，以確保系統的整體可靠性。
*   **安全至上：** 強調安全性對於金融機構至關重要，並分享了 Charles Schwab 如何設計嚴格的安全策略。
*   **持續優化：** 強調業務持續性是一個持續優化的過程，需要不斷改進和調整。

## 2. 詳細內容

**架構設計**

*   **關鍵任務應用程式的定義：** 定義關鍵任務應用程式為需要極高可用性的應用程式，目標是達到五個九（99.999%）的可用性。
*   **多區域架構的類型：** 討論了兩種主要的多區域架構：
    *   **主動-備用：** 一個區域為主動區域，另一個區域為備用區域，在主動區域發生故障時進行故障轉移。
    *   **主動-主動：** 兩個區域都同時提供服務，並在一個區域發生故障時將流量重新導向到另一個區域。
*   **資料複製：** 強調在多區域架構中，資料複製至關重要，並介紹了幾種資料複製技術，例如非同步複製和雙區域儲存。
*   **服務選擇：** 根據服務的範圍（區域、多區域、全球），選擇合適的 Google Cloud 服務。
*   **容量規劃：** 強調容量規劃的重要性，包括穩定狀態、高峰負載和故障轉移情況下的容量規劃。
*   **備份：** 強調備份是最後一道防線，並鼓勵測試備份恢復過程。

**運營實踐**

*   **安全變更：** 強調安全變更管理的重要性，包括高品質的程式碼標準、持續建置和測試、以及漸進式發布。
*   **故障測試：** 鼓勵在預生產環境中進行故障注入，以測試應用程式的容錯能力。
*   **事件偵測與回應：** 強調快速偵測和回應事件的重要性，並介紹了 Google Cloud 提供的事件偵測工具，例如 Personalized Service Health (PSH) 和 Cloud Service Health (CSH)。

**Charles Schwab 的案例**

*   **背景：** 介紹 Charles Schwab 是一家領先的金融服務公司，管理著大量的客戶資產。
*   **遷移到雲端的原因：** 遷移到雲端的主要原因包括業務持續性、成長和可擴展性、以及業務賦能。
*   **準備工作：** 強調準備工作的重要性，包括多層次彈性、安全態勢、安全發布、可擴展性和可觀察性。
*   **多層次彈性：** 在架構的每一層都考慮彈性，包括應用程式層、資料層和基礎架構層。
*   **安全態勢：** 設計嚴格的安全策略，以滿足金融機構的監管要求。
*   **安全發布：** 遵循安全發布實踐，包括漸進式發布和遊戲日演練。
*   **可擴展性：** 實施自動擴展和排程擴展，以滿足不同的負載需求。
*   **可觀察性：** 利用 Google Cloud 的監控工具，建立統一的可觀察性儀表板。
*   **事件回應：** 建立完善的事件回應機制，以快速偵測和解決問題。
*   **經驗教訓：** 分享了從遷移到雲端的過程中獲得的經驗教訓，例如識別單點故障、了解雲端設計瓶頸和規劃維護視窗。

## 3. 重要結論

本次會議強調了在 Google Cloud 上建立強大業務持續性策略的重要性，並提供了架構設計和運營實踐方面的實用建議。Charles Schwab 的案例展示了如何將這些原則付諸實踐，並實現高可用性和容錯能力。會議還強調了安全性和持續優化的重要性，以確保關鍵任務應用程式的可靠運行。
```