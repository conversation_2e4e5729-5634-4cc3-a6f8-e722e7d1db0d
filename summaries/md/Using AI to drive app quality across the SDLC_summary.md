# Using AI to drive app quality across the SDLC
[會議影片連結](https://www.youtube.com/watch?v=1vTOG5QFOWY)
運用 AI 驅動軟體開發生命週期中的應用程式品質

## 1. 核心觀點

本次會議主要探討如何利用人工智慧（AI）來提升軟體開發生命週期（SDLC）中應用程式的品質。核心觀點包括：

*   **AI 不僅用於編寫程式碼，更用於構建更優質的應用程式：** 強調 AI 的應用不應僅限於提高程式碼產出速度，更應著重於提升應用程式的整體品質，包括穩定性、安全性及效能。
*   **利用 AI 代理（Agent）簡化複雜任務：** 介紹如何使用 AI 代理自動執行程式碼現代化、文件更新等任務，從而提高開發效率和應用程式品質。
*   **應用程式設計中心（Application Design Center）加速基礎設施部署：** 展示如何利用 AI 輔助工具設計和部署應用程式的基礎設施，簡化配置過程並遵循最佳實踐。
*   **CloudHub 和 Cloud Assist 簡化故障排除和成本優化：** 介紹如何使用 AI 驅動的工具快速診斷和解決雲端環境中的問題，並優化資源利用率以降低成本。
*   **Firebase Studio 快速構建全端 AI 應用程式：** 展示如何使用 Firebase Studio 快速原型化和構建全端 AI 應用程式，簡化開發流程並加速產品上市。

## 2. 詳細內容

*   **Gemini Code Assist 代理實例：**
    *   展示如何使用 Gemini Code Assist 代理將舊版 Java 應用程式從 Java 8 升級到 Java 21，並自動更新文件。
    *   強調 AI 代理不僅可以簡化程式碼遷移，還可以確保應用程式使用最新的安全修補程式和功能，從而減少技術債。
    *   介紹如何使用 Kanban 看板追蹤 AI 代理的進度，並與團隊成員協作。
*   **AI 輔助基礎設施設計與部署：**
    *   展示如何使用應用程式設計中心（Application Design Center）利用 AI 建議快速建立應用程式的基礎設施架構。
    *   強調 AI 可以簡化 Terraform 程式碼的編寫，並提供最佳實踐建議，從而加速基礎設施的部署。
    *   介紹如何使用 Gemini Cloud Assist 診斷和解決雲端環境中的問題，例如自動擴展 Cloud Run 實例以應對流量高峰。
*   **CloudHub 和 Cloud Assist 的應用：**
    *   展示如何使用 CloudHub 監控應用程式的效能和成本，並識別優化機會。
    *   介紹如何使用 Cloud Assist 快速診斷和解決問題，例如識別 Cloud SQL 實例的資源利用率不足，並建議調整機器規格以降低成本。
*   **Firebase Studio 快速構建 AI 應用程式：**
    *   展示如何使用 Firebase Studio 快速原型化和構建全端 AI 應用程式，例如為 Symbol B&B 建立一個客戶服務應用程式。
    *   強調 Firebase Studio 簡化了應用程式的開發流程，並提供了 AI 輔助工具，例如程式碼完成和錯誤診斷。
    *   介紹如何使用 Genkit 框架在 Firebase Studio 中整合 AI 模型，並建立具有 AI 功能的應用程式。
*   **Google 開發者計畫企業級方案：**
    *   介紹 Google 開發者計畫企業級方案，該方案整合了各種開發工具、環境和合作夥伴的整合，包括 Harness、GitLab、Snyk 和 JetBrains。
    *   強調該方案旨在簡化 AI 驅動的應用程式開發，並提供 25% 的折扣碼 NEXT25。

## 3. 重要結論

本次會議強調了 AI 在提升軟體開發生命週期中應用程式品質方面的潛力。透過利用 AI 代理簡化複雜任務、加速基礎設施部署、簡化故障排除和成本優化，以及快速構建全端 AI 應用程式，開發團隊可以提高效率、降低成本，並構建更優質的應用程式。會議鼓勵開發者積極探索和應用 AI 技術，以推動軟體開發的創新和進步。
