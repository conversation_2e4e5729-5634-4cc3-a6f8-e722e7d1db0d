# Boost productivity with custom solutions on Google Workspace
[會議影片連結](https://www.youtube.com/watch?v=nDxWRjwLnhc)
使用 Google Workspace 上的自定義解決方案提高生產力

## 1. 核心觀點

本次會議主要探討如何利用 Google Workspace 平台及其相關工具（如 AppSheet、Apps Script）來提升生產力，並介紹了 Google Workspace Marketplace、巨量資料分析、以及合作夥伴的成功案例。核心觀點包括：

*   **Google Workspace Marketplace 的應用：** 利用 Marketplace 上的應用程式擴展 Workspace 功能，並強調 Google 對應用程式的安全審查機制。
*   **AppSheet 的無程式碼開發：** 使用者無需編寫程式碼即可創建應用程式，解決特定工作流程問題，並強調 IT 部門對 AppSheet 應用程式的治理和控制。
*   **Google Sheets 巨集的使用：** 利用巨集自動化重複性任務，提高工作效率，並介紹了 Google Sheets 在連接 BigQuery、Oracle、Locker 等系統方面的強大功能。
*   **Apps Script 的靈活應用：** 使用 Apps Script 擴展 Workspace 功能，自動化工作流程，並結合 Gemini 進行程式碼生成和問題解決。
*   **NewBank 的案例分享：** 介紹 NewBank 如何利用 Gemini 和 Vertex AI 平台構建 AI 代理 Ask New，提升員工資訊檢索效率，解決資訊孤島問題。
*   **Codea 的合作夥伴案例：** 分享 Codea 如何協助客戶利用 Google Workspace 解決實際業務問題，包括使用 Apps Script 自動生成 Google Slides 簡報，以及使用 AppSheet 結合 Gemini API 進行健康安全評估。

## 2. 詳細內容

*   **Google Workspace Marketplace：**
    *   強調 Marketplace 上的應用程式經過 Google 的安全審查，確保使用者資料安全。
    *   介紹了 Gmail 和 Salesforce 的整合應用，利用 Gemini 協助處理資料。
    *   管理者可以控制使用者安裝應用程式的權限，建立企業私有的應用程式商店。

*   **AppSheet：**
    *   使用者可以無需編寫程式碼創建應用程式，適用於桌面和行動裝置。
    *   AppSheet 提供超過 50 個連接器，可以連接第三方系統和資料庫。
    *   IT 部門可以對 AppSheet 應用程式進行治理和控制。

*   **Google Sheets 巨集：**
    *   利用巨集自動化重複性任務，例如自動建立樞紐分析表。
    *   Google Sheets 運行在 WebAssembly 上，速度大幅提升。
    *   Google Sheets 可以連接 BigQuery、Oracle、Locker 等系統。
    *   可以使用 Gemini 將 VBA 巨集轉換為 Google Sheets 可用的程式碼。

*   **Apps Script：**
    *   Apps Script 是一種強大的語言，可以擴展 Workspace 功能，自動化工作流程。
    *   可以使用 Gemini 協助生成 Apps Script 程式碼。
    *   展示了利用 Apps Script 自動發送會議邀請和通知的範例。

*   **NewBank 的 Ask New 案例：**
    *   NewBank 利用 Gemini 和 Vertex AI 平台構建 AI 代理 Ask New，解決員工資訊檢索效率低下的問題。
    *   Ask New 可以連接 Google Drive、Confluence、GitHub 等多個資料來源。
    *   Ask New 可以根據使用者查詢的意圖，提供相關的資訊和解決方案。
    *   未來 Ask New 將擴展功能，例如自動預訂機票。

*   **Codea 的合作夥伴案例：**
    *   Codea 協助 DK 出版社利用 Apps Script 自動生成 Google Slides 簡報，節省大量時間和人力。
    *   Codea 協助 Southeast Water 遷移到 Google Workspace，並利用 AppSheet 結合 Gemini API 進行健康安全評估。
    *   Codea 強調 Google Workspace 合作夥伴可以提供專業知識和經驗，協助客戶更有效地利用 Google Workspace。

## 3. 重要結論

本次會議展示了 Google Workspace 平台及其相關工具在提升生產力方面的強大能力。透過 Google Workspace Marketplace、AppSheet、Google Sheets 巨集、Apps Script 等工具，使用者可以輕鬆地擴展 Workspace 功能，自動化工作流程，解決實際業務問題。NewBank 和 Codea 的案例分享也證明了 Google Workspace 在不同行業和場景下的應用價值。
