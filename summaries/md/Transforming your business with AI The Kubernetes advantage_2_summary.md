# Transforming your business with AI The Kubernetes advantage_2
[會議影片連結]()
Transforming your business with AI The Kubernetes advantage_2

## 1. 核心觀點

本次會議主要探討了 Kubernetes 在人工智慧（AI）領域的應用優勢，以及 Google Kubernetes Engine (GKE) 如何協助企業利用 Kubernetes 簡化 AI 工作負載的管理和部署。會議強調了 GKE 在擴展性、資源優化、開發效率和成本效益方面的關鍵作用，並介紹了多項新功能和合作夥伴關係，旨在幫助企業更有效地利用 Kubernetes 進行 AI 創新。

## 2. 詳細內容

*   **GKE 的 AI 領導地位：** GKE 憑藉其卓越的擴展性（支援高達 65,000 個節點的叢集）成為 AI 工作負載的首選平台。許多公司，如 Hubex 和 LiveX AI，都利用 GKE 來構建高度可擴展的 AI 應用程式，並實現更快的上市時間和更低的總擁有成本 (TCO)。

*   **GKE Autopilot 的優化：** GKE Autopilot 簡化了 Kubernetes 叢集操作，並透過完全託管的控制平面、自動節點管理和自動調整叢集資源大小來提高資源效率。新的 Autopilot 功能支援更快的 Pod 排程、擴展反應時間和容量調整，從而節省計算資源並降低成本。

*   **Gemini Cloud Assist 的開發效率：** Gemini Cloud Assist 透過 AI 驅動的輔助功能，簡化了應用程式生命週期管理，從初始基礎架構設計到故障排除和持續優化。新的 Gemini Cloud Assist Investigations 功能可幫助使用者更快地理解根本原因並解決問題，從而節省寶貴的開發時間。

*   **構建 AI 平台：** Kubernetes 已成為託管雲原生應用程式和微服務的事實標準。許多組織正在利用 Kubernetes 來構建自己的 AI 訓練和推論平台。GKE 提供了多種工具和功能，例如動態資源分配、Q and Jobset 和 Leader Worker Set，以簡化 AI 工作負載的管理。

*   **GKE 推論功能：** 為了應對在 Kubernetes 上部署推論的挑戰，GKE 推出了兩項新功能：GKE 推論快速入門和 GKE 推論閘道。GKE 推論快速入門可讓使用者選擇具有所需效能特性的 AI 模型，而 GKE 會配置正確的基礎架構、加速器和 Kubernetes 資源來匹配。GKE 推論閘道則透過智慧路由和負載平衡來降低服務成本、減少尾部延遲並提高吞吐量。

*   **與 AnyScale 的合作夥伴關係：** Google Cloud 與 AnyScale 合作，為 GKE 使用者提供 AnyScale 的 Ray Turbo，這是一個針對大規模 AI 進行優化的 Ray 執行時。Ray 是一個開源框架，可讓 AI/ML 工程師輕鬆地在 Python 中開發程式碼，並在 Kubernetes 叢集上彈性地擴展該程式碼。

*   **超級計算能力：** Cluster Director for GKE 是一個超級計算服務平台，現已正式推出。它透過自動修復基於其健康狀況的故障叢集，為大規模分散式工作負載提供卓越的效能和彈性。

## 3. 重要結論

GKE 正在成為企業利用 Kubernetes 進行 AI 創新的關鍵平台。透過其擴展性、資源優化、開發效率和成本效益，GKE 幫助企業簡化 AI 工作負載的管理和部署，並加速 AI 應用程式的開發和上市。新的 GKE 功能和與 AnyScale 的合作夥伴關係進一步增強了 GKE 在 AI 領域的領導地位，並為企業提供了更多工具和資源來構建和擴展其 AI 平台。
