# Implement a cloud detection strategy for effective incident response
[會議影片連結](https://www.youtube.com/watch?v=OnjHltE2fgU)
實施有效的事件響應雲端偵測策略

## 1. 核心觀點

本次會議主要探討在雲端環境中，如何建立有效的偵測策略以應對安全事件。核心觀點包括：

*   雲端環境的特殊性：傳統的端點偵測與回應（EDR）方法在雲端環境中存在局限性，需要考慮混合身份、臨時雲端資產以及控制平面日誌等因素。
*   威脅建模的重要性：了解針對雲端環境的特定威脅，是建立有效偵測策略的基礎。
*   日誌記錄的關鍵性：預設情況下，許多重要的雲端日誌並未啟用，需要手動配置。
*   善用雲端供應商提供的安全工具：例如 Google Cloud 的 Security Command Center 和 AWS 的 GuardDuty，可以快速獲得有價值的安全洞察。
*   持續迭代與成熟度提升：透過威脅建模、日誌記錄、偵測與回應等方面的持續改進，逐步提升雲端安全防護能力。

## 2. 詳細內容

*   **雲端環境的特殊性：**
    *   混合身份風險：本地環境的身份驗證資訊同步到雲端，可能導致本地環境的漏洞蔓延到雲端。
    *   臨時雲端資產：虛擬機器快速啟動和關閉，使得傳統的 EDR 工具難以追蹤。
    *   EDR 的局限性：雲端環境中存在許多無伺服器或容器環境，無法部署 EDR，需要依賴日誌記錄和偵測。
*   **常見的雲端威脅：**
    *   身份保護薄弱：釣魚、資訊竊取惡意軟體和令牌盜竊等手段導致憑證洩露。
    *   本地整合風險：本地環境與雲端環境的整合點可能成為攻擊者橫向移動的途徑。
    *   攻擊面擴大：公眾可存取的資源、洩露的密鑰和憑證等都擴大了攻擊面。
*   **事件案例研究：**
    *   一個具有專案所有者權限的服務帳戶的私鑰被意外發佈到公共程式碼儲存庫。
    *   攻擊者利用該密鑰存取 Google Cloud 專案，並從雲端儲存儲體中竊取資料。
*   **偵測工程方法：**
    *   從威脅行為者的意圖出發，確定需要偵測的內容，然後再確定需要收集的日誌。
    *   不要一開始就收集所有日誌，然後試圖從中建立偵測規則。
*   **威脅建模：**
    *   從合規性、滲透測試、產業級威脅情報到組織特定威脅情報，逐步提升威脅建模能力。
    *   使用 Microsoft STRIDE 模型等工具來識別潛在的威脅。
    *   從「邪惡使用者故事」的角度思考，模擬攻擊者的行為。
*   **日誌記錄與聚合：**
    *   區分控制平面日誌和資料平面日誌。
    *   控制平面日誌：記錄資源的管理操作，例如建立、更新和刪除。
    *   資料平面日誌：記錄資源的運行操作，例如讀取和寫入資料。
    *   啟用必要的資料平面日誌，以獲得更精細的偵測能力。
    *   將日誌集中到 SIEM 解決方案中進行分析。
*   **偵測與回應：**
    *   從使用供應商提供的現成偵測規則開始，逐步建立針對特定環境的自訂偵測規則。
    *   建立事件回應計畫，並定期演練。
    *   利用威脅情報來豐富偵測結果，並提高事件分級的準確性。
    *   進行紫隊演練，模擬真實的攻擊場景，以驗證偵測和回應能力。

## 3. 重要結論

本次會議強調了在雲端環境中建立有效偵測策略的重要性，並提供了實用的方法和建議。透過威脅建模、日誌記錄、偵測與回應等方面的持續改進，組織可以顯著提升雲端安全防護能力，並有效應對不斷演變的雲端威脅。
