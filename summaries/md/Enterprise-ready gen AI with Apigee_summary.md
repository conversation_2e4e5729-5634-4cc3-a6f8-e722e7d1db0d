# Enterprise-ready gen AI with Apigee
[會議影片連結](https://www.youtube.com/watch?v=9YWnYwE4kps)
Enterprise-ready gen AI with Apigee

## 1. 核心觀點

本次會議主要探討了在生成式 AI (Gen AI) 應用中，API 管理實踐的重要性，並介紹了 Apigee 如何提供相關的價值。核心觀點包括：

*   Gen AI 的服務挑戰與 API 管理的挑戰相似，例如分配、安全、延遲、成本、效能、可用性和可觀察性。
*   Apigee 作為 API 管理平台，提供 API 目錄、彈性運行時、開發者入口網站、分析和異常檢測、進階安全性、產品化和貨幣化以及應用程式整合等增值服務。
*   Apigee 支援伺服器發送事件 (Server-Sent Events, SSE)，允許對每個事件執行策略，例如配額檢查和內容審查。

## 2. 詳細內容

會議首先強調了 API 管理實踐對於 Gen AI 用例的重要性。儘管 Gen AI 帶來了新的可能性，但其服務挑戰與傳統 API 管理問題非常相似。這些挑戰包括：

*   **分配：** 如何有效地提供 Gen AI 服務，並控制其使用限制和存取權限。
*   **安全：** 如何確保大型語言模型 (LLM) API 不被濫用，並控制存取。
*   **延遲：** 如何快速提供服務。
*   **成本：** 如何控制成本，確保效能和高可用性。
*   **可觀察性：** 如何追蹤提示、回應和整體使用情況。

Apigee 作為 API 管理平台，提供了一系列解決這些挑戰的增值服務：

*   **API Hub：** 幫助組織整理 API，包括 LLM API。
*   **彈性運行時：** 允許在不同環境中運行和提供 Gen AI 服務。
*   **開發者入口網站：** 提供介面，讓使用者可以找到、使用 LLM API，並取得存取憑證。
*   **分析和異常檢測：** 追蹤使用情況。
*   **進階安全性：** 提供 API 和 AI 特定的安全介面，確保安全性並防止濫用。
*   **產品化和貨幣化：** 允許將 LLM 轉化為產品並進行貨幣化。
*   **應用程式整合：** 結合 API 用例和 iPaaS 風格的整合，以建立代理程式。

會議中舉例說明了 Emma 在使用 LLM 時可能遇到的挑戰，例如：

*   **Token 限制：** 控制提示對 LLM 的影響。
*   **模型路由：** 將請求路由到適當的模型。
*   **語義快取：** 快取後端回應，避免重複呼叫。
*   **進階分析：** 追蹤可觀察性資訊。
*   **模型故障轉移：** 在後端服務故障時重新路由請求。
*   **模型更新：** 輕鬆更新和部署新模型。

會議最後介紹了 Apigee 的伺服器發送事件 (SSE) 支援。LLM 使用 SSE 提供類似人類互動的串流體驗。Apigee 允許在每個事件的基礎上執行策略，例如配額檢查和使用 Model Armor 檢查是否有濫用內容。Apigee 中的事件流允許為每個傳入的事件獨立地應用策略。

## 3. 重要結論

Apigee 提供了一個全面的平台，用於管理和保護 Gen AI 應用程式。透過其 API 管理功能，組織可以解決 Gen AI 服務的關鍵挑戰，例如分配、安全、延遲、成本和可觀察性。Apigee 的 SSE 支援允許對串流 LLM 回應進行細粒度控制和策略執行，進一步增強了安全性。
