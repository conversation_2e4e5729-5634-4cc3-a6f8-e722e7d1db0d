# Beyond metrics Use DORA and AI to build a world-class DevEx
[會議影片連結](https://www.youtube.com/watch?v=_ONEL3KlwEg)
超越指標：使用 DORA 和 AI 打造世界一流的開發者體驗

## 1. 核心觀點

本次會議主要探討如何利用 DORA（DevOps Research and Assessment）的研究成果和人工智慧（AI）來提升開發者體驗（DevEx）。會議強調，儘管 AI 在程式碼編寫等方面帶來了效率提升，但軟體交付的整體效能並未隨之改善。因此，需要關注開發流程中的瓶頸，並利用 AI 來優化程式碼編寫之外的環節，例如測試、部署、安全性和成本控制等。此外，會議還強調了文化、工具和流程在提升開發者體驗中的重要性，並提倡透過消除不必要的負擔、縮短反饋週期和確保開發者安全感來改善開發環境。

## 2. 詳細內容

*   **DORA 指標與軟體交付效能：**
    *   DORA 研究已持續十多年，旨在幫助技術團隊和組織不斷改進。
    *   DORA 的核心是軟體交付效能，透過四個關鍵指標衡量：交付前置時間、部署頻率、變更失敗率和服務恢復時間。
    *   研究表明，吞吐量和穩定性並非相互制衡，而是相輔相成。更快的交付速度往往伴隨著更高的穩定性。
    *   軟體交付效能與組織績效和團隊成員的福祉息息相關。

*   **AI 的應用與影響：**
    *   組織對 AI 的重視程度日益提高，開發者也廣泛使用 AI 輔助日常工作，例如程式碼編寫、資訊摘要、程式碼解釋、程式碼優化和文件編寫。
    *   大多數受訪者認為 AI 提高了生產力，但對 AI 生成程式碼的信任度參差不齊。
    *   研究顯示，增加 AI 採用率可以提高工作效率、工作滿意度和文件品質，並降低技術債和程式碼複雜度。
    *   然而，令人意外的是，AI 的普及並未提升軟體交付的吞吐量和穩定性，反而出現了下降。

*   **Harness 與 DevOps 平台：**
    *   Harness 是一家 DevOps 平台供應商，致力於利用 AI 來現代化軟體交付流程。
    *   Harness 認為，僅僅關注程式碼編寫效率是不夠的，還需要優化程式碼編寫之後的各個環節，例如建構、安全、部署、強化和最佳化。
    *   Harness 提供一系列 AI 驅動的工具，例如測試智慧（Test Intelligence，減少測試時間）、持續驗證（Continuous Verification，降低變更失敗率）和雲端成本最佳化等。

*   **改善開發者體驗的關鍵：**
    *   開發者體驗的核心是消除不必要的負擔，讓開發者專注於解決問題和發揮創造力。
    *   縮短反饋週期，讓開發者能夠更快地看到自己的工作成果並獲得回饋。
    *   確保開發者的安全感，讓他們在部署程式碼時不會感到恐懼。
    *   透過自動化來簡化合規和安全流程，避免阻礙開發速度。
    *   文化、工具和流程是相輔相成的，需要共同努力才能提升開發者體驗。

## 3. 重要結論

會議強調，AI 雖然在程式碼編寫方面具有潛力，但要真正提升軟體交付效能和開發者體驗，需要更全面地考慮開發流程中的各個環節，並利用 AI 來優化這些環節。此外，組織還需要關注文化、工具和流程的建設，為開發者創造一個更高效、更安全、更愉悅的工作環境。
