# AI, cloud, and edge Unlocking innovation for retail and manufacturing

[會議影片連結](https://www.youtube.com/watch?v=Vxiay5s0-1w)
AI、雲端和邊緣運算：釋放零售和製造業的創新

## 1. 核心觀點

本次會議主要探討了 AI、雲端和邊緣運算如何推動零售和製造業的創新。核心觀點包括：

*   **邊緣運算的重要性：** 在網路不穩定或需要低延遲的環境下，邊緣運算至關重要，能確保應用程式的持續運作。
*   **Google Distributed Cloud (GDC) 的價值：** GDC 提供類似雲端的體驗，但在本地端運行，解決了網路連線問題，並提供合規性、工作負載隔離和可擴展性。
*   **AI 在零售和製造業的應用：** AI 可用於庫存管理、安全監控、自助結帳、店內分析等多個方面，提高效率和改善客戶體驗。
*   **合作夥伴生態系統：** Google 與 Toshiba、Standard AI、Ever seen 和 Intenseye 等合作夥伴合作，提供各種 AI 解決方案。
*   **標準化的重要性：** 標準化有助於加速部署、降低成本，並確保系統的未來適用性。

## 2. 詳細內容

*   **Google Distributed Cloud (GDC)：**
    *   GDC 是一種雲端服務，實際部署在客戶的本地環境中。
    *   Google 負責安裝、維護、監控和更新平台。
    *   客戶可以根據需求選擇硬體配置，例如 vCPU 數量、冗餘配置和 GPU 加速。
    *   GDC 具有生存模式，即使與雲端的連線中斷，也能保持應用程式的運行。
    *   GDC 通過 SOC、ISO、PCI DSS 和 HIPAA 等合規性測試。
    *   GDC 提供工作負載隔離功能，使用容器沙箱、VM 沙箱和完全功能的輔助網路，以隔離不同安全級別的應用程式。
    *   GDC 提供 Fleet Packages 功能，簡化應用程式在多個地點的部署和管理。

*   **Fleet Packages 示範：**
    *   以 Smart Inventory AI 應用程式為例，展示如何使用 Fleet Packages 將應用程式部署到 1000 家商店。
    *   將商店分為實驗室、試點和生產階段，並使用標籤進行標記。
    *   使用 Git 管理應用程式的配置，並使用 Cloud Build 構建 OCI 映像。
    *   使用 Fleet Packages 定義部署策略，例如一次部署一個或多個商店。

*   **AI 解決方案：**
    *   **Toshiba：** 提供基於 GDC 的銷售點系統，減少員工培訓時間並提高自助結帳的交易成功率。
    *   **Standard AI：** 提供基於 GDC 的商店分析解決方案，將實體空間數位化，並提供近乎即時的數據。
    *   **Ever seen：** 提供基於 GDC 的自助結帳解決方案，減少錯誤率並保證投資回報。
    *   **Intenseye：** 提供基於 GDC 的工人安全解決方案，監控工作場所並預防工傷事故。

*   **客戶案例：**
    *   **Genuine Parts Company (GPC)：** 使用 GDC 運行商店內的應用程式，提高價格管理和庫存管理的效率，並改善商店運營。
    *   **German Edge Cloud (GEC)：** 提供基於 GDC 的製造業軟體解決方案，優化生產線的效率。

## 3. 重要結論

本次會議強調了 AI、雲端和邊緣運算在零售和製造業中的變革性潛力。Google Distributed Cloud 提供了一個強大的平台，使企業能夠在本地端運行雲端原生應用程式，解決網路連線問題，並滿足合規性要求。通過與合作夥伴的合作，Google 正在為零售和製造業提供各種 AI 解決方案，幫助企業提高效率、改善客戶體驗並確保工人安全。標準化是成功的關鍵，企業應致力於建立標準化的平台和流程，以加速部署、降低成本並確保系統的未來適用性。
