# Innovation with speed Journey of a large and complex data warehouse to Cloud
[會議影片連結](https://www.youtube.com/watch?v=CwOF7LgUHdg)
以速度創新：大型複雜資料倉儲遷移至雲端的旅程

## 1. 核心觀點

本次會議主要探討了埃森哲（Accenture）與 Google Cloud、Teradata 合作，協助匯豐銀行（HSBC）將大型複雜的 Teradata 資料倉儲快速遷移至雲端的案例。核心觀點包括：

*   AI 發展迅速，但底層數據往往位於複雜的本地資料倉儲中，需要現代化。
*   傳統的資料重構耗時，本次採用了「直接遷移」（Lift and Shift）的方法，大幅縮短了遷移時間。
*   透過與 Teradata 和 Google Cloud 的合作，實現了零中斷的遷移。
*   遷移至雲端為匯豐銀行奠定了基礎，使其能夠充分利用 AI 的各種應用場景。
*   本次合作帶來了立即的成本節省、快速遷移、零中斷，以及加速創新的成果。

## 2. 詳細內容

埃森哲的 Praveen Garoor 介紹了本次專案的背景，指出許多客戶都面臨著類似的挑戰：AI 發展迅速，但支援 AI 的數據卻位於複雜的本地資料倉儲中，需要進行現代化。匯豐銀行擁有一個約 160TB 的大型資料庫，該資料庫基於本地 Teradata，採用單體架構，擴展性差且缺乏靈活性。

為了解決這個問題，團隊沒有採用傳統的資料重構方法，而是選擇了「直接遷移」（Lift and Shift）的方法，將資料從本地直接遷移到雲端。Teradata 的 Ben 介紹了 Teradata Vantage on Cloud 如何提供良好的基礎設施，以支援從本地到雲端的直接遷移。

本次遷移在六個月內完成，並且實現了零中斷。匯豐銀行的 Mike 介紹了遷移帶來的益處，包括能夠停用現有的本地資料倉儲，並為充分利用 AI 奠定基礎。Google Cloud 的 Raman 則強調了 Google 如何與第一方系統以及 Teradata 等第三方 ISV 無縫協作，以幫助客戶滿足其需求，並充分利用 AI 的力量。

會議還討論了本次合作帶來的成果，包括立即的成本節省、快速遷移、零中斷，以及加速創新，從而大幅優化了匯豐銀行的現有環境。遷移至雲端後，匯豐銀行可以充分利用 Google 發布的各種功能，並在 AI 和 Gen-AI 領域進行創新。

與會者積極參與討論，詢問了關於遷移過程中的挑戰，以及如何將本地數據大規模遷移到雲端。Mike 強調了團隊之間的協作，並鼓勵其他企業複製他們的成功經驗。

## 3. 重要結論

本次會議傳達的主要訊息是，無論客戶處於旅程的哪個階段，都可以透過 Google 的技術和埃森哲、匯豐銀行以及 Teradata 等合作夥伴的協助，從雲端遷移中受益。本次匯豐銀行的案例證明了透過快速遷移和創新，可以為企業帶來顯著的價值。
