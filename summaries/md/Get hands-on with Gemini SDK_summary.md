# Get hands-on with Gemini SDK
[會議影片連結](https://www.youtube.com/watch?v=563bAOztX8g)
Gemini SDK 實作

## 1. 核心觀點

本次會議主要介紹 Gemini 及 Gemini SDK，並展示如何使用 Gemini SDK 構建強大的 AI 應用程式。會議涵蓋了 Gemini 模型家族的介紹、Vertex AI 平台的功能，以及 Gemini SDK 的使用方法和實際應用範例，特別是客戶支援場景。核心觀點包括：

*   Gemini 模型家族包含不同模型，針對不同使用案例進行優化，開發者應根據需求選擇合適的模型。
*   Vertex AI 提供完整的 AI 平台，涵蓋模型、工具和基礎設施，協助開發者構建企業級 AI 應用程式。
*   Gemini SDK 提供統一的介面，簡化了從 AI Studio 到 Vertex AI 的開發流程，加速 AI 應用程式的開發。
*   Gemini SDK 支援多種程式語言，包括 Python、Go、Java 和 Node.js，方便不同背景的開發者使用。
*   Gemini 具備多模態輸入、長上下文視窗、結構化輸出、原生工具使用和即時 API 等強大功能。

## 2. 詳細內容

*   **Gemini 模型家族：**
    *   Gemini 2.0 Pro：適用於需要強大思考能力和解決複雜問題的場景，例如編碼、數學、科學和研究。
    *   Gemini 2.0 Flash：適用於日常任務，針對高效能、快速和即時串流進行優化。
    *   Gemini 2.0 Flash (smallest)：適用於需要最大效率和最低延遲的場景，也是最經濟實惠的選擇。
*   **Vertex AI 平台：**
    *   提供 Gemini 和其他 200 多個模型，滿足各種使用案例的需求。
    *   提供模型建構器、代理程式建構器等工具，涵蓋 AI 開發的整個生命週期。
    *   提供穩健的服務環境，支援 AI 應用程式在雲端大規模執行。
    *   提供企業級安全、可擴展性和可靠性。
*   **Gemini SDK：**
    *   作為 Google AI Studio 和 Vertex AI 之間的橋樑，簡化開發流程。
    *   提供 Python、Go、Java 和 Node.js 等多種程式語言的支援。
    *   提供統一的介面，方便開發者使用 Gemini 的各種功能。
*   **客戶支援範例：**
    *   展示如何使用 Gemini SDK 構建 AI 代理程式，協助客戶尋找合適的產品。
    *   展示 Gemini 的多模態輸入能力，例如分析圖片並根據圖片內容回答問題。
    *   展示 Gemini 的原生工具使用能力，例如連接到 Google 搜尋和企業系統，獲取最新資訊。
    *   展示 Gemini 的即時 API 能力，實現雙向即時多模態互動。
*   **程式碼範例：**
    *   提供 Jupyter Notebook 範例，展示如何使用 Gemini SDK 實現客戶支援場景中的關鍵步驟。
    *   展示如何設定專案、匯入函式庫、建立客戶端物件、指定模型 ID、準備模型輸入和發送請求。
    *   展示如何使用結構化輸出功能，確保模型輸出符合特定格式。
    *   展示如何使用原生工具使用功能，連接到外部系統獲取資料。
    *   展示如何使用即時 API 功能，實現即時對話。
*   **未來發展藍圖：**
    *   Go SDK 已正式發布 (GA)。
    *   Java 和 Node.js SDK 將於五月發布。
    *   未來將推出更多令人興奮的功能。

## 3. 重要結論

Gemini 和 Gemini SDK 為開發者提供了強大的工具，可以構建創新的 AI 應用程式。Gemini SDK 的統一介面、多語言支援和強大功能，簡化了 AI 開發流程，加速了 AI 應用程式的部署。透過本次會議的介紹和範例，開發者可以更好地了解 Gemini 和 Gemini SDK，並將其應用於實際專案中。
