# From weeks to minutes Build full-stack, agentic apps with Firebase Studio
[會議影片連結](https://www.youtube.com/watch?v=_kLVjxLZ0NQ)
從數週到數分鐘，使用 Firebase Studio 構建完整的、代理式的應用程式

## 1. 核心觀點

Firebase Studio 是一個整合且可擴展的工作區，旨在幫助開發者更快速地構建應用程式。它支援構建、運行和管理各種應用程式，包括 Web 應用程式、跨平台行動應用程式和後端服務。Firebase Studio 的目標是讓開發者能夠在 30 秒內將想法轉化為可運作的應用程式。AI 在應用程式構建中扮演核心角色，開發者可以根據需要選擇使用多少 AI 功能。

## 2. 詳細內容

Firebase Studio 提供了一個使用者友善的介面，開發者可以使用自然語言和多模態輸入來構建應用程式。使用者可以透過提示指定應用程式的功能，並根據藍圖進行修改。一旦準備就緒，點擊按鈕即可開始構建應用程式。在構建過程中，開發者可以透過 Gemini 驅動的聊天介面進行互動和迭代，而無需過多考慮技術細節。

對於不希望使用 AI 或希望深入研究現有專案的開發者，Firebase Studio 提供了一個完整的專業工作區，支援使用各種程式語言和框架構建應用程式。開發者可以自訂開發環境，甚至配置 VM。此外，Firebase Studio 還支援從 GitHub、BitLab 和 Bitbucket 開啟現有專案，或從本地上傳 ZIP 檔案。

Firebase Studio 提供了一系列範本，涵蓋前端、後端和行動應用程式，這些範本由 Firebase 團隊和社群共同構建。AI 功能由 Gemini 提供支援，開發者可以選擇最新的 Gemini 模型。這些功能是完全代理式的，能夠感知整個程式碼庫，並支援修改單個檔案、單行程式碼或整個專案。Gemini Code Assist agents 也將在 Firebase Studio 介面中提供。

Firebase Studio 強調協作功能，開發者可以輕鬆地與同事分享工作區，並進行即時協作。Firebase Studio 是完全基於 Web 的，只需要瀏覽器和網路連線即可使用。它基於 VS Code，提供了一個熟悉且強大的編碼環境。即使在瀏覽器中運行，Firebase Studio 也提供了完整的雲端 VM，具備管理檔案系統、使用終端和安裝套件所需的所有功能。

Firebase Studio 提供了預先配置的環境和深度自訂功能，並透過 NixOS 技術實現。AI 輔助貫穿整個端到端流程，包括構建、部署和監控應用程式。Firebase Studio 提供了即時預覽功能，包括 Web 預覽和 Android 模擬器，無需任何配置。它還提供了與 Google 服務的深度整合，只需點擊幾下或透過 AI 輔助，即可使用最佳的 Google 服務來解決問題。

## 3. 重要結論

Firebase Studio 是一個專業工具，適用於所有開發者，無論是 AI 優先、程式碼優先、前端或後端開發。它提供了一個整合且可擴展的工作區，旨在幫助開發者更快速地構建應用程式，並提供強大的協作功能和與 Google 服務的深度整合。
