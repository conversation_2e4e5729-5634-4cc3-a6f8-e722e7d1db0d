Empower small IT teams to harness gen AI with Google Cloud databases

[會議影片連結](https://www.youtube.com/watch?v=Li57kMm51zc)
運用 Google Cloud 資料庫，賦能小型 IT 團隊掌握生成式 AI

## 1. 核心觀點

本次會議主要展示如何利用 AlloyDB AI 和應用程式資料來構建生成式 AI 應用程式。重點在於利用向量搜尋和自然語言處理能力，簡化應用程式開發流程，並提升使用者體驗。AlloyDB AI 透過內建的安全機制和高效能的向量搜尋，讓開發者能夠更專注於應用程式本身，而無需耗費大量資源處理底層的資料庫管理和 AI 模型整合。

## 2. 詳細內容

會議首先展示了一個名為 Symbol Shops 的線上護膚和化妝品公司的搜尋應用程式。該應用程式利用向量搜尋技術，能夠理解使用者搜尋詞彙的語義，並返回相關的產品結果，即使這些結果不包含使用者輸入的確切關鍵字。例如，搜尋「預防老化」可以返回 SPF 防曬產品，因為 SPF 具有預防老化的功效。

此外，該應用程式還展示了個性化功能，允許使用者根據自己的偏好（例如，純素產品、適合油性皮膚的產品）來篩選搜尋結果。這種個性化功能是透過 SQL 的 WHERE 子句實現的，展示了在 PostgreSQL 中使用向量搜尋的強大功能。

AlloyDB AI 在 PostgreSQL 的向量搜尋基礎上，進一步增強了掃描索引（scan index）功能。掃描索引是 Google 搜尋演算法在 YouTube 和 Google 搜尋中的應用，它能夠提供更高品質的搜尋結果，並具有更佳的效能。與標準 PostgreSQL 上的 HNSW 索引相比，掃描索引的索引建立速度快 10 倍，向量搜尋查詢速度快 4 倍，過濾向量搜尋速度快 10 倍，並且通常使用約 3 倍少的記憶體。

會議還介紹了掃描索引的三個關鍵增強功能：索引自動維護（index auto maintenance）、查詢召回評估器（query recall evaluator）和自適應過濾（adaptive filtration）。這些功能分別用於確保索引適應不斷變化的資料、輕鬆評估向量搜尋的品質，以及確保 PostgreSQL 規劃器以正確的順序應用過濾器，從而實現最佳效能。

除了向量搜尋，會議還介紹了模型端點管理（model endpoint management）功能，該功能允許使用者直接在資料庫中生成嵌入（embeddings），利用 Vertex AI 的文字嵌入模型、Google DeepMind 的 Gemini 嵌入模型和多模態模型嵌入。使用者還可以使用來自 OpenAI 和 HuggingFace 等供應商的嵌入模型。此外，該功能還整合了 LLM 和重新排序 API。

會議的後半部分展示了自然語言處理（natural language）能力。在同一個應用程式中，使用者可以使用聊天機器人以自然語言提問，並從資料庫中獲取答案。從應用程式的角度來看，只需要編寫一個簡單的 SQL 查詢，AlloyDB AI 就能夠處理使用者提出的任何問題，並在幕後生成相應的 SQL 語句。AlloyDB AI 的 API 自然語言處理功能提供內建的安全機制，確保使用者只能存取他們應該看到的資料。它還使用來自資料庫資料日誌和其他位置的資料，以及意圖澄清和消除歧義功能，以解決自然語言的細微差別。

總體架構非常簡單：主要使用資料來支援應用程式，AlloyDB AI 位於頂層。AlloyDB AI 處理向量搜尋和自然語言處理，因此您可以專注於構建應用程式本身，而資料庫將處理大部分工作。這意味著您能夠構建這些豐富的體驗，而無需耗盡大量資源。

## 3. 重要結論

AlloyDB AI 透過其向量搜尋和自然語言處理能力，簡化了生成式 AI 應用程式的開發流程。它提供高效能、安全且易於使用的工具，讓開發者能夠更專注於應用程式本身，而無需耗費大量資源處理底層的資料庫管理和 AI 模型整合。這使得小型 IT 團隊也能夠利用生成式 AI 的力量，構建更智慧、更具吸引力的應用程式。
