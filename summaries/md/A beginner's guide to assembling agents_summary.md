# A beginner's guide to assembling agents
[會議影片連結](https://www.youtube.com/watch?v=T96ArcQTek0)
組裝代理的初學者指南

## 1. 核心觀點

本次會議主要介紹了 Agent Space 及其在組裝代理方面的應用。Agent Space 作為搜尋和代理的中心樞紐，旨在幫助使用者釋放代理的潛力。透過 Agent Space，使用者可以連接到各種數據源，利用 Gemini 模型的能力，並在安全合規的框架內協調多個代理，自動化複雜任務。會議還介紹了兩個 Agent Space 內建的強大代理：Deep Research 和 Idea Generation，並展示了如何使用無程式碼代理設計器和 Agent Developer Kit (ADK) 來構建自定義代理。

## 2. 詳細內容

Agent Space 的核心功能包括：

*   **數據連接：** 連接企業內外部的各種數據源，為代理提供知識基礎。
*   **Gemini 模型整合：** 利用 Google 最先進的 Gemini 模型，使代理能夠思考、行動和自動化複雜任務。
*   **代理協調：** 支援多個代理之間的協作，實現更複雜的工作流程。
*   **安全合規：** 提供安全且符合法規的環境，確保數據安全和隱私。

會議重點介紹了兩個內建代理：

*   **Deep Research：** 一個複雜的多步驟研究代理，可以從內部企業數據和外部網路數據中合成資訊。它使用智慧迴圈生成問題，並迭代該迴圈直到達到目標。Deep Research 也可作為 API 使用，以增強現有代理或應用程式。
*   **Idea Generation：** 一個多代理構思流程，旨在推動企業的創新。它受到 Google 研究技術的啟發，該技術曾用於為生物醫學研究生成假設。它使用類似西洋棋的競賽來對想法進行排名，並允許使用者協作以完善這些想法。

會議還介紹了兩種構建自定義代理的方法：

*   **無程式碼代理設計器：** 允許員工透過簡單的介面構建代理，無需編寫程式碼。它提供了 30 多種工具和操作，以及與企業數據的整合。
*   **Agent Developer Kit (ADK)：** 一個開源框架，用於定義、測試和部署複雜的代理。Agent Space 提供對 ADK 的原生支援，簡化了代理的整合、協調和 UI 渲染。

## 3. 重要結論

Agent Space 提供了一個強大的平台，用於構建、部署和管理代理。透過其數據連接、Gemini 模型整合、代理協調和安全合規功能，Agent Space 能夠幫助企業自動化複雜任務、推動創新並釋放數據的潛力。無程式碼代理設計器和 ADK 提供了靈活的選項，允許不同技能水平的使用者構建自定義代理，以滿足其特定需求。
