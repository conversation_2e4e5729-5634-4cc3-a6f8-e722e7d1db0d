# Developer's jumpstart guide to Vertex AI
[會議影片連結](https://www.youtube.com/watch?v=irnZxftBSU4)
Developer's jumpstart guide to Vertex AI

## 1. 核心觀點

本次會議主要探討了如何利用 Vertex AI 快速啟動開發者專案，並展示了 Live API 的多種應用場景，包括客戶服務、軟體教學以及與 AR 眼鏡的整合。會議強調了 Live API 在解決複雜軟體操作問題上的潛力，以及生成媒體在影片編輯方面的應用。此外，會議還分享了程式碼生成的技巧，並介紹了可用的資源和範例。

## 2. 詳細內容

- **Live API 應用展示：**
    - 客戶服務：展示了使用 Live API 進行客戶支援的應用，但由於網路問題，演示效果不佳。
    - 軟體教學：展示了使用 Live API 輔助操作 Blender 等複雜軟體，透過螢幕分享讓 AI 提供即時指導，解決使用者在複雜介面中遇到的操作難題。
    - AR 眼鏡整合：展示了與 Snap Spectacles 整合的 Live API，實現了 Tony Stark 式的 AR 體驗，使用者可以透過眼鏡與 AI 互動，並調用特定工具與 3D 環境互動。

- **Live API 開發建議：**
    - 建議開發者熟悉 PCM 音訊格式，並分享了用於除錯 PCM 音訊的工具。

- **Agents 討論：**
    - 定義 Agent 為具有工具並能決定何時使用這些工具的系統。
    - 強調 Agent 應解決實際問題，而不僅僅是執行簡單任務。
    - 介紹了 Agent Development Kit (ADK) 等 Agent 開發框架。
    - 建議開發者在構建 Agent 時，除了聊天介面外，還應考慮其他觸發方式，例如物理按鈕或 Git commit。
    - 推薦使用 N8N 等視覺化工具構建 Agent 工作流程。

- **生成媒體討論：**
    - 介紹了 Vertex AI 的 Vio 模型，可用於文字生成影片，並展示了生成小狗在球池中玩耍影片的範例。
    - 展示了使用 Imagine 模型編輯圖片背景，以及使用 Image2Video 功能將圖片轉換為影片的應用。
    - 介紹了 Vio 模型的新功能，包括相機預設、影片擴展以及指定首尾幀生成影片。

- **程式碼生成技巧：**
    - 分享了使用 "白金範例" 方法生成程式碼的技巧，即利用現有程式碼範例作為基礎，引導模型生成風格一致的程式碼。

- **資源分享：**
    - 介紹了包含大量範例程式碼和 Notebook 的 GitHub 儲存庫，鼓勵開發者貢獻程式碼。

- **Snap Spectacles 演示：**
    - 展示了 Snap Spectacles 與 Gemini 整合的 AR 應用，使用者可以透過眼鏡與 AI 互動，獲取資訊和協助。

## 3. 重要結論

本次會議涵蓋了 Vertex AI 的多個重要方面，包括 Live API 的應用、Agent 開發、生成媒體以及程式碼生成技巧。會議強調了 Live API 在解決複雜問題和提升使用者體驗方面的潛力，並鼓勵開發者積極探索和貢獻 Vertex AI 生態系統。Snap Spectacles 的演示展示了 AR 技術與 AI 結合的未來應用前景。
