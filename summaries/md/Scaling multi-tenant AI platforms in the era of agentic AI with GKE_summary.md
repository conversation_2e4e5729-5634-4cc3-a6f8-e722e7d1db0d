# Scaling multi-tenant AI platforms in the era of agentic AI with GKE
[會議影片連結](https://www.youtube.com/watch?v=QUKxWw3c6lc)
在 Agentic AI 時代，使用 GKE 擴展多租戶 AI 平台

## 1. 核心觀點

本次會議主要探討在 Kubernetes 和 GKE 環境下，如何有效地擴展多租戶 AI 平台，以支援日益增長的 AI 模型和 Agent 應用。核心觀點包括：

*   **多模型是未來趨勢：** 企業需要能夠部署和管理多種模型，以滿足不同的應用場景和需求。
*   **NVIDIA NIM 的優勢：** NVIDIA Inference Microservices (NIM) 提供優化的模型部署方案，可在 NVIDIA GPU 上實現卓越的效能。
*   **GKE 的角色：** GKE 提供靈活且可控的平台，用於部署和管理 AI 模型，並支援多租戶環境。
*   **Agent 的興起：** AI Agent 正在快速發展，企業需要為 Agent 的部署和管理做好準備。
*   **自動化和治理的重要性：** 在多模型環境中，自動化部署流程、成本控制和資源治理至關重要。
*   **多集群管理：** 隨著規模擴大，需要考慮跨多個集群管理模型和 Agent。

## 2. 詳細內容

*   **引言：**
    *   Brandon Royal (Google GKE 團隊產品經理) 強調 GKE 團隊致力於普及 AI，協助客戶部署 AI 模型和 Agent，並利用不斷發展的開放生態系統。
    *   提到 Kubernetes 已經發展十年，越來越多的 AI 模型被部署在 Kubernetes 上。
    *   現在的重點是規模化，如何部署多個模型並利用 NVIDIA 的技術堆疊來提高效率。

*   **AI 模型和 Agent 的快速發展：**
    *   AI 模型的創新速度驚人，例如 Llama 4、DeepSeq、Gemma 3 等。
    *   企業現在擁有眾多模型選擇，可以構建強大的 Agent。
    *   KPMG 的調查顯示，51% 的組織正在探索構建 AI Agent，37% 已經在試點。

*   **多模型平台的挑戰：**
    *   選擇合適的模型並不容易，需要考慮準確性、速度、硬體要求、授權和微調等因素。
    *   GKE 團隊正在努力提供最佳平台，以支援多模型部署、管理和治理。
    *   重點包括卓越的性價比、自動化生態系統和智慧路由。

*   **多模型部署的挑戰：**
    *   在多租戶平台上服務多個模型需要仔細考慮。
    *   Agent 架構包括工具呼叫服務、記憶體和上下文，以及大型語言模型。
    *   Kubernetes 在協調核心組件方面表現出色，但路由到多個模型以及在多個大型語言模型之間分配負載更具挑戰性。
    *   需要考慮資源利用率、請求長度不一致以及水平擴展等因素。

*   **GKE 的解決方案：**
    *   GKE 專注於加速模型導入和優化，提供推理快速入門配方，根據吞吐量和延遲目標提供配置建議。
    *   GKE 推出了推理優化閘道，利用自定義指標（例如 KV 快取利用率）來智慧地路由請求，從而提高效能。
    *   展示了在 GCP 控制台中部署 Llama 3 的示範，強調了簡化的部署體驗。

*   **NVIDIA NIM 的介紹：**
    *   Abhishek (NVIDIA 企業團隊產品經理) 介紹了 NVIDIA Inference Microservices (NIM)，旨在解決企業客戶在模型部署方面的痛點，例如網域 IP 洩漏和從 POC 到生產的轉換。
    *   NIM 提供了一種優化的方式，可以在 NVIDIA GPU 上以更高的效能部署大型語言模型和生成式 AI 模型。
    *   NIM 是容器化的模型，可以部署在 Kubernetes 集群上，並包含預先封裝的優化函式庫。
    *   NVIDIA 與原始模型提供者合作，提供 NIM 化模型，這些模型可以在 ai.nvidia.com 上的 API 目錄中找到。
    *   LiveX 的測試表明，與開源替代方案相比，NIM 在 Llama 模型上的效能提高了 6.1 倍。
    *   展示了在 GKE 上部署 NIM 的示範，強調了簡化的部署流程。
    *   NVIDIA 還推出了 Nemo 微服務，用於微調用例，以及 AI 藍圖，用於特定行業的應用。

*   **多集群管理：**
    *   Brandon 討論了部署多個 Agent 和模型時面臨的挑戰，包括資源擴展、整體優化和可獲取性。
    *   推出了多集群協調器，這是一種開源功能，允許將工作負載路由到多個集群，從而實現更好的資源利用率和效能。
    *   多集群協調器利用 GKE 中可用的底層 Fleet Management。
    *   還介紹了自定義計算類別，允許使用者定義計算的優先順序，以及 FlexStart，用於在特定時間段內請求資源。

*   **MLB 的案例研究：**
    *   Jeremy Shulman (美國職棒大聯盟解決方案工程高級總監) 分享了 MLB 如何在 GKE 上部署 Agent 的案例。
    *   MLB 使用 Agent 來主動響應網路問題，並為其他營運團隊提供存取和協作。
    *   MLB 的目標是縮短從想法到實現的時間，以及從問題到解決的時間。
    *   MLB 使用 GKE 部署 LLM，並使用 Dialogflow 構建網路營運 Agent 的自然語言介面。
    *   展示了連接 Agent 的示範，該 Agent 可以自動執行網路問題的診斷和修復，以及網路營運 Agent 的示範，該 Agent 允許使用者使用自然語言來執行網路操作。
    *   Jeremy 強調了與 Google 的戰略關係、產品上市時間以及可擴展性和成本效益。

*   **總結：**
    *   Brandon 重申，未來是多模型的，GKE 致力於幫助組織更快地將 Agent 和模型推向市場。
    *   他強調了擁抱生態系統和規劃租戶的重要性。

## 3. 重要結論

本次會議強調了在 Agentic AI 時代，使用 GKE 擴展多租戶 AI 平台的重要性。NVIDIA NIM 提供了一種優化的模型部署方案，而 GKE 提供了一個靈活且可控的平台，用於部署和管理 AI 模型。隨著 AI Agent 的快速發展，企業需要為 Agent 的部署和管理做好準備，並考慮自動化、治理和多集群管理等因素。MLB 的案例研究展示了如何在實際中應用這些技術來解決實際問題。
