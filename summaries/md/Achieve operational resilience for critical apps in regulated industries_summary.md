# Achieve operational resilience for critical apps in regulated industries
[會議影片連結](https://www.youtube.com/watch?v=2vZ4NqXj_WA)
在受監管產業中，實現關鍵應用程式的營運韌性

## 1. 核心觀點

本次會議主要探討芝加哥商業交易所集團（CME Group）在 Google Cloud 上實現關鍵應用營運韌性的經驗。重點包括：

*   CME Group 與 Google Cloud 的合作歷程，以及從傳統地端部署轉移到雲端解決方案的過程。
*   營運韌性的原則，包括可觀測性、資料韌性以及變更管理。
*   在受監管產業中，如何透過雲端技術和 SRE（網站可靠性工程）實踐來確保關鍵應用程式的穩定性和可靠性。

## 2. 詳細內容

*   **CME Group 簡介：** CME Group 是全球最大的衍生品交易所，提供市場、清算和資料服務。該公司一直致力於創新，包括成為首家全電子化交易所，並於 2021 年與 Google Cloud 建立合作夥伴關係，成為首批完全遷移到 Google Cloud 的大型交易所和清算機構之一。

*   **監管合規：** 作為一家金融機構，CME Group 受到多個監管機構的監管。在現代化其技術堆疊時，必須確保符合監管流程，並以具韌性和穩健的方式運行其市場、清算和資料平台。

*   **可觀測性的重要性：** CME Group 的交易量持續增長，尤其是在市場波動或重大新聞事件期間。為了支持這些交易量，需要構建可擴展、穩健且具韌性的應用程式。可觀測性是實現這一目標的關鍵，沒有它，就無法以所需的規模運營。

*   **營運韌性的指導原則：** CME Group 遵循多項指導原則，包括可觀測性、可用性、可靠性、韌性、速度、安全性和效能。在遷移到 Google Cloud 的過程中，這些原則是平台基礎的基石。

*   **可觀測性的三大要素：** 可觀測性的目標是能夠回答關於系統內部運作的問題。實現這一目標的三個要素是監控（Metrics）、日誌（Logs）和追蹤（Traces）。這些要素可以組合成各種工具，以滿足不同角色的需求，例如維運人員、開發人員、平台管理員、SRE、業務分析師和安全分析師。

*   **Google Cloud Observability 的發展：** Google Cloud Observability 在過去一年中進行了大量投資，推出了 100 多項新功能，旨在將日誌、指標和追蹤整合到單一視窗中。這些投資旨在滿足像 CME Group 這樣的客戶的特定使用案例。

*   **儀表板（Dashboards）的應用：** CME Group 利用開箱即用的服務儀表板、專案儀表板、正常運行時間檢查和健康指標。此外，還使用 Google Cloud API 從平台獲取更深入的洞察，並將這些訊號與來自地端應用程式的訊號合併，以建立單一統一的儀表板。

*   **日誌生命週期：** 日誌生命週期包括收集、擷取、路由、儲存和分析。OpenTelemetry 是一個強大的工具，可用於擷取和收集日誌資料。日誌路由器可用於過濾、選擇和路由日誌資料到不同的目的地，例如 Cloud Logging、Cloud Storage、BigQuery 或 Pub/Sub。

*   **資料常駐性和資料韌性：** CME Group 在 Ohio 和 Iowa 兩個區域使用 Google Cloud，並利用多個可用區。這種多區域、多可用區的架構有助於提高韌性。基礎設施即程式碼消除了伺服器採購的等待時間，使開發人員能夠快速部署和擴展應用程式。

*   **變更管理：** CME Group 採取了分階段的方法遷移到 Google Cloud。這種方法使他們能夠從早期遷移的應用程式中獲得回饋，並自動化最佳實務。與 Google 的協作、組織願景、基礎設施即程式碼以及 SRE 文化的採用也是成功遷移的關鍵因素。

## 3. 重要結論

CME Group 透過與 Google Cloud 的合作，成功地將其關鍵應用程式遷移到雲端，並實現了高度的營運韌性。可觀測性、資料韌性和有效的變更管理是實現這一目標的關鍵要素。本次會議強調了在受監管產業中，透過雲端技術和 SRE 實踐來確保關鍵應用程式的穩定性和可靠性的重要性。
