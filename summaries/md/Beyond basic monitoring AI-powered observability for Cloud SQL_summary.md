Beyond basic monitoring AI-powered observability for Cloud SQL

[會議影片連結](https://www.youtube.com/watch?v=h2OZVNezEHQ)
超越基礎監控：AI驅動的Cloud SQL可觀測性

## 1. 核心觀點

本次演講主要介紹了 Cloud SQL 中由 AI 驅動的可觀測性功能，旨在幫助開發者主動管理資料庫效能問題，即使他們不具備深厚的資料庫專業知識。核心觀點包括：

*   **AI 驅動的可觀測性：** 提供直觀的洞察和工具，幫助開發者排除效能問題。
*   **Cloud SQL 的優勢：** 作為一個全託管服務，Cloud SQL 降低了營運負擔和風險，讓開發者可以專注於創新。
*   **可觀測性的三大支柱：** 包含資料擷取、洞察分析和引導式疑難排解。

## 2. 詳細內容

*   **Cloud SQL 簡介：** Cloud SQL 是一個全託管的 MySQL、Postgres 和 SQL Server 服務，被 Google Cloud 的許多頂級客戶使用。它提供熟悉的關聯式資料庫功能，同時減少了營運負擔和風險。Cloud SQL 採用 API 優先的方法，並且其 Postgres 和 MySQL 服務支援 Gen AI 功能，例如向量索引和向量搜尋。

*   **可觀測性的三大支柱：**
    *   **資料擷取 (Ingest)：** 提供增強的即時遙測、細緻的資料、延長的保留時間和豐富的診斷信號，以幫助偵錯複雜的效能問題。
    *   **洞察 (Insights)：** 提供互動式視覺效果和直觀的資料庫關鍵元素視圖，例如查詢計畫和等待事件，以便主動發現和解決效能問題。
    *   **引導式疑難排解 (Prescribed)：** 提供引導式疑難排解提示，主動突出潛在問題，從而普及可觀測性和效能問題偵錯。

*   **AI 輔助疑難排解示範：** 示範使用 Enterprise Symbol 行動應用程式（使用 Firebase 和 Cloud SQL for Postgres 構建）來追蹤商品庫存。當應用程式效能下降時，Cloud SQL Query Insights 能夠快速找出執行時間最長的查詢。Query Insights 會自動檢查資料量、日誌爭用、查詢計畫變更和系統資源變更等因素。在示範中，Query Insights 發現資料量增加了 10 倍，查詢延遲增加了 20 倍，並建議新增索引以解決效能問題。開發人員可以使用 Database Studio 輕鬆地應用此建議。

*   **AI 輔助疑難排解的優勢：**
    *   普及資料庫可觀測性，讓 DBA 可以專注於策略性專案。
    *   透過減少停機時間來降低營運風險。
    *   縮短上市時間，加快應用程式開發週期。

*   **全域可觀測性：** 除了單個實例的洞察之外，Cloud SQL 還提供整個資料庫叢集的可觀測性，在單一一致的視圖中提供建議和診斷。

## 3. 重要結論

Cloud SQL 中由 AI 驅動的可觀測性功能，透過提供直觀的洞察、互動式視覺效果和引導式疑難排解，賦予開發者主動管理資料庫效能問題的能力。這不僅降低了營運風險，還加快了應用程式開發週期，並讓 DBA 可以專注於更具策略性的工作。講者鼓勵大家探索 Cloud SQL Query Insights 的最新創新，包括 AI 輔助疑難排解，這些功能現在可以從 Cloud SQL 控制台中取得。
