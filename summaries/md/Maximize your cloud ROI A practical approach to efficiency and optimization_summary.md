Maximize your cloud ROI A practical approach to efficiency and optimization

[會議影片連結](https://www.youtube.com/watch?v=7csgD3iIc2Q)
最大化您的雲端投資報酬率：效率與優化的實用方法

## 1. 核心觀點

本次會議主要探討如何最大化雲端投資報酬率（ROI），特別是在 Google Cloud Platform (GCP) 上，透過效率和優化實現成本效益。核心觀點包括：

*   DevOps 團隊不喜歡處理成本數據，這是一項繁瑣且耗時的任務。
*   Cloud Assist Optimize 是一套新工具，旨在簡化成本優化流程，包含 Cloud Hub Optimize、Cost Explorer 和 Gemini Cloud Assist Chat Helper Agent。
*   MLB（美國職棒大聯盟）分享了他們如何使用這些工具來優化雲端成本。
*   透過實際演示，展示了如何輕鬆開始使用這些新工具。

## 2. 詳細內容

*   **成本優化的痛點：**
    *   DevOps 團隊需要從多個數據源（如雲端帳單、日誌、監控等）收集成本數據。
    *   需要處理各種工具的特性，例如 API、SQL 查詢或第三方產品。
    *   收集到的數據需要清洗、過濾和整合，才能產生有用的洞察。
    *   所有這些工作都耗費時間，原本可以用於開發應用程式。
*   **Cloud Assist Optimize 的解決方案：**
    *   將成本和使用率數據整合在一個地方，提供精確的成本效益分析。
    *   支援 App Hub，允許以應用程式視圖查看和除錯成本。
    *   使用 AI 系統以自然語言回答成本相關問題。
*   **Cloud Hub Optimize：**
    *   在 Cloud Hub 中新增的 "Optimize" 標籤，以簡單易懂的方式呈現成本資訊。
    *   顯示與前期相比的成本變化，以及未充分利用的資源。
    *   提供應用程式的成本趨勢、成本最高的五個工作負載以及成本變化。
    *   顯示成本高且未充分利用的五個工作負載。
*   **Cost Explorer：**
    *   提供更詳細的成本和使用率數據，包括每個產品和資源類型的成本細分。
    *   顯示工作負載的總成本、成本變化、P95 使用率和平均使用率。
    *   使用樹狀圖視覺化工具，快速識別成本優化機會。
*   **Gemini Cloud Assist Chat Helper Agent：**
    *   允許使用自然語言提問成本相關問題，例如 "我在這個專案上從 2022 年到 2024 年花了多少錢？"
    *   提供連結到帳單控制台的連結，以驗證成本的準確性。
*   **MLB 的案例研究：**
    *   MLB 從 2023 年開始正式實施 FinOps 計劃。
    *   他們擁有超過 200 個 GKE 叢集、6,000 個應用程式、20+ PB 的數據和 700 個 GCP 專案。
    *   Cloud Hub 和 Cost Explorer 創建了一個單一的管理平台，簡化了開發人員的工作流程。
    *   App Hub 允許 FinOps 團隊分擔定義每個團隊應用程式的負擔，並產生相關報告。
    *   在 MLB 開幕日，Cost Explorer 顯示雲端日誌的增加速度比預期的 GKE 增加速度更快，這促使他們與開發團隊進行了討論。
*   **演示：**
    *   演示展示了一個簡單的 GKE 應用程式，該應用程式使用多個 GCP 產品進行電影推薦。
    *   透過 Cloud Hub Optimize，可以快速識別叢集成本高且使用率低。
    *   透過將前端工作負載合併到 GPU 機器上，可以更有效地利用資源並降低成本。
*   **未來發展：**
    *   更豐富的聊天功能，允許使用自然語言提問有關應用程式、資源和使用率的問題。
    *   支援多專案和多應用程式管理。
    *   提供 API，允許 DevOps 團隊從命令行和 API 格式使用數據。

## 3. 重要結論

Cloud Assist Optimize 提供了一套全面的工具，可以幫助 DevOps 團隊和平台工程團隊更輕鬆地優化雲端成本。透過整合成本和使用率數據、提供可操作的洞察以及使用 AI 簡化工作流程，這些工具可以幫助組織最大化其雲端投資報酬率。MLB 的案例研究證明了這些工具的價值，並展示了它們如何幫助組織節省資金並提高效率。
