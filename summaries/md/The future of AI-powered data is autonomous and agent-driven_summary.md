The future of AI-powered data is autonomous and agent-driven

[會議影片連結](https://www.youtube.com/watch?v=-Uda0ldaVbA)
AI 驅動數據的未來是自主和代理驅動的

## 1. 核心觀點

本次會議主要探討了 Google Cloud 在數據和 AI 領域的最新進展，特別是 BigQuery 如何演進為一個自主的數據到 AI 平台。核心觀點包括：

*   **BigQuery 的演進：** 從傳統的數據倉庫轉變為自主的數據到 AI 平台，具備智能、實時和自主的特性。
*   **專用代理（Specialized Agents）：** 將 AI 的強大功能賦予每個用戶，簡化數據分析和模型開發流程。
*   **互操作引擎（Interoperable Engines）：** 提供強大的後端支持，以無與倫比的性能、可擴展性和靈活性分析數據。
*   **自主數據基礎（Autonomous Data Foundation）：** 自動化整個數據生命週期，從數據攝取到洞察生成，釋放團隊的創新能力。

## 2. 詳細內容

*   **General Mills 的案例：** General Mills 分享了他們遷移到 Google Cloud 的經驗，以及如何利用 Google Cloud 的數據和 AI 功能實現了顯著的業務成果，包括數百萬美元的成本節約和收入增長。他們還構建並部署了 MillsChat，一個基於 Google LLM 的內部生成式 AI 系統。
*   **BigQuery 的增長：** BigQuery 的客戶數量是市場上其他兩個企業數據平台的五倍，並且 BigQuery 和 Vertex AI 的成本效益比其他數據倉庫或 AI 平台高 8 到 16 倍。
*   **數據工程代理（Data Engineering Agent）：** 嵌入在 BigQuery 管道中，由 Gemini 提供支持，可以自動化數據準備和標準化等繁瑣任務，主動檢測異常以確保數據質量，甚至可以自動化元數據生成等治理任務。
*   **數據科學代理（Data Science Agent）：** 旨在加速模型開發，包括特徵工程、智能模型選擇、可擴展訓練和簡化推理，使數據科學家能夠以空前的速度和效率應對最複雜的挑戰。
*   **對話式分析代理（Conversational Analytics Agent）：** 由 Gemini 2.0 的高級推理能力提供支持，可以處理更複雜的問題，並可以部署在自定義應用程序、聊天界面或專用工作區中。新的對話式分析 API 為開發人員開闢了全新的可能性。
*   **AI 查詢引擎（AI Query Engine）：** BigQuery 引入了新的 AI 查詢引擎，可以調動真實世界的知識、語言理解和 Gemini 的推理能力，從而可以回答以前無法回答的問題。
*   **多模態表（Multimodal Tables）：** BigQuery 允許在同一個表中存儲和查詢非結構化數據（圖像、音頻、語音和文本）以及結構化數據，簡化了跨多個數據源的 AI 和機器學習推理。
*   **Apache Iceberg 的支持：** BigQuery 提供對 Apache Iceberg 的市場最佳支持，允許構建具有企業級功能的開放湖倉，包括細粒度的治理和頂級安全性。

## 3. 重要結論

Google Cloud 正在構建一個自主的數據到 AI 平台，該平台可以預測需求、自動化操作並主動採取行動。通過專用代理、互操作引擎和自主數據基礎，Google Cloud 旨在幫助企業將數據轉化為切實可行的現實世界成果，並增強團隊的能力，從而實現更大的成就。BigQuery 的演進和新功能的推出，標誌著數據和 AI 領域的重大進步，為企業提供了前所未有的能力來利用數據的力量。
