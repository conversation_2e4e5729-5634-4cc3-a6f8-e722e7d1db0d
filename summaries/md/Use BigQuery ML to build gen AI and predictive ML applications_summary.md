# Use BigQuery ML to build gen AI and predictive ML applications
[會議影片連結](https://www.youtube.com/watch?v=_jH01He_yxU)
使用 BigQuery ML 構建生成式 AI 和預測性 ML 應用程式

## 1. 核心觀點

本次會議主要介紹了 BigQuery ML 的生成式 AI 和預測性 ML 功能，以及如何使用這些功能構建有用的應用程式。BigQuery ML 是一個功能集合，允許使用者僅用幾行 SQL 語句在 BigQuery 中執行端到端的 AI 和 ML。它簡化了 ML 流程，擴大了組織內使用者對 ML 的訪問，並降低了複雜性。BigQuery ML 整合了 Vertex AI 和 GCP Cloud AI 服務，讓使用者可以直接從 BigQuery 使用 SQL 消耗 LLM 和其他模型。

## 2. 詳細內容

**BigQuery ML 的功能套件：**

*   **資料特徵工程：** BigQuery ML 原生實作了常見的特徵轉換，例如分桶、編碼、正規化，甚至可以處理非結構化資料（如影像）並調整其大小。這些功能都可透過 SQL 使用，並且具有高度的可擴展性。
*   **模型訓練：** BigQuery ML 實作了所有常見的 ML 模式，並提供演算法來應對各種常見的使用案例。使用者可以使用熟悉的 SQL 語法進行超參數優化。
*   **模型評估：** 使用者可以在 BigQuery 內部評估預測性機器學習模型和 LLM。
*   **模型註冊：** 使用者可以將訓練好的模型註冊到 Vertex CI 模型登錄檔，以便擁有一個通用的模型儲存庫。
*   **推論：** 推論可以透過簡單的 SQL 語句來完成，並且 BigQuery ML 提供了靈活的推論功能，包括批次推論、串流推論和連接到遠端端點以進行推論。
*   **監控：** BigQuery ML 提供了監控功能，可以檢測訓練和推論資料何時開始出現偏差。

**生成式 AI 功能：**

*   **LLM 的選擇：** BigQuery 允許使用者使用 Gemini 模型，以及 Claude、Llama 和 Mistral 模型。使用者也可以在 Vertex AI 模型端點上託管任何 Hugging Face 模型，並將其與 BigQuery 搭配使用。
*   **多模態：** BigQuery 提供了結構化和非結構化資料的表格抽象。使用者可以將具有結構化資料的欄位和指向 GCS 中非結構化資料的另一個欄位混合在一起，並將此混合資料傳遞給 Gemini 進行多模態推論或傳遞給 DocAI 進行 PDF 解析。
*   **結構化資料輸出：** BigQuery ML 允許使用者傳入輸出架構，並使用 AIG 來產生表格函數，該函數會將輸出作為新欄位提供給使用者。
*   **列式函數：** BigQuery ML 正在新增新的列式函數，讓使用者可以輕鬆地將 LLM 推論作為 select 語句或 where 語句的一部分。
*   **可擴展性和定價：** BigQuery ML 具有高度的可擴展性，並且在定價方面具有市場領先地位。當使用者從 BigQuery 使用 Gemini 模型時，可以獲得批次工作負載的 50% 折扣。

**生成式 AI 的使用案例：**

*   **NLP：** 實體提取、情感分析、內容生成和摘要。
*   **影像：** 檢測汽車的哪些部位損壞並產生結構化資料。
*   **資料豐富：** 使用國家和城市範例來豐富資料。
*   **分類：** 使用 Gemini 模型進行任何類型的分類。
*   **複雜的 Gen AI 應用程式：** 建立多模態搜尋應用程式或使用 SQL 建立 RAG 應用程式。

**預測性 ML 功能：**

*   **預測：** BigQuery ML 提供了基於 ARIMA 的模型進行預測，並新增了許多功能來幫助企業做好預測，例如多變數預測和同時預測數千個時間序列。
*   **異常檢測：** BigQuery ML 支援時間序列異常檢測和 IID 資料異常檢測。
*   **迴歸和分類：** BigQuery ML 提供了各種演算法來支援迴歸和分類問題，包括 XGBoost。
*   **分群和降維：** 使用者可以使用 K-means 進行使用者分群並優化行銷策略，或者使用降維來處理非常複雜的資料並將這些特徵提供給下游模型。
*   **推薦：** 使用者可以使用矩陣分解和 widened deep 模型進行推薦。

**近期發布：**

*   **Times FM 基礎模型整合：** BigQuery ML 整合了 Times FM 基礎模型，這是一個在超過 1000 億個真實世界資料點上訓練的先進基礎模型，並且可以很好地推廣到真實的業務領域。
*   **貢獻分析模型：** BigQuery ML 推出了 NGA 貢獻分析模型，允許使用者指定他們真正關心的資料部分（例如維度）以及用於查看變更的指標，並獲得導致測試集和控制集之間資料變更的主要貢獻者。

**Home Depot 的案例分享：**

Home Depot 使用 BigQuery ML 來改善客戶體驗並支援其專業承包商。他們使用 BigQuery ML 進行預測性 ML 應用程式和生成式 AI 應用程式。對於預測性 ML，他們使用 XGBoost 演算法來優先處理前 20 名客戶，這些客戶的表現優於其他群組。對於生成式 AI，他們使用 Gemini 來提取客戶評論中的實體，以了解客戶的職業。

## 3. 重要結論

BigQuery ML 提供了一套全面的工具，用於構建生成式 AI 和預測性 ML 應用程式。它簡化了 ML 流程，擴大了組織內使用者對 ML 的訪問，並降低了複雜性。BigQuery ML 整合了 Vertex AI 和 GCP Cloud AI 服務，讓使用者可以直接從 BigQuery 使用 SQL 消耗 LLM 和其他模型。Home Depot 的案例分享展示了 BigQuery ML 如何用於改善客戶體驗並支援其專業承包商。
