# What's next for Google Cloud databases in the AI era
[會議影片連結](https://www.youtube.com/watch?v=L9WmdZD6z8E)
AI 時代下 Google Cloud 資料庫的下一步

## 1. 核心觀點

本次會議聚焦於 Google Cloud 資料庫在 AI 時代的最新創新，以及如何協助客戶建構資料驅動的應用程式。核心觀點包括：

*   **AI 應用程式與代理程式的建構：** 強調資料庫在代理工作流程中的重要性，以及如何利用即時資料做出高品質的決策。
*   **更多 API 提供給開發者：** 致力於開放性，提供多樣的資料庫引擎和資料模型選擇，以及跨平台部署的自由。
*   **加速遷移與現代化至 Google Cloud：** 提供業界領先的託管服務，簡化資料庫管理，並提供具成本效益的解決方案。
*   **AlloyDB 的 AI 創新：** 整合 Agent Space、自然語言支援、向量搜尋等功能，使開發者能更輕鬆地建構智慧型應用程式。
*   **Spanner 的多模型支援：** 結合關聯式、圖形、鍵值、全文檢索和向量搜尋，提供高度擴展性的 AI 工作負載解決方案。
*   **Firestore 的 MongoDB 相容性：** 為開發者提供更多選擇，利用現有的 MongoDB 應用程式程式碼和工具，同時享受 Firestore 的可擴展性和高可用性。

## 2. 詳細內容

*   **GenAI 應用程式與代理程式：**
    *   Google Cloud 提供一套強大的業界領先資料庫，建立在行星級基礎架構上，並以 AI 為核心。
    *   推出 MCP Toolbox for databases，簡化開發流程，增強安全性，並提供端到端的可觀察性。
    *   AlloyDB 整合 Google Agent Space，讓使用者可以結合即時結構化和非結構化資料。
    *   AlloyDB AI 自然語言的下一代版本，提供更安全、更準確的結構化資料查詢能力，並支援多種模態。
    *   AlloyDB 的向量搜尋採用率大幅提升，提供優化的 SQL 功能，並與 Vertex AI 和 Google DeepMind 合作，引入新的 AI 模型。
    *   AlloyDB AI Query Engine 讓開發者可以在 SQL 查詢中使用自然語言表達式。
*   **開發者自由與 API：**
    *   提供多種資料庫引擎和資料模型選擇，包括開放原始碼、Google 和合作夥伴技術。
    *   AlloyDB Omni 允許在任何地方執行 AlloyDB，並與 Ivan 合作提供在 AWS、Azure 和 Google Cloud 上運行的全託管 AlloyDB Omni 服務。
    *   Spanner 支援多模型，包括關聯式、圖形、鍵值、全文檢索和向量搜尋。
    *   推出 Spanner 的圖形視覺化功能，讓開發者可以從 Spanner 資料庫中提取有價值的資訊。
    *   Firestore 推出 MongoDB 相容性，讓開發者可以使用現有的 MongoDB 應用程式程式碼、驅動程式和整合。
    *   Bigtable 推出連續具體化檢視，簡化即時指標整合到應用程式的流程。
*   **遷移與現代化：**
    *   提供業界領先的託管服務，支援 MySQL、Postgres、Valkey、Oracle 和 SQL Server 等流行的開放原始碼和商業引擎。
    *   Cloud SQL 和 AlloyDB 在基於 Google Axion 處理器的 C4 執行個體上可用，提供更好的價格效能。
    *   Oracle Database 在 Google Cloud 上提供，並新增 Oracle Autonomous Database Serverless 和跨區域災難復原支援。
    *   推出 Oracle-based database service，提供在 Google Cloud 內部執行 Oracle 資料庫的彈性方式。
    *   Oracle Exadata X11M 在 Google Cloud 上正式推出，為最密集的 Oracle 應用程式提供顯著的效能優勢和更高的容量。
    *   資料庫遷移服務現在支援 SQL Server 到 Postgres 的遷移，適用於 Cloud SQL 和 AlloyDB。
    *   Database Center，AI 驅動的統一艦隊管理解決方案，現已正式推出，支援產品組合中的每個資料庫。
*   **Walmart 的案例分享：**
    *   Walmart 利用 Google Cloud Spanner 處理電子商務和零售支付，AlloyDB 提升產品搜尋的準確性，AlloyDB Omni 支援機器人和供應鏈自動化。
    *   Walmart 透過傳統 AI 進行異常偵測，利用生成式 AI 增強搜尋能力和產品推薦，並使用代理式 AI 自動化資料庫備份、維護和效能調整等任務。
*   **Target 的案例分享：**
    *   Target 使用 AlloyDB 改善線上搜尋體驗，透過結合結構化和非結構化資料，將線上零售網站的自然語言查詢準確性提高了 20%。

## 3. 重要結論

Google Cloud 持續創新，提供智慧、統一且開放的資料平台，以啟動 AI 應用。透過整合 AI 技術、提供多樣的資料庫選擇、簡化開發流程，並提供具成本效益的解決方案，Google Cloud 致力於協助客戶在 AI 時代取得成功。與 Oracle 的合作夥伴關係也為客戶提供了更多選擇和靈活性，以遷移和現代化其資料庫資產。
