# What’s new in Looker AI for BI
[會議影片連結](https://www.youtube.com/watch?v=wUPgHQMe8tk)
Looker AI 在商業智慧領域的最新應用

## 1. 核心觀點

本次會議主要介紹了 Looker 平台在 AI 方面的最新進展，包括統一 Looker Studio 和 Looker、在整個平台中融入 Gemini AI，以及強化 Looker 的開放語義模型。重點在於利用 AI 簡化 BI 的使用，使其更易於存取，並解決資料專家和分析師不足的問題。

## 2. 詳細內容

**Looker 平台統一與增強：**

*   **Looker Reports：** 整合 Looker Studio，提供企業級報表系統和視覺化引擎，讓 Looker 客戶能更輕鬆地進行自助服務。
*   **Gemini AI 融入：** 在 Looker 中導入 Gemini，提供對話式分析和 AI 助理，協助使用者從資料層面解放 BI 和資料工作。
*   **開放語義模型強化：** Looker 的核心差異在於受管理的 BI 和開放語義模型，這在 Gen AI 時代變得更加重要。
*   **開放平台策略：** Looker 致力於開放平台，允許使用者將 Looker 語義模型連接到企業 BI 系統，並在他們喜歡的工具中使用，例如 Google Sheets 和 Excel。

**對話式分析：**

*   解決了儀表板無法回答所有問題的痛點，減少分析師的 BI 需求，並讓業務使用者能夠自行安全地獲取答案。
*   透過可信任的指標，確保資料的準確性。

**產品路線圖：**

*   **Looker Reports：** 全新的視覺化系統，用於企業報表和自助服務。
*   **Gemini 和 Looker：** 整合 Gemini 的功能。
*   **Spectacles 整合：** 為 Looker 開發人員提供更深入的持續整合和 SQL 驗證。

**Unilever 的案例分享：**

*   Unilever 使用 Google Cloud 和 Looker 轉型其通路貿易業務。
*   透過數位化經銷商營運、提升 B2B 體驗和部署 AI/ML 驅動的執行，改善對小型零售商的服務。
*   使用 Looker 作為商業智慧平台，提供即時洞察，並建立銷售交叉鏡頭自助服務 BI 平台，讓銷售團隊能夠建立自己的儀表板。
*   透過遷移儀表板追蹤平台採用情況，加速在東南亞市場的推廣。

**AI for BI 的未來：**

*   **Gemini 的應用：** 利用 Gemini 作為推理引擎，處理資料相關問題，並將其分解為複雜的多步驟計畫。
*   **準確性提升：** 透過訓練 Gemini 使用 Looker 的語義層，將準確性提高 42%。
*   **語義層的重要性：** 語義層提供 ACV、成長和區隔等概念的黃金標準定義，減少資料錯誤。
*   **自動語義產生：** 允許非 LookML 專家建立語義模型，並讓資料專家能夠更快地開發 LookML。
*   **多模態輸入：** Gemini 能夠處理圖像和 SQL 等非自然語言輸入，並產生 LookML。
*   **Gemini 和 Looker 的普及：** Gemini 和 Looker 將在所有 Looker 版本中提供。
*   **程式碼解釋器：** 讓使用者能夠進行複雜的分析，例如關鍵驅動因素分析、預測和預測建模。
*   **開放 API：** 允許開發人員建立自訂應用程式，並將對話式分析嵌入到聊天應用程式中。

## 3. 重要結論

Looker 正在透過整合 Gemini AI 和強化其開放語義模型，積極推動 BI 的創新。這些進展旨在簡化 BI 的使用，使其更易於存取，並解決資料專家和分析師不足的問題。Unilever 的案例展示了 Looker 如何幫助企業轉型其業務，並透過資料驅動的決策來改善營運。未來，Looker 將繼續擴展 AI 的應用，並提供更強大的工具和 API，讓使用者能夠建立自訂應用程式，並將資料智慧融入到其工作流程中。
