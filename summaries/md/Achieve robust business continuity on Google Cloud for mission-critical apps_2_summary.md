# Achieve robust business continuity on Google Cloud for mission-critical apps_2
[會議影片連結]()
在 Google Cloud 上實現關鍵任務應用程式的穩健業務持續性_2

## 1. 核心觀點

本次會議主要探討在 Google Cloud 上，如何為關鍵任務應用程式實現穩健的業務持續性。核心觀點涵蓋架構設計、營運實務以及案例分析，強調多區域部署、服務選擇、容量規劃、備份策略、安全變更、故障測試和事件響應等關鍵要素。Charles Schwab 的案例研究則展示了這些原則在實踐中的應用。

## 2. 詳細內容

會議首先討論了業務持續性的關鍵考量。對於關鍵任務應用程式而言，容錯能力至關重要，目標是實現接近零停機時間的五個九可用性。多區域部署是實現這一目標的常見方法，包括主動-被動和主動-主動兩種模式。主動-主動部署雖然前期較為複雜，但能簡化災難恢復。

架構方面，選擇合適的服務至關重要。不同的服務具有不同的可靠性特徵，區域性服務比區域服務更能容忍故障，而全域服務的可靠性最高。容量規劃不僅要考慮穩態流量，還要考慮峰值流量，以及災難或故障情況下的容量需求。備份是最後一道防線，異地備份更具彈性。

營運實務方面，安全變更、故障測試和事件響應是關鍵。安全變更策略應包括 Canary 測試和藍綠部署。故障測試需要在預生產環境中模擬故障，以驗證應用程式的容錯能力。事件響應應以服務等級目標（SLO）為導向，確保在發生中斷或事件時能夠滿足業務需求。

Charles Schwab 的案例研究展示了如何將這些原則應用於其登入應用程式。該應用程式採用多區域、多可用區部署，具有自我架構的複製功能和異地備份。安全性是端到端的，涵蓋基礎設施安全、身份驗證、防火牆、軟體供應鏈安全、加密和法規遵從。變更管理採用漸進式方法，並高度自動化。事件管理以 SLO 為導向，關注事件確認、解決、緩解和根本原因分析的速度。

實務經驗方面，強調在真實負載下進行大規模測試，以發現生產環境中的瓶頸。變更管理至關重要，需要策略性規劃發布和部署。Charles Schwab 採用「遊戲日」的方式，將所有利益相關者團隊和應用程式組件（包括軟體、流程和人員）聚集在一起，模擬故障場景。測試應貫穿開發和部署過程的每一層。

## 3. 重要結論

在 Google Cloud 上實現關鍵任務應用程式的穩健業務持續性，需要綜合考慮架構設計、營運實務和實務經驗。多區域部署、服務選擇、容量規劃、備份策略、安全變更、故障測試和事件響應是關鍵要素。Charles Schwab 的案例研究展示了這些原則在實踐中的應用。透過在真實負載下進行大規模測試、策略性規劃變更管理以及採用「遊戲日」的方式，可以有效提升應用程式的可靠性和可用性。
