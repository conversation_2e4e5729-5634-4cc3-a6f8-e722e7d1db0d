# Google AI for developers Research to reality
[會議影片連結](https://www.youtube.com/watch?v=LtapFtQQKMc)
Google AI 開發者：從研究到現實

## 1. 核心觀點

本次會議主要介紹 Google DeepMind 在 AI 領域的最新研究成果以及如何將這些成果應用於實際開發中。重點包括 Gemini 模型系列的強大功能和多樣性，以及 Google AI Studio 這個便捷的開發工具。強調 Google 致力於以負責任的方式開發 AI，並使其對開發者、企業和一般大眾都有所助益。

## 2. 詳細內容

*   **Google DeepMind 的使命：** 以負責任的方式開發 AI，促進人類和科學的進步。AI 應被視為強大的工具，用於提高生產力、增強創造力，並解決人類面臨的重大挑戰。

*   **Gemini 模型系列：** Google 最具雄心的項目，從底層構建為多模態，擅長 API 整合。
    *   **Gemini 2.5 Pro：** 在各種任務中表現最佳，擁有高達 200 萬 tokens 的超長上下文窗口。
    *   **Gemini Flash：** 低延遲，適合代理體驗。
    *   **Gemini Nano：** 針對設備端任務優化。

*   **Gemma 開放模型系列：** 基於與 Gemini 相同的研究技術，為開發者提供更多控制權。Gemma 3 是最新、最強大的模型，有多種尺寸可供選擇，可在各種設備上運行。

*   **Google AI Studio：** 簡化 AI 模型實驗的工具，提供友好的使用者介面，可建立、測試和儲存 prompts。現在也支援圖像和影片生成。

*   **AI Studio 的進階功能：**
    *   **結構化輸出：** 定義 API 回應的格式，方便與現有系統整合。
    *   **程式碼執行：** 使用 Python 程式碼解決 prompt 中的問題，例如計算字串中的字母數量或生成圖表。
    *   **YouTube 連結支援：** 從 YouTube 影片中提取資訊，例如摘要或重點。
    *   **Google 搜尋整合：** 使用 Google 搜尋的結果作為模型的上下文，提供更準確和最新的答案。
    *   **程式碼生成：** 使用 Gemini 2.5 Pro 生成高品質的程式碼，例如使用 P5.js 框架建立遊戲。

*   **Vio 模型：** 能夠根據文字 prompt 生成影片，並提供編輯功能。

## 3. 重要結論

Google DeepMind 正在積極推進 AI 技術的發展，並透過 Gemini 模型系列和 Google AI Studio 等工具，將這些技術提供給開發者使用。Google 致力於以負責任的方式開發 AI，並使其對社會產生積極影響。會議展示了 AI 在各個領域的應用潛力，並鼓勵開發者積極參與 AI 的創新和發展。
