# Build high-performance apps for the edge with <PERSON><PERSON> in Go 1.24
[會議影片連結](https://www.youtube.com/watch?v=2ECGTgZp1KA)
使用 Go 1.24 和 Wasm 為邊緣構建高效能應用程式

## 1. 核心觀點

本次會議主要探討了如何使用 Go 語言和 WebAssembly (Wasm) 技術，在邊緣環境構建高效能應用程式。Go 團隊強調 Go 不僅僅是一門程式語言，而是一個完整的平台，旨在提供高效、安全、可靠的生產系統構建方案。會議重點介紹了 Go 1.24 在 Wasm 支援方面的改進，以及如何在 Google Cloud 和 Shopify 等實際應用場景中利用這些技術。

## 2. 詳細內容

- **Go 語言的優勢：**
    - **生產力：** Go 易於學習、維護和擴展，能快速構建可擴展的生產應用程式。
    - **平台：** Go 提供完整的開發者體驗，包括 IDE 整合、建置部署工具、監控工具、漏洞管理和執行階段工具。
    - **生產就緒：** Go 具有可靠、高效、穩定和安全的特性，廣泛應用於企業關鍵業務系統和基礎設施。
    - **DevOps 整合：** Go 旨在解決軟體開發生命週期的各個環節，從程式碼編寫到部署和維運。
    - **安全性：** Go 非常重視安全性，提供依賴管理系統、模糊測試等工具，以確保應用程式的安全性。

- **WebAssembly (Wasm) 的優勢：**
    - **可移植性：** Wasm 是一種二進位指令格式，可以在任何支援 Wasm 執行環境的主機上執行。
    - **高效能：** Wasm 應用程式可以達到接近原生程式碼的效能。
    - **輕量級：** Wasm 類似於容器，但更輕量，沒有作業系統和不必要的二進位檔案。
    - **安全性：** Wasm 執行環境是一個沙箱，具有結構化的流程和驗證機制。

- **Go 1.24 對 Wasm 的支援：**
    - **匯出 Go 函數：** Go 1.24 允許將 Go 函數匯出到 Wasm 主機，實現不同語言之間的互操作性。
    - **WASI Reactor 支援：** Go 1.24 支援構建 WASI Reactor，適用於長時間執行的應用程式、外掛程式或擴充功能。

- **Google Cloud Service Extensions：**
    - **無伺服器：** 只需提供程式碼，Google Cloud 負責管理其餘部分，按使用量付費。
    - **整合：** 與多個雲端網路代理整合，提供一致的體驗。
    - **開放原始碼：** 基於 Go、Wasm 和 Proxy-Wasm API。
    - **開發者友善：** 提供強大的本地測試體驗，包括效能基準測試工具。
    - **應用場景：** 流量管理、安全性、效能和可觀測性。

- **Shopify 的應用案例：**
    - **全球基礎設施：** 在多個 Google 區域運行，以服務於全球使用者。
    - **高流量處理：** 在 Black Friday 和 Cyber Monday 期間處理大量請求。
    - **Service Extensions 的應用：**
        - 自訂日誌記錄到 Cloud Logging。
        - 新增、附加和修改標頭以進行追蹤。
        - 正規化標頭。
    - **Go 語言在 Shopify 的應用：**
        - Kubernetes 和 GKE 整合。
        - 圖片、JavaScript 和 CSS 請求處理。
        - 安全性、可靠性和漏洞管理。
        - 偵錯和效能管理。

## 3. 重要結論

Go 語言和 WebAssembly 技術的結合，為在邊緣環境構建高效能應用程式提供了強大的解決方案。Go 1.24 在 Wasm 支援方面的改進，以及 Google Cloud Service Extensions 和 Shopify 等實際應用案例，展示了這些技術的潛力。Go 團隊致力於提供一個完整的平台，以簡化生產系統的構建、部署和維運，並確保應用程式的安全性和可靠性。
