# World models What lies ahead
[會議影片連結](https://www.youtube.com/watch?v=DhWYqPPt7JQ)
世界模型：未來展望

## 1. 核心觀點

本次演講主要探討世界模型（World Models）的概念、應用以及未來發展方向。講者 Tim 強調世界模型對於人工智慧理解和互動物理世界的重要性，並介紹了 Google 和 DeepMind 在此領域的三個重要項目：Genie 2、Veo 2 和 Gemini 2.0 的原生圖像生成能力。核心觀點包括：

*   **世界模型的重要性：** 使 AI 能夠像人類一樣理解、互動和推理物理世界。
*   **Genie 2 的能力：** 作為一個大型基礎世界模型，能夠根據鍵盤和滑鼠操作生成互動式環境，並具備長期記憶和物件互動能力。
*   **Veo 2 的進展：** 作為 Google 的前沿影片生成模型，在真實感、運動能力和相機控制方面取得了顯著進展，有助於建立更精確的物理世界模型。
*   **Gemini 2.0 的圖像生成：** 透過語言模型直接生成和編輯圖像，展現了對物理世界的理解和推理能力，例如模擬開門步驟和不同視角的場景生成。
*   **物理智能（Physical Intelligence）的未來：** 世界模型與語言模型的結合，將推動 AI 在物理世界中更好地推理和行動，並與機器人技術結合，實現更有用的機器人應用。
*   **世界模擬（World Simulation）的願景：** 隨著模型變得越來越真實和強大，最終可能實現對現實世界的模擬。

## 2. 詳細內容

*   **世界模型的定義與運作方式：** 世界模型接收當前世界狀態作為輸入，並根據給定的動作預測未來狀態。例如，給定一個蘋果攤位的狀態和「拿起蘋果」的動作，模型應預測拿起蘋果後的狀態。
*   **Genie 2：**
    *   是一個基於鍵盤和滑鼠操作的世界模型，能夠生成互動式環境。
    *   具備長期記憶，即使物體移出視野，也能保持對其存在的記憶。
    *   能夠與環境中的物體互動，例如透過按下空白鍵使氣球爆裂。
    *   可用於訓練和評估 AI 代理，例如 Sima 代理可以在 Genie 2 模擬的環境中學習打開紅色或藍色門。
*   **Veo 2：**
    *   是一個前沿影片生成模型，能夠生成高度真實的影片。
    *   在真實感、運動能力和相機控制方面表現出色。
    *   能夠模擬複雜的物理現象，例如咖啡倒入杯中時液位的上升，以及切番茄時的摩擦力。
    *   使用者更喜歡 Veo 2，因為它能更準確地遵循提示。
*   **Gemini 2.0 的原生圖像生成：**
    *   能夠根據文字提示生成和編輯圖像。
    *   展現了對物理世界的理解和推理能力，例如模擬開門的步驟，從空中視角生成地面視角，以及根據操作更新場景（例如打開燈或爐灶）。
*   **物理智能：**
    *   指的是 AI 在物理世界中推理、行動和互動的能力。
    *   世界模型是實現物理智能的關鍵組成部分。
    *   與機器人技術結合，可以實現更有用的機器人應用。
*   **Gemini Robotics：**
    *   將 Gemini 的能力應用於機器人控制。
    *   透過生成模型賦予機器人行動能力。
*   **世界模擬：**
    *   隨著模型變得越來越真實和強大，最終可能實現對現實世界的模擬。

## 3. 重要結論

世界模型是人工智慧發展的重要方向，它使 AI 能夠更好地理解和互動物理世界。Genie 2、Veo 2 和 Gemini 2.0 等項目在世界模型領域取得了顯著進展，為未來 AI 的發展奠定了基礎。物理智能和世界模擬是令人期待的未來願景，它們將推動 AI 在物理世界中更好地推理、行動和互動，並為人類帶來更多便利和價值。
