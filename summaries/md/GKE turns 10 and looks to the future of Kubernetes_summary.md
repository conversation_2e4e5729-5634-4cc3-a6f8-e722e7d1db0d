# GKE turns 10 and looks to the future of Kubernetes
主題演講
[會議影片連結](https://www.youtube.com/watch?v=d2C3PbkOzUc)
GKE 邁入十週年，展望 Kubernetes 的未來

## 1. 核心觀點

本次會議主要探討 GKE（Google Kubernetes Engine）在過去十年中的發展歷程，以及 Kubernetes 未來的發展方向。重點包括：

*   GKE 作為 Google 核心容器平台的重要性，以及其在 Google 內部產品中的廣泛應用。
*   Kubernetes 從 Borg 專案演變至今的創新歷程，以及 GKE 在其中的關鍵作用。
*   Google 對 Kubernetes 社群的持續貢獻，以及 GKE 如何專注於解決使用者不希望或不應該處理的任務，例如安全性和可靠性。
*   GKE 如何透過規模擴展和優化，協助使用者克服 AI 帶來的挑戰，並降低成本。
*   AppLovin 如何利用 Google Cloud 和 GKE 解決其基礎設施挑戰，並實現業務增長。
*   GKE 如何透過自動擴展、容器優化計算等技術，提升效能和降低延遲。
*   GKE 未來發展方向，包括即時調整 Pod 大小、多叢集協調器等功能。

## 2. 詳細內容

*   **GKE 的重要性：** Google 每週啟動超過 40 億個新容器，GKE 在 Google 內部產品（如 Vertex、DeepMind、Nest、Waze）中得到廣泛應用。
*   **Kubernetes 的發展歷程：** Kubernetes 源於 Google 內部的 Borg 專案，2014 年首次提交程式碼，2015 年推出首個託管 Kubernetes 服務 GKE。
*   **Google 對 Kubernetes 的貢獻：** Google 一直是 Kubernetes 專案的最大貢獻者，其貢獻量超過其他超大規模企業的總和。
*   **GKE 的核心價值：** GKE 專注於使用者不希望或不應該處理的任務，例如安全性和可靠性，並透過規模擴展和優化，協助使用者克服 AI 帶來的挑戰，並降低成本。
*   **AppLovin 的案例：** AppLovin 是一家行動應用程式和廣告平台，透過 Google Cloud 和 GKE 解決了其基礎設施挑戰，包括處理每天數兆的事件、擴展基礎設施以應對業務增長、以及自動化機器學習流程。
*   **GKE 的自動擴展：** GKE 透過自動擴展功能，可以快速擴展和縮減容器數量，以應對不同的工作負載需求。
*   **GKE 的容器優化計算：** GKE 透過容器優化計算功能，可以更有效地利用計算資源，並降低成本。
*   **GKE 的未來發展方向：** GKE 未來將推出更多新功能，包括即時調整 Pod 大小、多叢集協調器等，以提升效能、降低延遲、並簡化管理。
*   **GKE Inference Gateway：** 透過基於模型行為指標路由流量，降低服務成本並減少尾部延遲。
*   **動態工作負載排程 (DWS)：** 提供兩種方法存取庫存，包括在未來日期排程以獲得保證的庫存，或在有足夠容量時靈活運行訓練作業。
*   **自訂運算類別：** 允許指定 VM 系列、類型和成本結構，以便在需要時獲得所需的確切庫存類型。
*   **GKE 1.32：** 在 Autopilot 上可用，可在 8 秒內將 Pod 從 1 個擴展到 10 個。
*   **容器優化運算：** Autopilot 叢集中提供的新功能，可減少 50% 的閒置容量和 35% 的延遲。
*   **即時 Pod 大小調整：** 允許在不重新啟動的情況下調整 Pod 的大小，從而使 VPA 對於即時工作負載更有用。
*   **多叢集協調器：** 允許跨多個叢集協調相同的工作負載，並在擴展決策中考慮到所有這些叢集。

## 3. 重要結論

GKE 在過去十年中取得了顯著的進展，並將繼續在 Kubernetes 的未來發展中扮演關鍵角色。透過持續創新和優化，GKE 正在協助使用者克服 AI 帶來的挑戰，並降低成本，同時簡化 Kubernetes 的管理和使用。AppLovin 的案例證明了 GKE 在解決實際業務挑戰方面的價值。GKE 的未來發展方向將進一步提升效能、降低延遲、並簡化管理，使其成為企業部署和管理容器化應用程式的首選平台。
