Under the Iceberg Simple, unified Cloud Storage for analytics data lakes

[會議影片連結](https://www.youtube.com/watch?v=ewI3Z8PYV_Y)
Under the Iceberg 簡單、統一的雲端儲存，適用於分析資料湖

## 1. 核心觀點

本次會議主要探討了客戶如何利用 Apache Iceberg 等開放表格格式來統一其分析資料湖，並使用雲端儲存作為資料湖的通用資料平台。重點在於雲端儲存如何在簡化性、效能和智慧方面進行創新，以促進這些分析資料湖的發展。

## 2. 詳細內容

會議首先提到，客戶正廣泛採用開放表格格式，特別是 Apache Iceberg，來統一他們的分析資料湖。無論是使用第一方解決方案（如 BigQuery tables for Apache Iceberg）、第三方解決方案（如 Snowflake 或 Databricks），還是自行使用開源方案，他們都將雲端儲存作為資料湖的通用資料平台。

接著，會議強調了雲端儲存在簡化性、效能和智慧方面的重要創新。以 Spotify 為例，他們需要管理大規模的資料湖，包含數百萬 TB 甚至 PB 級別的資料，橫跨多個大洲的數千甚至數萬個儲存桶，並處理包括分析和 AI/ML 在內的各種資料密集型工作負載。這些客戶希望分析並更好地理解他們的資料，以實現成本最佳化和提高效率，並希望更好地將儲存和運算資源共置，以提高效能並減少停機時間。

為了解決這些問題，會議介紹了雲端儲存的關鍵創新，例如 Storage Intelligence Insight datasets。這項功能可幫助客戶使用 BigQuery 或 Looker 範本，或 Gemini 輔助的自然語言查詢，來詢問有關其資料的正確問題。Insight datasets 允許客戶每日快照物件和儲存桶的元資料，以便回答這些關鍵問題，而不會影響讀寫操作。

會議還介紹了 Anywhere Cache，這是一種連接到 Google Cloud Storage 儲存桶的基於 SSD 的讀取快取。Anywhere Cache 可幫助客戶輕鬆地將儲存和運算資源共置於現有的儲存桶中。Anywhere Cache 可加速讀取，每個區域的吞吐量高達 2.5 TB/s，並將首次位元組延遲時間縮短 70%。它也是一個很好的解決方案，可以與多區域儲存桶結合使用，以提供運算選擇和異地備援，而不會犧牲效能或成本效益。

此外，會議還討論了像 Uber 這樣的客戶，他們正在進行分析遷移和重構。有些客戶試圖將他們自行構建的資料湖遷移到 Google Cloud。他們的應用程式（如 Hadoop 和 Spark）期望獲得類似檔案的效能和治理。其他客戶則試圖重構其分析工作負載，以便在物件儲存上工作，以提高成本效益、可擴展性和易用性。但他們需要將效能提高 10 倍，包括吞吐量、延遲和每秒交易次數。他們通常還需要類似檔案的寫入語意，例如可附加性。

為此，會議介紹了雲端儲存的關鍵創新，例如階層式命名空間。客戶可以在建立儲存桶時啟用階層式命名空間，以獲得類似檔案系統的資料結構和 API。階層式命名空間可加速分析和 AI 工作負載，初始 QPS 提高 8 倍，從而更好地啟動工作負載，並實現原子資料夾重新命名，這對於 Hadoop Spark 任務處理以及 ML 檢查點通常至關重要。會議還討論了使用受管理資料夾在資料夾層級提供精細的 IAM 授權。

接下來，會議介紹了 Rapid Storage，這是一項私人預覽功能，可在專用的區域儲存桶中提供高效能的物件儲存。Rapid Storage 提供高達 6 TB/s 的吞吐量和每秒 2000 萬個請求，以及亞毫秒級的隨機讀取延遲，並透過新的基於 gRPC 的串流協定實現可附加寫入。

最後，Two Sigma 的客戶展示了他們如何使用雲端儲存和 Anywhere Cache 來構建單一資料平台，以滿足其高效能分析工作負載的需求。Two Sigma 能夠利用雲端儲存的關鍵創新，例如 AutoClass、階層式命名空間，尤其是 Anywhere Cache，這幫助他們提高了分析工作負載的規模和效能，而無需進行任何重大的程式碼變更，並顯著降低了其營運複雜性。

## 3. 重要結論

總而言之，雲端儲存是統一資料湖架構的基礎，無論您使用的是第一方、第三方還是自行構建的解決方案。它透過建立在雲端儲存上的通用資料平台以及在簡化性、效能和管理方面的關鍵創新，幫助您統一您的分析資料湖。
