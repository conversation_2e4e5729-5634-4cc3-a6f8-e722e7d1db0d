Data platform migrations Perspectives from industry insiders

[會議影片連結](https://www.youtube.com/watch?v=AVw610QmdiI)
資料平台遷移：業界人士的觀點

**1. 核心觀點**

本次會議主要探討了企業在進行資料平台遷移時面臨的挑戰、解決方案以及經驗分享。講者們來自不同產業，包括物流、廣告、金融和保險，他們分享了各自公司在資料平台遷移過程中的策略、實施方法和成果。核心觀點包括：

*   **資料治理的重要性：** 在遷移前進行資料盤點和清理，只遷移必要的資料，避免將過時或重複的資料帶入新的平台。
*   **選擇合適的雲端平台：** 根據自身需求選擇合適的雲端服務供應商，並充分利用雲端平台的彈性和可擴展性。
*   **使用者教育和培訓：** 在遷移過程中，需要對使用者進行充分的教育和培訓，確保他們能夠有效地使用新的平台。
*   **自動化的重要性：** 盡可能地自動化遷移過程，包括資料驗證和一致性檢查，以減少錯誤和提高效率。
*   **持續優化：** 遷移不是一次性的任務，而是一個持續優化的過程，需要不斷地監控和調整平台，以確保其性能和成本效益。
*   **業務參與：** 業務部門的參與至關重要，確保遷移過程與業務目標一致，並能為業務帶來實際價值。
*   **擁抱開源：** 善用開源技術，保持環境相容性，並降低成本。

**2. 詳細內容**

*   **J.B. Hunt (物流公司):**
    *   將傳統的 DB2 資料倉儲遷移到 BigQuery，實現了資料的集中化管理。
    *   透過資料清理，將報告數量從 12,000 個減少到 3,000 個，降低了遷移成本。
    *   利用 BigQuery ML 和 Vertex AI，將模型建立時間從數天縮短到數小時。
    *   將資料更新頻率從每日批次處理提高到每兩小時一次，提高了分析效率。
    *   強調 FinOps 的重要性，透過專案層級的預算控制和成本追蹤，實現資源的有效利用。

*   **CoreGraph (廣告公司):**
    *   建立了一個名為 Measure 的資料平台，使用 BigQuery 作為單一資料來源。
    *   將分散的平台整合到一個統一的環境中，包含約 10,000 個資料集、300 個專案和 2,000 個 Cloud Run 任務。
    *   透過 BigQuery 的無伺服器架構和安全功能，確保資料的安全性和合規性。
    *   強調使用者賦能、資料治理和持續回饋的重要性，以確保平台的成功採用。
    *   透過使用者培訓，建立八個不同的學習路徑，並訓練 AI 代理來回答使用者的問題。

*   **DBS (金融機構):**
    *   採用混合雲架構，同時在內部部署資料中心和 Google Cloud 上運行資料平台。
    *   使用平行運行的策略，將資料從新加坡遷移到印尼，以滿足資料監管要求。
    *   利用自動化的資料驗證引擎，確保資料在不同區域之間的一致性。
    *   在遷移過程中，將 Hive Schema 升級到 Iceberg，以提高資料處理效率。
    *   強調人員的重要性，包括專案執行團隊和業務分析師，確保遷移的順利進行和平台的有效使用。

*   **CNA Insurance (保險公司):**
    *   選擇 Google Cloud 作為其雲端平台，以取代老舊的 Oracle Exadata 環境。
    *   採用現代化的方法進行資料遷移，而不是簡單的「搬移」，以實現業務轉型。
    *   強調業務部門的參與，確保遷移過程與業務目標一致。
    *   透過資料治理和資料品質的提升，為未來的 AI 應用奠定基礎。

**3. 重要結論**

資料平台遷移是一個複雜但必要的過程，可以為企業帶來許多好處，包括提高效率、降低成本和實現業務轉型。成功的資料平台遷移需要仔細的規劃、有效的執行和持續的優化。此外，使用者教育、資料治理和業務參與也是至關重要的因素。透過分享這些經驗，講者們希望能夠幫助其他企業在資料平台遷移的道路上取得成功。
