Unlock cloud savings Gemini Cloud Assist and FinOps hub 2.0

[會議影片連結](https://www.youtube.com/watch?v=7_FVCPKpa7o)
解鎖雲端節省：Gemini Cloud Assist 和 FinOps Hub 2.0

## 1. 核心觀點

本次會議主要介紹 Google Cloud 如何透過 Gemini Cloud Assist 和 FinOps Hub 2.0 協助企業解鎖雲端節省。核心觀點包括：

*   **FinOps 的重要性：** FinOps 已成為管理雲端支出和評估雲端價值的必要實踐。
*   **Google Cloud 的 FinOps 工具演進：** 從 2022 年至今，Google Cloud 不斷推出新工具以支援 FinOps，並與 FinOps 的發展趨勢緊密結合。
*   **Gemini Cloud Assist 的應用：** 利用 Gemini Cloud Assist 簡化成本報告的建立，並提供更深入的成本洞察。
*   **FinOps Hub 2.0 的新功能：** FinOps Hub 2.0 透過利用率洞察 (Utilization Insights) 識別和減少浪費，並提供 Gemini 驅動的體驗以節省時間。
*   **專案計費成本管理員角色：** 允許專案負責人存取成本報告和 FinOps Hub 2.0，以便更好地管理其專案的雲端支出。
*   **Lloyd's Banking Group 的 FinOps 實踐：** Lloyd's Banking Group 分享了如何利用 FinOps Hub 提升其 Google Cloud 上的成本優化實踐。

## 2. 詳細內容

*   **FinOps 的演進：**
    *   2022 年：重點在於讓工程師採取行動並進行優化，Google Cloud 推出基礎資料層，提供豐富、細緻的成本資料。
    *   2023 年：重點在於提升 FinOps 在組織內的影響力，Google Cloud 推出 FinOps Hub，提供一站式雲端成本優化服務。
    *   2024 年：重點在於管理承諾和減少浪費，Google Cloud 推出即時費率建模優化、碳成本報告和細緻的異常偵測。
*   **客戶的 FinOps 實踐改進：**
    *   FinOps Hub 推出後，客戶的 FinOps 評分平均提高了 7%。
    *   使用 Gemini Cloud Assist 建立客製化報告，節省了超過 10 萬個 FinOps 小時。
    *   異常偵測功能推出後，偵測到近一百萬個成本異常。
*   **Lloyd's Banking Group 的 FinOps 實踐：**
    *   Lloyd's Banking Group 擁有複雜的混合和多雲環境，FinOps 團隊涵蓋公共和私有雲，並擴展到 SaaS 和其他技術成本項目。
    *   該公司利用 FinOps Hub 進行成本優化，並透過 API 匯出資料以視覺化儀表板。
    *   FinOps Hub 幫助該公司優先考慮影響最大的活動，並推動組織內的思維轉變。
    *   該公司透過 FinOps Hub 實施了近一千項建議。
*   **Gemini Cloud Assist 的新功能：**
    *   支援按 App Hub 應用程式檢視成本。
    *   提供更深入的成本洞察，包括專案、區域和 SKU 層級的成本驅動因素。
*   **FinOps Hub 2.0 的新功能：**
    *   利用率洞察 (Utilization Insights) 識別閒置資源、調整大小機會和次佳配置。
    *   廢棄物地圖 (Waste Map) 提供資源使用情況的簡單視圖，並按總廢棄物成本和資源數量對其進行分組。
    *   與 App Hub 整合，可透過特定應用程式的視角進行優化。
    *   Gemini 驅動的重點優先排序 (Focus Prioritization) 功能可識別具有最大機會的專案。
    *   Gemini 驅動的摘要和傳送 (Summarize and Send) 功能可草擬溝通內容，以便與 DevOps、財務和業務利害關係人協作。
*   **新的成本優化建議：**
    *   針對 GKE：識別閒置叢集、過度配置的叢集和效能或可靠性風險。
    *   針對 Compute Engine：擴展到不足配置的 VM，並提供閒置和部分使用的 GKE 預留建議。
    *   針對 Cloud SQL：擴展到不足配置的 SQL 執行個體。
    *   針對 Cloud Run：檢查 CPU 分配的特定配置，並建議更便宜的計費模型。
*   **專案計費成本管理員角色：**
    *   允許專案負責人存取成本報告和 FinOps Hub 2.0。
    *   可使用更細緻的權限包含在自訂角色中。
*   **其他功能：**
    *   支援帳單控制台中的深色模式。

## 3. 重要結論

Google Cloud 透過 Gemini Cloud Assist 和 FinOps Hub 2.0 提供強大的工具，協助企業更有效地管理雲端支出、減少浪費並優化工作負載。Lloyd's Banking Group 的成功案例證明了這些工具的價值。透過專案計費成本管理員角色，Google Cloud 正在賦予更多人權力來管理其雲端支出，並最終解鎖雲端的全部價值。
