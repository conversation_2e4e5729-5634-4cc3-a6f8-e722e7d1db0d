# Your model, your data Security and privacy protection for the Quantum Age
[會議影片連結](https://www.youtube.com/watch?v=jvWPfrhRUbM)
你的模型，你的數據：量子時代的安全與隱私保護

## 1. 核心觀點

本次會議主要探討在人工智慧時代，如何保障模型和數據的安全與隱私。核心觀點包括：

*   **隱私保護的重要性：** 在AI應用中，無論是模型訓練、微調還是推理，數據的機密性至關重要。
*   **不同行業的獨特需求：** 金融、醫療、政府等行業在數據安全和隱私方面有各自不同的挑戰和需求。
*   **機密運算的價值：** 機密運算技術能夠保護模型的智慧財產權，並促進安全的數據協作。
*   **量子計算的威脅與應對：** 量子計算的發展對現有加密體系構成威脅，需要提前部署量子安全加密技術。
*   **行業合作的重要性：** 需要整個行業共同努力，推動機密運算技術的發展和應用。

## 2. 詳細內容

*   **Nelly Porter（Google Cloud 產品管理總監）的開場：**
    *   強調了在AI時代，安全和隱私的重要性，並介紹了Google Cloud在機密運算方面的解決方案。
    *   指出不同行業的客戶在數據機密性方面有不同的需求，例如金融行業關注避免客戶間的數據洩露，醫療行業關注數據匿名化對模型品質的影響，政府部門則更關注即時資訊的安全性。
    *   介紹了機密運算如何幫助客戶保護模型智慧財產權，安全地進行數據協作，並滿足數據共享、執法存取和跨境數據傳輸等方面的政策要求。
    *   介紹了Google Cloud的機密雲產品組合，包括機密AI和機密空間，以及新發布的機密A3機器系列，該系列基於Intel機密運算環境和NVIDIA H100。
    *   強調了Google在量子安全加密方面的努力，包括在Chrome和Google網站上預設啟用量子安全傳輸，以及在Cloud KMS中提供量子安全數位簽章的預覽。

*   **Vint Cerf（網際網路先驅）的觀點：**
    *   強調了網際網路的開放性對AI發展的重要性，例如ImageNet的建立離不開開放的網際網路。
    *   指出機密運算對於保護AI模型的智慧財產權至關重要，可以確保只有客戶才能存取其AI工作的成果。
    *   介紹了Google的機密運算技術，允許客戶在GPU、TPU和CPU上執行加密的工作負載，包括軟體、AI模型和數據，Google無法存取這些數據。
    *   對AI的應用前景表示樂觀，例如Gemini工具在筆記方面的出色表現，以及Agentic AI的潛力。
    *   同時也提醒人們注意AI模型可能出現的意外行為，需要深入理解並加以控制。

*   **關於AI Agents的討論：**
    *   討論了AI Agents之間互操作性的問題，以及制定標準詞彙表的重要性，以避免Agents之間的混淆。
    *   強調了AI Agents的身份驗證和授權的重要性，以確保它們能夠安全地存取數據並執行任務。
    *   介紹了機密運算聯盟（Confidential Computing Consortium）的目標，即推動機密運算技術的標準化，以實現不同雲環境中Agents之間的互操作性。

*   **關於數位簽章和量子安全加密的討論：**
    *   強調了數位簽章在驗證網站和個人身份方面的重要性，以及AI Agents可能需要類似的強身份驗證機制。
    *   介紹了Google在後量子密碼學方面的努力，包括在Chrome客戶端上啟用量子安全會話，以及在Cloud KMS中支援量子安全數位簽章。
    *   強調了採用NIST標準的後量子密碼學的重要性，以保護客戶的資訊在未來25年內免受量子計算的威脅。

## 3. 重要結論

在AI時代，安全和隱私至關重要。機密運算技術是保護模型和數據安全的重要手段。同時，需要提前部署量子安全加密技術，以應對量子計算帶來的威脅。整個行業需要共同努力，推動機密運算技術的發展和應用，確保AI技術的安全可靠發展。
