# Google Cloud Next 25 in a minute
[會議影片連結](https://www.youtube.com/watch?v=OIJywyYQrd8)
Google Cloud Next 25 一分鐘精華

## 1. 核心觀點

本次會議主要介紹 Google Cloud 如何透過 Gemini 模型和新的工具，革新軟體開發流程，提升開發者效率。重點包括：

*   **新一代 Agentic 應用程式開發：** 推出 Agent Development Kit、Agent Engine 和 Agent Space，協助開發者構建、運行和管理 AI 代理程式，實現人機協作。
*   **提升開發者生產力：** 透過 Code Assist 和 Cloud Assist 代理程式，加速開發流程，簡化雲端操作，涵蓋整個軟體開發生命週期。
*   **Gemini 模型賦能：** 利用 Gemini 的強大上下文窗口和多模態支援，開發更強大的應用程式。
*   **AI Studio 體驗：** 開發者可以在 AI Studio 中試用最新的 Gemini 模型，包括 Gemini 2.5 Pro。

## 2. 詳細內容

*   **AI Studio 新功能展示：** 展示了 AI Studio 的全新 UI，包含最新的 Gemini 實驗模型、Google 搜尋工具，以及原生圖像編輯和生成等新功能。透過廚房改造的例子，展示了 Gemini 在圖像理解和生成方面的能力。
*   **Agent 的定義和應用：** Agent 是一種與 AI 模型互動的服務，利用工具和上下文資訊來執行目標導向的操作。會議展示了如何使用 Agent Development Kit (ADK) 建立一個協助承包商驗證建築法規和查詢許可證的 Agent。
*   **Agent Development Kit (ADK)：** ADK 是一個開源、模型無關的工具，支援模型上下文協定 (MCP)，可以標準化 LLM 處理資料請求所需的所有資訊。ADK 需要指令、工具和模型三個要素。
*   **Vertex AI：** Google Cloud 的端到端平台，用於構建和管理 AI 應用程式和 Agent，以及模型訓練和部署。
*   **Agent Engine 和 Agent Space：** Agent Engine 簡化了 Agent 的部署和運行，提供企業級安全控制、生產級監控和日誌記錄，以及評估和品質框架。Agent Space 是一個公司內部的 Agent 中心，可以在其中構建無程式碼 Agent，並註冊使用 ADK 構建的 Agent。
*   **多 Agent 系統：** 展示了如何使用 ADK 構建和協調多 Agent 系統，例如一個用於生成客戶提案、處理許可證和合規性，以及訂購和交付材料的系統。
*   **Cloud Assist Investigations：** 一個新的 Cloud Assist 功能，可以幫助診斷基礎設施問題和程式碼問題，並提供建議的程式碼編輯。
*   **Agent-to-Agent Protocol (A to A)：** 一個標準化的協定，用於發現和連接來自不同生態系統或供應商的 Agent。
*   **Gemini 在不同 IDE 中的應用：** 展示了如何在 Cursor、IntelliJ 和 Visual Studio Code 等 IDE 中使用 Gemini 進行程式碼編寫和測試。
*   **Vertex AI Model Garden：** 一個模型花園，可以連接到一些最流行的模型，或者從 Hugging Face 等註冊表中引入自己的模型。Model Garden 支援來自不同創建者的最新模型，包括 Llama、Gemma 3、Anthropic 和 Mistral。
*   **Gemini Code Assist 和 Firebase Studio：** Android Studio 現在支援 Gemini Code Assist，提供 Android 專用的 AI 功能。Gemini 和 Firebase 在全新的 Firebase Studio 中提供完整的 AI 輔助。
*   **Code Assist Kanban Board：** 一個新的軟體開發方式，透過 Agent 來協調軟體開發生命週期的各個方面。Kanban Board 包含一個 "背包"，其中包含所有工程上下文資訊，例如樣式指南、安全策略和格式設定偏好。
*   **VO2 展示：** 展示了 Google Cloud 和 Google DeepMind 如何與合作夥伴合作，利用 VO2 技術在娛樂領域實現新的可能性。

## 3. 重要結論

Google Cloud 正在透過 Gemini 模型和新的工具，為開發者提供更強大的能力，加速軟體開發流程，提升生產力。Agentic 應用程式、Code Assist、Cloud Assist 和 Agent Space 等新功能，將改變軟體開發的方式，實現人機協作，並為企業帶來更大的價值。
