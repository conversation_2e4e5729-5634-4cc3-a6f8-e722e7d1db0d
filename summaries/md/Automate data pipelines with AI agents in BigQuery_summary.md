# Automate data pipelines with AI agents in BigQuery
[會議影片連結](https://www.youtube.com/watch?v=60M7VelVCGM)
使用 AI Agents 在 BigQuery 中自動化資料管線

## 1. 核心觀點

本次會議主要探討如何利用 AI Agents 在 BigQuery 中自動化資料管線，解決傳統資料工程的痛點，並提升效率。核心觀點包括：

*   **AI Agents 的定義與應用：** AI Agents 能夠代表使用者追求目標、完成任務，並以主動的方式執行。在資料工程領域，AI Agents 將扮演輔助、半自主，甚至完全自主的角色。
*   **資料工程 Agents 的目標：** 專注於自動化資料管線的建構、管理與優化，減少資料工程團隊的人力負擔，並加速從原始資料到洞察的過程。
*   **解決資料工程的瓶頸：** 透過 AI Agents 簡化資料擷取、轉換、載入 (ETL) 流程，加速問題排除，並縮短從業務需求到實際應用的時間。
*   **Deloitte 的應用案例：** Deloitte 將利用 AI Agents 簡化資料遷移、資料管理等操作，為客戶提供更高效的服務。

## 2. 詳細內容

*   **AI Agents 的種類：** 除了資料工程 Agents，Google Cloud 還提供資料科學 Agents (Colab notebook 內嵌)、資料治理 Agents (metadata 管理、目錄更新) 和對話式分析 Agents (自然語言查詢)。
*   **資料工程 Agents 的功能：**
    *   **管線建構：** 透過自然語言或系統指令，自動建立資料管線，並支援 AI 資料準備工具。
    *   **管線管理：** 簡化管線的疑難排解、主動調整，並確保管線高效運行。
    *   **批量操作：** 支援批量建立、修改管線，例如一次性建立數百個管線，或根據 schema 變更批量更新管線。
*   **資料工程 Agents 的使用方式：**
    *   **低程式碼 UI：** 降低資料準備和管線建構的門檻，讓更多人參與資料處理。
    *   **CLI/API：** 方便資料工程師以程式化的方式管理管線，並整合到現有系統中。
*   **實際 Demo 展示：**
    *   **透過自然語言建立管線：** 使用者輸入簡單的指令，例如 "從 GCS 載入 CSV 檔案到 BigQuery table"，AI Agent 就能自動建立包含資料清理步驟的管線。
    *   **修改現有管線：** 根據使用者的指令，AI Agent 能夠修改現有管線，例如新增資料轉換步驟或聯結其他資料表。
    *   **透過 CLI 批量建立管線：** 使用者可以透過指令行工具，根據現有管線的指令，批量建立多個管線，並根據不同的資料來源進行客製化。
*   **Deloitte 的應用案例：**
    *   **法規報告自動化：** 針對金融機構的法規報告需求，AI Agent 能夠自動化資料模型映射、管線建立和資料品質監控，加速報告產出，並降低違規風險。
    *   **從報告需求到管線建立：** AI Agent 能夠解析法規報告的格式和說明文件，自動識別關鍵資料元素，並建立相應的資料管線和品質規則。

## 3. 重要結論

AI Agents 有望大幅簡化資料工程的流程，降低人力成本，並加速資料洞察的產生。Google Cloud 提供的資料工程 Agents 結合了自然語言處理、機器學習等技術，能夠自動化資料管線的建構、管理與優化。Deloitte 等合作夥伴也將利用 AI Agents 為客戶提供更高效的資料服務。
