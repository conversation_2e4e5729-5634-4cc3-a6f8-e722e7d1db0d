# Best practices for designing and deploying Cross-Cloud network security
[會議影片連結](https://www.youtube.com/watch?v=X0LQTHc1FOw)
設計和部署跨雲網路安全的最佳實踐

## 1. 核心觀點

本次會議主要探討了在設計和部署跨雲網路安全時的最佳實踐。講者們強調了雲安全威脅形勢的演變，包括大型語言模型（LLM）的雙面刃效應，以及合規性和資料外洩成本的日益增加。會議重點在於如何透過 Google Cloud 的網路安全產品組合，實現無縫整合、集中控制和全面強制的安全策略，以應對跨雲環境中的挑戰。此外，會議也分享了來自 Charles Schwab 和 DBS Bank 的客戶案例，展示了他們在雲網路安全方面的實踐經驗和學習心得。

## 2. 詳細內容

*   **雲安全威脅形勢：**
    *   LLM 的應用：雖然 LLM 可以用於安全編碼和漏洞發現，但也被攻擊者用於偵察、零日漏洞挖掘和惡意軟體開發。
    *   合規性壓力：企業因不合規而面臨巨額罰款，高層主管需負責年度合規審計。
    *   資料外洩成本：資料外洩成本持續攀升，常見原因包括錯誤配置、複雜的雲架構和零日漏洞。

*   **Google Cloud 的網路安全解決方案：**
    *   解決方案目標：提供無縫整合、集中控制和全面強制的安全策略，以應對跨雲環境中的挑戰。
    *   主要功能：
        *   DNS Armor：檢測 DNS 層的資料外洩攻擊（即將推出）。
        *   NGFW 網頁過濾：精細控制出站網頁流量（即將推出）。
        *   內聯網路 DLP：保護傳輸中的敏感資料。
        *   Cloud Armor 的階層式策略：已在白名單預覽中。
        *   NGFW 標籤增強：支援階層式策略和組織範圍。
        *   端到端 MTLS 安全性：實現零信任原則。
        *   服務擴充功能：支援第三方網路安全服務整合。

*   **關鍵功能深入探討：**
    *   DNS Armor：與 Infoblox 合作，提供雲原生體驗，早期檢測 DNS 層的威脅。
    *   內聯網路 DLP：透過網路強制點（如負載平衡器和安全網頁代理）提供資料可見性和控制，支援第一方和第三方 DLP 解決方案。
    *   安全態勢管理：使用雲原生結構（如網路類型）編寫不同流量路徑的策略，結合階層式安全策略和防火牆洞察，實現全面的安全態勢評估和監控。
    *   網路安全整合：透過防火牆策略規則進行流量導向和選擇，利用分散式強制執行，無需重新設計網路架構。

*   **部署模式和最佳實踐：**
    *   基礎架構：強大的網路架構是有效安全性的基礎，包括 NCC、混合連線模式（如 Cloud Interconnect 和 Cross-Cloud Interconnect）以及 Private Service Connect。
    *   安全控制：在 Hypervisor 層建立基本安全控制，並透過封包攔截技術擴展到進階安全功能。
    *   流量流向：涵蓋入站、出站、工作負載、服務和使用者流量，針對不同流量類型應用不同的安全產品和策略。
    *   分層防禦：結合 Cloud Armor、Cloud NGFW、安全網頁代理和第三方整合，建立深度防禦架構。

*   **客戶案例分享：**
    *   Charles Schwab：分享了其網路安全演進歷程，包括從單一 VPC 架構到分段式 VPC 架構的轉變，以及安全網頁代理和 Cloud NGFW 的實施經驗。
    *   DBS Bank：介紹了其雲採用策略，強調外部存取控制、有效的微分割和運營效率，並分享了使用階層式防火牆、Cloud NGFW 和安全網頁代理的實施經驗。

*   **示範：**
    *   展示了如何將第一方安全產品與第三方安全產品無縫整合，並將其連接到 SecOps 工具，實現威脅檢測和響應的自動化。

## 3. 重要結論

本次會議強調了在跨雲環境中設計和部署網路安全的重要性，並提供了實用的最佳實踐和解決方案。透過 Google Cloud 的網路安全產品組合，企業可以建立一個無縫整合、集中控制和全面強制的安全策略，以應對不斷演變的雲安全威脅。客戶案例分享和示範進一步展示了這些解決方案的實際應用和價值。
