```
# Hybrid LLMs for edge AI applications
[會議影片連結](https://www.youtube.com/watch?v=OqeiJ0es0sA)
混合型 LLM 用於邊緣 AI 應用

## 1. 核心觀點

本次會議主要探討了混合使用大型語言模型（LLM），結合雲端和邊緣運算的優勢，以提升 AI 應用程式的效能、隱私和可用性。核心觀點包括：

*   **混合 LLM 的優勢：** 結合雲端 LLM 的強大能力和邊緣 LLM 的低延遲、隱私保護和離線可用性。
*   **邊緣運算的必要性：** 在網路連線不穩定或需要保護使用者隱私的場景下，邊緣 LLM 尤其重要。
*   **MediaPipe 框架：** Google 的 MediaPipe 框架可簡化在邊緣裝置上部署和執行 LLM 的流程。
*   **多種混合架構：** 介紹了序列式、並行式和路由式等多種混合 LLM 架構，各有優缺點。
*   **效能評估的重要性：** 強調在選擇和部署 LLM 時，需要仔細評估模型的效能、延遲和準確性。

## 2. 詳細內容

*   **Gemini 模型系列：** 介紹了 Google 的 Gemini 模型系列，包括 Gemini 和 Gemma，前者提供強大的雲端 LLM 服務，後者是開源模型，可在邊緣裝置上部署。
*   **Gemini 和 Gemma 的選擇：** Gemini 提供最佳效能和即時資訊存取，而 Gemma 則提供更大的控制權和自訂性，適合在邊緣裝置上運行。
*   **邊緣 LLM 的優勢：** 低延遲、保護使用者隱私、離線可用性和零成本運行。
*   **MediaPipe 框架：** 介紹了 MediaPipe 框架，它是一個跨平台、可擴展的框架，用於構建高效的機器學習管線，並支援在 Web、iOS 和 Android 上部署。
*   **混合 LLM 架構：**
    *   **序列式（Cascading）：** 先使用邊緣 LLM，如果結果不滿意，再使用雲端 LLM。
    *   **並行式：** 同時使用邊緣和雲端 LLM，然後使用 response merger 決定返回哪個結果。
    *   **路由式：** 使用一個路由器模型（例如 MobileBERT）來決定將查詢發送到邊緣 LLM 還是雲端 LLM。
*   **路由模型訓練：** 使用 Gemini 模型生成的資料來微調 MobileBERT 模型，以提高路由決策的準確性。
*   **天氣應用程式範例：** 展示了一個天氣應用程式，該應用程式使用路由式混合 LLM 架構，根據查詢的內容和網路連線狀況，動態選擇使用邊緣 LLM 或雲端 LLM。
*   **效能考量：** 討論了延遲、網路連線和裝置效能等因素，以及如何根據這些因素優化混合 LLM 應用程式。
*   **未來方向：** 探討了多模態混合應用程式、模型微調和替代路由決策等未來發展方向。

## 3. 重要結論

混合 LLM 是一種有前景的方法，可以結合雲端和邊緣運算的優勢，以提升 AI 應用程式的效能、隱私和可用性。透過仔細選擇模型、架構和優化策略，開發人員可以構建強大的 AI 解決方案，滿足各種使用場景的需求。MediaPipe 框架為在邊緣裝置上部署和執行 LLM 提供了便利，而多種混合架構則提供了靈活性，可以根據具體需求進行調整。
```