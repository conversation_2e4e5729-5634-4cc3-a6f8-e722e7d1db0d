Unlock value for your workloads Microsoft, Oracle, OpenShift, and more

[會議影片連結](https://www.youtube.com/watch?v=epnQ7Rm40lE)
解鎖您在 Microsoft、Oracle、OpenShift 等工作負載的價值

## 1. 核心觀點

本次會議主要探討如何在 Google Cloud 上釋放企業工作負載的價值，特別是 Microsoft、Oracle 和 OpenShift 等。講者強調 Google Cloud 提供的基礎設施優勢、降低總體擁有成本 (TCO) 的方法，以及利用 AI 驅動現代化的能力。客戶案例 Blackline 和 Kinaxis 分享了他們將工作負載遷移到 Google Cloud 的經驗，以及如何從中受益。

## 2. 詳細內容

*   **Google Cloud 的價值主張：**
    *   **世界級的 IaaS：** Google Cloud 提供可靠、安全且高效能的基礎設施，專為企業關鍵任務工作負載而設計。
    *   **降低 TCO：** 透過客製化虛擬機器、優化授權成本和 AI 驅動的營運，Google Cloud 協助客戶降低總體擁有成本。
    *   **AI 驅動的現代化：** Google Cloud 提供完整的 AI 堆疊，包括自研晶片、模型和 API，使客戶能夠輕鬆地將 AI 整合到其傳統工作負載中。

*   **Microsoft 工作負載：**
    *   Google Cloud 為 Windows 和 SQL Server 工作負載提供強大的平台，擁有數萬名客戶。
    *   獨特的差異化價值包括客製化機器類型 (Custom Machine Types, CMT)、SMTO (Simultaneous Multi-Threading Off) 和現代化解決方案。
    *   客製化機器類型允許客戶根據其特定需求配置 CPU 和 RAM，從而節省 SQL Server 授權成本。
    *   SMTO 允許客戶關閉超執行緒，從而降低授權成本。
    *   Google Cloud 提供一系列託管服務，例如 GKE (Google Kubernetes Engine) 和 Cloud SQL，以簡化 Windows 和 SQL Server 工作負載的管理。
    *   推出 License Manager 服務，允許客戶在平台上採購 Office 等授權。

*   **SQL Server 解決方案：**
    *   Workload Manager for SQL Server 是一個自動化服務，可協助客戶確保其 SQL Server 部署已正確配置並遵循最佳實務。
    *   推出一個簡單的點擊式精靈，用於在平台上部署和配置 SQL Server 部署。
    *   HyperDisk Multi-Writer for HA 是一種原生解決方案，可簡化故障轉移叢集 (FCI) 的管理。
    *   Google Cloud BCDR 是一種全面的業務連續性和災難復原 (BCDR) 解決方案，支援 SQL Server。
    *   HyperDisk Cross-Region Async Replication 是一種經濟高效的災難復原解決方案，可將磁碟跨區域複製。

*   **Blackline 客戶案例：**
    *   Blackline 是一家領先的財務結算管理平台，正在將其工作負載遷移到 Google Cloud。
    *   Blackline 選擇 Google Cloud 是因為其支援 Microsoft 工作負載以及現代化未來工作負載的能力。
    *   Blackline 透過從 AOAG (Always On Availability Groups) 遷移到 FCI，節省了超過 35% 的成本。

*   **現代化和創新：**
    *   Google Cloud 提供一系列 AI 驅動的解決方案，以協助客戶現代化其 Windows 和 SQL Server 工作負載。
    *   Gemini-powered assessment report 可分析 .NET 程式碼庫，並提供現代化的建議。
    *   Gemini-powered agentic .NET modernization 解決方案可自動轉換程式碼。
    *   Database Migration Service 可協助客戶將 SQL Server 資料庫遷移到 Cloud SQL for PostgreSQL。

*   **Kinaxis 客戶案例：**
    *   Kinaxis 是一家供應鏈技術公司，正在使用 Google Cloud 的 AI 功能來現代化其應用程式。
    *   Kinaxis 選擇 Google Cloud 是因為其可擴展性、全球覆蓋範圍和技術專業知識。
    *   Kinaxis 正在使用客製化機器類型和 SMT 來優化其 Windows 授權成本。

*   **Oracle 工作負載：**
    *   Google Cloud 與 Oracle 建立了合作夥伴關係，為 Oracle 堆疊提供支援。
    *   客戶可以在 Google Cloud 上執行 Oracle Linux、Oracle Database 和 Oracle 應用程式。
    *   推出可在 GCP 中使用的 Oracle Linux 映像。

*   **OpenShift 工作負載：**
    *   Google Cloud 與 Red Hat 建立了長期戰略合作夥伴關係，提供託管和自我管理的 OpenShift。
    *   OpenShift 客戶可以利用 Google Cloud 的功能來降低 TCO 並整合 AI 服務。

## 3. 重要結論

Google Cloud 提供了一個強大的平台，可為企業工作負載提供支援，特別是 Microsoft、Oracle 和 OpenShift。透過利用 Google Cloud 的基礎設施優勢、降低 TCO 的方法和 AI 驅動的現代化能力，客戶可以釋放其工作負載的價值並推動業務成果。客戶案例 Blackline 和 Kinaxis 強調了 Google Cloud 如何協助他們實現成本節省、提高可擴展性和加速創新。
