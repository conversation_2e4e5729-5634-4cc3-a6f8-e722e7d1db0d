# What’s next with generative AI at Google Cloud_2
[會議影片連結]()
What’s next with generative AI at Google Cloud_2

## 1. 核心觀點

本次會議主要探討 Google Cloud 在生成式 AI 領域的最新進展，重點介紹了 AgentSpace 和 Vertex AI 兩大平台，以及 Gemini 模型系列的最新功能和應用。會議強調了 Google Cloud 在加速 AI 代理開發和部署、管理多代理系統、以及在企業內部安全地擴展 AI 應用方面的能力。此外，會議還邀請了 Citi 和 Pearson 等客戶分享他們在 AI 應用方面的經驗。

## 2. 詳細內容

**Vertex AI：模型、工具與基礎設施**

*   **Model Garden：** 提供精選的最佳模型，而非追求模型數量。
*   **Gemini 模型：** 強調 Gemini 的快速成長和在各行業的廣泛應用，包括 Moody's 在報告生成、Palo Alto Networks 在威脅檢測和客戶支援、以及 Box 在非結構化資料分析方面的應用。
*   **Gemini 2.5 Pro：** 具備進階推理能力，並提供「思考預算」功能，讓開發者可以控制成本與品質之間的平衡。
*   **Gemini 2.5 Flash：** 針對低延遲和降低成本進行優化，適用於高流量場景。
*   **Live API：** 允許即時音訊、視訊和文字串流，實現與模型的低延遲對話，打破類比和數位之間的界限。
*   **Vio：** 影片建立和編輯平台，提供內繪、外繪和相機預設等功能。
*   **開放合作夥伴模型生態系統：** 提供多種模型選擇，包括 AI21、Anthropic、Meta、DeepSeek 和 Gemma。
*   **Vertex AI Model Optimizer：** 根據查詢複雜度將請求路由到適當的 Gemini 模型，以確保高品質的回應。
*   **Vertex Model Development Service：** 簡化基礎模型的建立和訓練過程，讓 ML 團隊可以專注於模型開發，而非基礎設施管理。
*   **Agent Eval：** 允許以單行程式碼觸發代理執行，以取得評估指標。
*   **Vertex Global Endpoint：** 全球負載平衡系統，提供高可用性和自動故障轉移。

**Agent Builder：簡化多代理系統的開發**

*   **Agent Development Kit (ADK)：** 開放原始碼框架，簡化多代理系統的建立過程，允許使用不同的模型和框架。
*   **預建連接器：** 簡化資料存取，並支援 MCP 標準。
*   **Agent-to-Agent Protocol (A2A)：** 旨在讓不同生態系統中的代理能夠相互通訊，目前有超過 50 個合作夥伴參與開發。
*   **Agent Engine：** 完全託管的企業級執行環境，簡化代理的部署和擴展。

**AgentSpace：解鎖企業資料的潛力**

*   **多模態搜尋體驗：** 允許跨越資料孤島，搜尋任何格式的資訊。
*   **Chrome 整合：** 將搜尋功能直接整合到 Chrome 瀏覽器中。
*   **AgentGallery：** 集中管理企業批准的代理，包括 Google 專家代理、合作夥伴代理和員工建立的代理。
*   **Idea Generation Agent：** 自主開發新穎的想法，並評估其可行性。
*   **Deep Research Agent：** 將數小時的工作濃縮為單一提示，從內部來源合成資訊。
*   **Agent Designer：** 讓員工能夠建立自己的代理，同時遵守資料安全和存取控制。

**客戶案例**

*   **Citi：** 利用 AI 處理市場情報、建立虛擬助理和自動化程式碼審查。
*   **Pearson：** 使用 AI 提供個人化的學習體驗，並協助學生和教育工作者。

**企業準備度**

*   強調資料治理、隱私、安全、合規、可靠性和永續性。
*   客戶擁有其資料，Google 提供資料隱私和控制。
*   提供生成輸出和訓練資料的賠償保證。
*   推出主權 AI 服務，讓企業可以更好地控制 AI 部署。
*   Google Distributed Cloud 將支援 AgentSpace 和 Gemini，以便在內部環境中使用。

## 3. 重要結論

Google Cloud 正在積極推動生成式 AI 的發展，並提供全面的平台和工具，協助企業建立、部署和管理 AI 代理。透過 AgentSpace 和 Vertex AI，企業可以解鎖資料的潛力，提高生產力，並創造新的商業價值。Google Cloud 強調開放性、選擇性和企業準備度，致力於讓 AI 成為企業成功的關鍵驅動力。
