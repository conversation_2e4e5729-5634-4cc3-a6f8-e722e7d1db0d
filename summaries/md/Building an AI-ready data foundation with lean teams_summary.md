# Building an AI-ready data foundation with lean teams
[會議影片連結](https://www.youtube.com/watch?v=i4kQgJi0sCM)
建構具備 AI 能力的資料基礎，並由精簡團隊執行

## 1. 核心觀點

本次會議主要探討如何利用精簡團隊，在 BigQuery 和 Google Cloud 上建立一個具備 AI 能力的資料基礎。會議強調了資料孤島、AI 準備度不足以及成本增加是企業在 AI 轉型過程中面臨的主要挑戰。BigQuery 如何透過整合資料、AI 和治理，以及提供跨雲支援和開放平台，來解決這些挑戰。同時，與會者分享了他們在不同產業和業務中使用 BigQuery 的經驗，強調了集中化、簡化、加速價值實現以及賦能團隊的重要性。

## 2. 詳細內容

會議首先點出企業在實現 AI 驅動轉型時遇到的三大阻礙：

*   **資料孤島：** 結構化和非結構化資料分散在多個雲端和本地環境中，存在多個副本。
*   **AI 準備度不足：** AI 模型的品質取決於資料品質，不良的資料品質會危及 AI 模型。缺乏統一的資料和 AI 治理。
*   **成本增加：** 現有系統無法隨著資料量的增長而擴展。AI 開發和部署成本高昂，且通常是附加元件而非原生整合。資料在不同系統和儲存元件之間的移動成本效益不佳。

為了解決這些挑戰，BigQuery 將資料和 AI 的優勢結合在一個平台中，旨在提高企業的速度和敏捷性。它有助於為組織創造短期和長期價值，並結合了從資料到 AI、治理、處理引擎以及 BI 層的所有內容。該平台的核心是多模型資料基礎層，使用者可以在單一一致的環境中操作。BigQuery 的跨雲功能擴展到 Google Cloud 之外，支援 AWS、Azure 等其他超大規模供應商，確保客戶不會被供應商鎖定，並提供靈活性。統一的資料和 AI 治理提供集中化的策略執行、安全性和元資料智慧，允許組織跨雲端無縫地定義、保護和監控其資料和 AI 資產。由 Vertex AI 驅動的深度 AI 整合將 AI 原生功能直接引入查詢中。各種引擎支援 SQL、Python、Spark，用於串流資料、查詢和對資料採取行動。

Google Cloud 提供多種資源來支援客戶的遷移旅程，包括資料遷移激勵計畫、專業服務組織 (PSO) 和客戶工程團隊。Google Cloud 也與技術公司密切合作，為客戶提供選擇，展現了其對合作夥伴的承諾以及成為最開放雲端供應商的決心。BigQuery 遷移服務可以輕鬆地將資料遷移到 BigQuery，並支援 15 種以上的查詢翻譯來源。

與會者分享了他們使用 BigQuery 的經驗：

*   **Yasser (Marius)：** Yasser 是一家在北非營運的超級應用程式公司，面臨資料分散在 Databricks 和 BigQuery 的挑戰。他們決定將所有資料遷移到 BigQuery，並在 Vertex AI 上建立 ML 平台，以實現集中化和簡化。BigQuery 的整合性、易於使用的功能以及與 Terraform 的良好相容性是他們選擇 BigQuery 的關鍵原因。
*   **SmarterX (Charlie)：** SmarterX 是一家提供法規分類的公司，需要將資料、LLM 和運算資源集中在同一個地方。BigQuery 讓他們能夠更快地構建 AI 模型，並更輕鬆地處理各種資料格式。
*   **MarketMind (Tom)：** MarketMind 是一家金融科技新創公司，需要建立一個新一代的投資者關係平台。他們選擇 BigQuery 作為資料儲存和分析的基礎，並利用 Vertex AI 建立一個代理式的 Gen AI 平台。BigQuery 的可擴展性和整合性使他們能夠以精簡的團隊完成工作。
*   **Reverse (Shin-yung)：** Reverse 是一家全球粉絲平台，面臨資料儲存和分析環境分離的挑戰。他們選擇 BigQuery 是因為其整合性、效能和靈活性，以及簡化 AI 基礎架構的能力。

## 3. 重要結論

BigQuery 是一個強大的平台，可以幫助企業建立具備 AI 能力的資料基礎，並由精簡團隊執行。透過集中化資料、簡化工作流程、加速價值實現以及賦能團隊，BigQuery 可以幫助企業在 AI 轉型中取得成功。對於正在考慮遷移到 BigQuery 的企業，與會者建議從小型專案開始，並逐步擴展到更大的資料集。他們還強調了與 Google Cloud 團隊和合作夥伴合作的重要性，以獲得必要的支援和專業知識。
