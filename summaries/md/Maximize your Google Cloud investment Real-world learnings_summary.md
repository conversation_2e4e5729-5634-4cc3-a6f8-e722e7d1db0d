# Maximize your Google Cloud investment Real-world learnings
[會議影片連結](https://www.youtube.com/watch?v=M8aD3ghd5l4)
最大化您的 Google Cloud 投資：真實世界的經驗

## 1. 核心觀點

本次會議主要探討了 Shopify 如何利用 Google Cloud 的社群使用折扣（Community Use Discounts，CUDs）和 Google Cloud 定價計算器來優化其雲端投資。核心觀點包括：

*   **社群使用折扣（CUDs）的價值：** CUDs 提供顯著的成本節省，尤其是在 GCE（Google Compute Engine）上，透過承諾使用一定量的資源，可以獲得比隨需定價更低的價格。
*   **集中管理 CUDs 的重要性：** 集中管理 CUDs 可以確保資源在整個組織內共享，避免資源浪費，並最大化折扣效益。
*   **優化工作負載的重要性：** 在應用 CUDs 之前，必須先優化工作負載，確保資源得到有效利用，避免購買了過多的承諾資源。
*   **Google Cloud 定價計算器的作用：** 透過連結帳單帳戶，Google Cloud 定價計算器可以提供更準確的成本估算，幫助團隊更好地預測和管理雲端支出。
*   **持續學習和調整策略：** 雲端環境不斷變化，需要持續學習新的技術和方法，並根據實際情況調整 CUDs 的使用策略。

## 2. 詳細內容

*   **社群使用折扣（CUDs）：**
    *   CUDs 分為資源型和消費型兩種。資源型 CUDs 適用於 GCE，需要承諾一定數量的核心、記憶體等資源。消費型 CUDs 適用於 GCE、Dataflow、Bigtable 等服務，需要承諾一定的消費金額。
    *   Shopify 同時使用資源型和消費型 CUDs，根據不同的工作負載選擇不同的 CUDs 類型。
    *   CUDs 提供可預測性、彈性承諾和長期一致性。
    *   與 AWS 的承諾不同，Google 的 CUDs 沒有前期費用。
    *   Shopify 主要使用 1-3 年的 CUDs，穩定工作負載使用 3 年，實驗性工作負載使用 1 年。
    *   Shopify 採用分批購買和階梯式購買策略，避免一次性購買大量 CUDs，並根據實際需求逐步增加承諾資源。
    *   講者提到 ProsperOps 的一份報告指出，60% 的客戶使用 CUDs，而 40% 的客戶完全未使用，顯示了 CUDs 的巨大節省潛力。
    *   Flex CUDs 不受機器類型或區域限制，更具彈性，但折扣較低。Shopify 主要使用資源型 CUDs，因為折扣更高。
    *   消費型 CUDs 適用於 Bigtable、Dataflow 等服務，但並非所有 SKU 都適用，需要注意。

*   **CUDs 的最佳實踐：**
    *   集中管理 CUDs，避免團隊成員私自購買，導致資源浪費。
    *   啟用 CUDs 共享，確保資源在整個組織內共享。
    *   在應用 CUDs 之前，必須先優化工作負載，確保資源得到有效利用。
    *   採用更廣泛的覆蓋策略，分散承諾，避免過於集中的到期日。
    *   合併和拆分承諾，根據實際需求調整資源配置。
    *   使用標籤標記 CUDs，方便追蹤和管理。

*   **Google Cloud 定價計算器：**
    *   Google Cloud 定價計算器可以幫助團隊估算 Google Cloud 服務的成本。
    *   透過連結帳單帳戶，可以獲得更準確的成本估算，因為折扣會自動應用。
    *   這使得工程團隊可以更好地預測新應用或功能的成本，並做出更明智的決策。
    *   可以更好地了解不同區域的價格差異，並評估消費型 CUDs 的效益。
    *   不再需要手動查詢價格表，簡化了成本估算流程。

*   **問答環節：**
    *   討論了如何應對 Google Cloud 新機器類型的發布，以及如何評估遷移到新機器類型的成本效益。
    *   Shopify 的 CUDs 覆蓋率目標是 80%，以保持彈性，應對規模擴縮。
    *   討論了如何建立成本優化文化，以及如何教育團隊了解 CUDs 的優缺點。
    *   針對從本地環境遷移到 Google Cloud 的公司，建議先使用消費型 CUDs 或 Flex CUDs，以降低風險。
    *   討論了如何標記 CUDs，以及如何使用內部系統追蹤 CUDs 的歷史記錄。
    *   討論了如何平衡資源停車（關閉未使用的系統）和 CUDs 的使用。
    *   討論了如何使用 Flex CUDs 進行探索性專案。
    *   討論了如何優化 File Store、備份和跨區域複製等服務的成本。
    *   討論了如何使用 GPU，以及如何評估 GPU 的 CUDs。
    *   討論了 FinOps 團隊的組成和規模。
    *   討論了如何應對市場波動和全球不穩定性。
    *   討論了如何將 AI 應用於 FinOps。

## 3. 重要結論

Shopify 透過集中管理 CUDs、優化工作負載和使用 Google Cloud 定價計算器，成功地優化了其 Google Cloud 投資。本次會議分享了 Shopify 的實戰經驗，為其他希望降低雲端成本的公司提供了寶貴的參考。 此外，講者強調了 FinOps 團隊的重要性，以及建立成本優化文化的重要性。
