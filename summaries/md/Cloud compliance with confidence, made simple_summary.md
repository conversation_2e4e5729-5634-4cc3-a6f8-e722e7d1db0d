Cloud compliance with confidence, made simple

[會議影片連結](https://www.youtube.com/watch?v=k-GOBVNwXJg)
雲端合規，簡單又安心

## 1. 核心觀點

本次會議主要探討如何透過 Google Cloud 的 Compliance Manager 和 Assured Workloads 等工具，簡化雲端合規流程，並在不斷變化的法規環境下保持信心。核心觀點包括：

*   **合規與主權挑戰：** 法規不斷變化，地緣政治局勢帶來新的要求，工具分散難以管理。
*   **簡化合規：** 從配置到監控再到稽核，提供端到端的合規環境，並利用 AI 提升效率。
*   **Assured Workloads 的作用：** 透過軟體定義的控制項，在單一 GCP 組織中滿足多種法規要求，無需重新架構。
*   **AI 在合規中的應用：** 利用 AI 生成新的控制項、分析法規框架，並協助建立客製化控制項。
*   **全球部署與規模化：** Assured Workloads 允許全球部署，無需為不同地區的合規要求進行妥協。
*   **數位主權：** 從資料、營運和軟體三個層面看待主權，Assured Workloads 主要提供資料主權，並在營運主權方面與合作夥伴合作。

## 2. 詳細內容

*   **Compliance Manager 示範：**
    *   集中化的儀表板，顯示跨多個框架（如 AI Protection、CIS、Assured Workloads FedRAMP-HI）的合規狀態。
    *   可查看整體合規分數、框架覆蓋率和關鍵發現。
    *   可篩選發現，查看違規資源的詳細資訊，並獲得修復指南。
    *   可管理框架，部署預先建立的框架或建立自訂框架。
    *   利用 AI 建議控制項，並自動產生自訂控制項的程式碼。
    *   可執行稽核，自動收集稽核證據，並產生報告。

*   **Assured Workloads 的優勢：**
    *   透過軟體定義的控制項，在 Google Cloud Platform 上實現合規。
    *   支援多種法規要求，包括 IL-5、FedRAMP、HIPAA、CGIS 等。
    *   提供資料落地、存取控制、Assured Support、金鑰管理和加密等控制項。
    *   允許全球部署，無需重新架構。
    *   利用 Google Cloud 的完整規模和基礎架構，包括 TPU 和 GPU。

*   **數位主權的三個層面：**
    *   **資料主權：** 控制資料的落地和金鑰。
    *   **營運主權：** 控制控制項的營運和雲端的營運，透過與當地合作夥伴合作實現。
    *   **軟體主權：** 控制軟體，確保雲端在斷線情況下也能繼續運作，透過 Trusted Partner Cloud 和 Google Distributed Cloud 實現。

*   **AI 在合規中的應用：**
    *   Cloud AI 服務已獲得美國公共部門的授權，可用於 FedRAMP-I 和 IL-5 環境。
    *   Gen AI 和 Vertex AI 等服務已擴展到 Assured Workloads，並具有資料位置、本地 ML 處理和外部存取保護等控制項。
    *   Compliance Manager 將包含 Google Cloud Security Baseline，提供良好的合規起點。
    *   Gemini Cloud Assist 可協助使用者了解如何滿足 FedRAMP 要求。

*   **客戶案例分享：**
    *   **SAP：** 利用 Assured Workloads 滿足本地資料落地和境內控制等要求，實現全球交付。
    *   **Palo Alto Networks：** 使用 Assured Workloads 建立第一個 FedRAMP-High 授權環境，確保資料留在美國境內。
    *   **Pexip：** 在視訊平台即服務中使用 Assured Workloads，提供易於使用且靈活的解決方案，滿足客戶嚴格的主權要求。

## 3. 重要結論

Google Cloud 的 Compliance Manager 和 Assured Workloads 提供了一套全面的工具，可簡化雲端合規流程，並在不斷變化的法規環境下保持信心。透過軟體定義的控制項、AI 輔助和全球合作夥伴關係，Google Cloud 能夠滿足各種合規和主權要求，並協助客戶在全球範圍內安全地部署和擴展其雲端工作負載。
