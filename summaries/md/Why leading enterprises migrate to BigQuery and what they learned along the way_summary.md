```
# Why leading enterprises migrate to BigQuery and what they learned along the way
[會議影片連結](https://www.youtube.com/watch?v=XxOjvZdoCgY)
為什麼領先企業遷移到 BigQuery 以及他們一路走來的經驗

## 1. 核心觀點

本次會議主要探討了企業遷移到 Google BigQuery 的原因、方法以及過程中獲得的經驗教訓。核心觀點包括：

*   **現代化的必要性：** BigQuery 提供數據、AI 和各種類型的數據於單一平台，是企業現代化的理想選擇。
*   **BigQuery 遷移服務的優勢：** Google 提供一系列免費的遷移服務，並透過 Gemini 和 AI 來減少人工干預和錯誤。
*   **遷移的考量因素：** 企業在決定遷移時，需要考慮風險、價格效能和複雜性等因素。
*   **遷移生命週期的全面性：** BigQuery 遷移服務涵蓋遷移生命週期的所有階段，並提供自動化和智慧化的工具。
*   **自動化程度高：** BigQuery 遷移服務在自動評估和程式碼翻譯方面具有很高的自動化率。
*   **Gemini 的應用：** 透過 Gemini 增強的翻譯功能，可以處理各種程式碼，並提供優化的建議。
*   **資料治理的重要性：** 資料沿襲 (Lineage) 功能對於資料治理和符合法規至關重要。
*   **成本效益：** BigQuery 在成本效益方面具有優勢，可以降低企業的總體擁有成本 (TCO)。
*   **遷移是一個大型專案：** 遷移是一個複雜的過程，需要充分的溝通、盤點和規劃。

## 2. 詳細內容

會議首先介紹了 BigQuery 的優勢，強調其作為一個統一的數據、AI 平台，能夠滿足企業現代化的需求。接著，詳細介紹了 BigQuery 遷移服務，包括其免費使用、API 存取、CLI/UI 支援以及針對客戶和合作夥伴的激勵計畫。

會議重點介紹了 BigQuery 遷移服務的各項功能，包括：

*   **Gemini 增強的批次和 API 翻譯：** 支援超過 15 種來源，並提供大規模的程式碼翻譯能力。
*   **Gemini 啟用的翻譯：** 允許在翻譯工作負載進入編譯器引擎之前調用 Gemini，從而可以處理各種格式的程式碼。
*   **來源沿襲 (Source Lineage)：** 提供系統的依賴關係圖，幫助企業了解遷移的影響。
*   **輕量級評估：** 提供針對 Cloudera/Hive、Snowflake、Redshift 和 Oracle 的總體擁有成本 (TCO) 評估。
*   **資料遷移：** 提供針對 Cloudera、Snowflake 和 Teradata 的資料遷移功能，並針對 Teradata 實現增量壓縮。
*   **元數據和治理：** 提供針對 Cloudera 的元數據和治理功能。

會議還展示了資料沿襲 (Lineage) 和 Gemini 增強翻譯的 Demo，展示了這些功能在簡化遷移過程和提高程式碼品質方面的作用。

來自 Ford Credit 的 Rizal 分享了遷移到 BigQuery 的經驗教訓，強調了充分溝通、盤點、規劃和測試的重要性。他還提到，遷移是一個大型專案，需要考慮資料驗證、平行運行、報告遷移和系統退役等因素。

來自 Quest Diagnostics 的 Mark 分享了選擇 Google 和 BigQuery 作為戰略合作夥伴的原因，強調了 GenAI 和 Agentic AI 在推動數據洞察和業務轉型方面的潛力。他還展示了 Quest Data Fabric 的願景，旨在讓業務使用者能夠以更自然的方式與數據互動。

## 3. 重要結論

本次會議全面介紹了 BigQuery 遷移的各個方面，從策略規劃到具體實施，為企業提供了寶貴的參考。透過 BigQuery 遷移服務和 Gemini 的應用，企業可以更輕鬆、更高效地將其數據和應用程式遷移到 Google Cloud，並充分利用 BigQuery 的強大功能，實現業務轉型和創新。同時，企業也需要充分認識到遷移的複雜性，並做好充分的準備和規劃，才能確保遷移的成功。
```