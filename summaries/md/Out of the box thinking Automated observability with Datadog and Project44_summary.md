# Out of the box thinking Automated observability with Datadog and Project44
[會議影片連結](https://www.youtube.com/watch?v=5-bjwVsgMG0)
跳脫框架思考：使用 Datadog 和 Project44 實現自動化可觀測性

## 1. 核心觀點

本次會議主要探討了如何利用 Datadog 和 Project44 實現自動化可觀測性，從而提升開發效率、降低問題發現時間，並最終改善客戶體驗。核心觀點包括：

*   **自動化是關鍵：** 透過自動化，開發者可以將更多精力集中在核心業務邏輯上，而非繁瑣的基礎設施管理和監控配置。
*   **可觀測性平台的重要性：** Datadog 作為可觀測性平台，能夠提供對基礎設施和應用程式運行狀況的深入洞察，幫助快速定位和解決問題。
*   **基礎設施即代碼（Infrastructure as Code, IaC）：** 利用 Terraform 等工具，可以將基礎設施配置納入版本控制，實現可重複、可審計的部署流程。
*   **標準化和模組化：** 透過建立標準化的模組，可以簡化監控配置，並確保一致性。
*   **SLO（Service Level Objective）的重要性：** 基於 SLO 的監控可以更準確地反映客戶體驗，並幫助團隊優先處理影響客戶的事件。

## 2. 詳細內容

**Datadog 的角色：**

*   Datadog 是一個可觀測性和安全平台，為組織提供對其基礎設施和應用程式運行狀況的深入了解，並提供改善系統安全性的方法。
*   Datadog 擁有超過 850 個整合，可以自動將資料饋送到 Datadog 中，方便使用者獲取資訊。
*   Datadog 提供服務目錄和事件管理工具，並新增了隨時待命功能，專注於開發人員體驗，自動化並簡化他們的生活。

**Project44 的實踐：**

*   Project44 旨在建立整個供應鏈的連接組織，並透過該供應鏈改善客戶體驗。
*   Project44 透過追蹤全球所有模式和地區的貨物，建立庫存可見性產品，轉運點效率產品，以及基於 AI 的供應鏈情報。
*   Project44 建立了一個平台，簡化開發人員的生活，並更容易專注於問題。
*   Project44 透過標準化的模組，簡化監控配置，並確保一致性。
*   Project44 使用 Atlantis 進行 Terraform 自動化，透過 GitHub 進行基礎設施變更的審查和部署。
*   Project44 透過 Admission Controller 自動注入 Datadog tracing library，簡化追蹤設定。
*   Project44 建立基於黃金訊號（Golden Signals）和 SLO 的監控模組，幫助開發者專注於重要的指標。
*   Project44 使用 Datadog scorecards 追蹤工程團隊對自動化工具和最佳實踐的採用情況。

**自動化可觀測性的具體步驟：**

1.  **建立自服務平台：** 讓開發者能夠自助申請基礎設施資源，減少營運團隊的瓶頸。
2.  **使用基礎設施即代碼：** 利用 Terraform 等工具管理基礎設施配置，實現版本控制和自動化部署。
3.  **自動化追蹤設定：** 透過 Admission Controller 自動注入 tracing library，簡化追蹤設定。
4.  **建立標準化的監控模組：** 建立基於黃金訊號和 SLO 的監控模組，簡化監控配置。
5.  **使用自動化工具：** 利用 Datadog workflow automation 等工具，自動執行常見的營運任務。
6.  **追蹤採用情況：** 使用 Datadog scorecards 追蹤工程團隊對自動化工具和最佳實踐的採用情況。

## 3. 重要結論

透過 Datadog 和 Project44 的合作，展示了自動化可觀測性如何顯著提升開發效率、降低問題發現時間，並最終改善客戶體驗。自動化不僅簡化了開發者的工作，也提高了系統的穩定性和可靠性。鼓勵大家思考如何將這些實踐應用到自己的組織中，以提升開發效率和改善客戶體驗。
