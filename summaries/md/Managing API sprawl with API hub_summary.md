# Managing API sprawl with API hub
[會議影片連結](https://www.youtube.com/watch?v=hZ7fRpGBKA0)
使用 API Hub 管理 API 擴散

## 1. 核心觀點

本次演講主要介紹如何使用 API Hub 來管理 API 的擴散問題。API 擴散指的是 API 在不同的閘道和工具中分散，導致難以管理、發現和治理。API Hub 作為 API 生態系統的神經中樞，可以整合來自不同來源的 API 資訊和元數據，提供單一的企業真相來源，從而解決 API 擴散帶來的挑戰。

## 2. 詳細內容

首先，講者描述了 API 擴散的常見場景，即 API 分散在不同的閘道中，而 API 的元數據也分散在多個工具中。這對平台負責人和開發人員都帶來了挑戰。平台負責人難以監控新 API 是否符合公司標準，而開發人員則難以發現現有的 API，導致 API 重複建設。

API Hub 的核心功能包括：

*   **集中管理：** 從任何閘道和元數據來源匯入 API 資訊和元數據，形成單一的企業真相來源。
*   **API 探索：** 提供 API 目錄，方便開發人員發現和使用現有的 API。
*   **生命週期和治理：** 監控 API 的生命週期，並確保 API 符合公司標準。API Hub 可以在發現不符合標準的 API 時發出警報。
*   **語意搜尋：** 允許開發人員使用自然語言搜尋 API，提高 API 的發現效率。
*   **洞察頁面：** 提供 API 生態系統的即時資訊，包括最常用的 API、API 的使用情況以及 API 的問題。
*   **供應鏈圖：** 顯示 API 之間的依賴關係，幫助平台負責人識別瓶頸，並幫助發布經理預測 API 變更的影響。

## 3. 重要結論

API Hub 作為 API 生態系統的神經中樞，可以幫助企業解決 API 擴散帶來的挑戰，提高 API 的可發現性、可治理性和安全性。透過集中管理 API 資訊和元數據，API Hub 可以為企業提供有價值的洞察，並減少 API 的重複建設。講者鼓勵大家嘗試 API Hub，將 API 納入 API Hub 中，並從中獲取價值。
