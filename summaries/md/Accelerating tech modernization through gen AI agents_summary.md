# Accelerating tech modernization through gen AI agents
[會議影片連結](https://www.youtube.com/watch?v=eYtSGiuNx6I)
透過生成式AI Agents加速技術現代化

## 1. 核心觀點

本次會議主要探討如何利用生成式AI Agents加速技術現代化，並降低成本和風險。核心觀點如下：

*   **成本與風險降低：** 使用AI進行現代化改造，可降低35%至45%的成本和風險。
*   **技術與人的結合：** 技術固然重要，但人的因素（例如：如何建構、調整Agents）至關重要，若人的環節出錯，Agents將無法達到預期效果。
*   **結構化擴展：** 擁有Agents和建構它們的人才還不夠，還需要結構化的方法來擴展應用，避免停留在試點階段。
*   **變更管理：** 除了技術、人才和結構化方法，還需要變更管理，讓使用者能夠有效利用Agents及其產出。
*   **價值實現加速：** 過去需要7到10年才能完成的系統現代化，現在可以在幾個月內實現並開始產生價值。

## 2. 詳細內容

*   **技術可行性：** 生成式AI Agents技術正在快速發展，並在實際應用中展現出解決問題的能力。
*   **規模化的關鍵：** 規模化的重點不在於將程式碼或業務流程簡單地放入聊天介面，而是需要微服務架構，並引入對話服務，讓Agents能夠提問並與領域專家協作。
*   **Agents如同孩子：** Agents需要引導和迭代，領域專家的參與對於確保Agents朝正確方向發展至關重要。
*   **雲端類比：** Agents如同雲端運算一樣，可以提供彈性和低成本的勞動力管理，快速部署專家團隊，並根據使用時間付費。
*   **業務流程驅動：** 業務流程應該是主要驅動力，技術是輔助工具，不應為了適應技術而修改業務流程。
*   **自然語言介面：** 使用自然語言描述任務，降低技術門檻，讓更多使用者（例如：業務分析師）能夠參與。
*   **專家定義：** 像招聘一樣定義Agents的技能和能力，確保它們能夠有效地協作。
*   **演示案例：**
    *   **問題：** 支付處理系統運行在大型主機上，客戶經常需要致電客服中心查詢餘額，導致客戶滿意度低。
    *   **解決方案：**
        1.  **逆向工程：** 使用Agents分析客服中心培訓文件和通話記錄，了解業務流程和客戶痛點。
        2.  **流程重塑：** 與業務負責人合作，重新設計業務流程，讓客戶能夠自行查詢餘額。
        3.  **架構設計：** 設計雲原生架構，與大型主機整合，建立獨立於大型主機的通知服務。
        4.  **程式碼建構：** 使用Agents自動產生YAML檔案（用於Google Workflow）和JavaScript程式碼，快速部署新功能。
*   **技術人員的角色轉變：** 未來技術人員將更多地負責建構、訓練和監督Agents，而不是直接執行任務。

## 3. 重要結論

生成式AI Agents正在改變技術現代化的方式，透過降低成本、加速價值實現和提高組織敏捷性，為企業帶來巨大的競爭優勢。然而，成功實施需要技術、人才、結構化方法和變更管理的結合，並以業務成果為導向。企業應避免將AI視為萬靈丹，並重視人的因素和組織變革。
