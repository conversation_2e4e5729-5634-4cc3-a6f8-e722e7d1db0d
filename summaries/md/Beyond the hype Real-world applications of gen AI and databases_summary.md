# Beyond the hype Real-world applications of gen AI and databases
[會議影片連結](https://www.youtube.com/watch?v=QguI0ypBiP4)
超越炒作：Gen AI 與資料庫的真實世界應用

## 1. 核心觀點

本次會議主要探討了生成式 AI (Gen AI) 與資料庫結合的實際應用，並著重於企業如何利用這兩者的力量來解決真實世界的問題。核心觀點包括：

*   **彌合差距：** 企業級 Gen AI 應用存在基礎模型能力與實際需求之間的差距，而企業的營運資料庫是彌合這一差距的關鍵。
*   **AI-Ready 資料庫：** 新一代資料庫需要具備 AI-Ready 的特性，簡化應用程式堆疊，並將 AI 功能直接整合到資料庫中。
*   **結構化與非結構化資料的結合：** 現代應用程式需要能夠同時處理結構化與非結構化資料，並利用 AI 技術進行分析和處理。
*   **Agentic AI 的應用：** Agentic AI 是一種利用 LLM (大型語言模型) 來決定下一步行動的架構，資料庫是 Agentic AI 必須互動的重要系統。
*   **安全與情境感知：** 在 Gen AI 應用中，安全性和情境感知至關重要，需要採取措施來保護資料並確保 AI 的行為符合預期。

## 2. 詳細內容

*   **Google Cloud 的資料與 AI 平台：** Google Cloud 提供了一個高度差異化的資料與 AI 平台，涵蓋了從基礎設施到世界級模型的各個層面，並支援開放的生態系統。
*   **AI-Ready 資料庫的特性：** AI-Ready 資料庫應具備以下特性：
    *   **AI 融入資料：** SQL 和 API 應具備強大的 AI 功能，減少應用程式層的整合工作。
    *   **向量搜尋：** 原生支援向量搜尋，實現語義搜尋和相似性比對。
    *   **AI 查詢引擎：** 利用 AI 模型進行 SQL 查詢，解鎖企業資料的深層語義洞察。
    *   **自然語言介面：** 支援自然語言到 SQL 的轉換，簡化資料查詢。
*   **AlloyDB 的 AI 功能：** AlloyDB 是 Google Cloud 資料庫中 AI-Readyness 的先鋒，提供以下 AI 功能：
    *   **向量搜尋：** 提高線上搜尋體驗的準確性。
    *   **AI 查詢引擎：** 透過 AI.if 和 AI.rank 等運算符，利用 LLM 的知識和推理能力。
    *   **多模態嵌入：** 支援文字、圖像和影片等多種資料類型的嵌入。
*   **EchoStar 的 AI 應用案例：** EchoStar 利用 AI 技術改善了多個業務領域，包括：
    *   **現場服務：** 透過影片分析進行現場勘查和安裝稽核。
    *   **客戶服務：** 提供自助式聊天機器人和自動化通話摘要。
    *   **品質保證：** 分析通話、調查和案例，了解客戶情緒。
    *   **安全營運：** 進行威脅偵測和補救。
*   **Agentic AI 的應用場景：** Agentic AI 可用於各種場景，例如：
    *   **SRE 團隊的事件回應：** 自動偵測警報、檢查問題並找出根本原因。
    *   **客戶服務：** 根據客戶的意圖，即時協助客服人員。
*   **MCP 工具箱：** MCP 工具箱簡化了安全資料庫工具的開發，並允許將關鍵資訊（例如使用者 ID）從 AI 的控制中分離出來。

## 3. 重要結論

Gen AI 與資料庫的結合為企業帶來了巨大的潛力，可以改善多個業務領域的效率和準確性。AI-Ready 資料庫是實現這一目標的關鍵，它簡化了應用程式堆疊，並將 AI 功能直接整合到資料庫中。然而，在部署 Gen AI 應用時，安全性和情境感知至關重要，需要採取適當的措施來保護資料並確保 AI 的行為符合預期。Google Cloud 提供了全面的資料與 AI 平台，可以幫助企業充分利用 Gen AI 與資料庫的力量。
