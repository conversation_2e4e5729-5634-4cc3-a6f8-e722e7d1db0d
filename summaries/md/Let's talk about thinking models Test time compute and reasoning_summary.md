Let's talk about thinking models Test time compute and reasoning

[會議影片連結](https://www.youtube.com/watch?v=BblWtyu6fl4)
讓我們談談思考模型、測試時間計算和推理

**1. 核心觀點**

本次演講主要探討了 Gemini 中「思考」能力的重要性，以及它如何解決模型在測試時的計算瓶頸。核心觀點包括：

*   傳統模型受限於單次前向傳播的計算量，難以應對複雜問題。
*   增大模型規模是解決方案之一，但成本效益有限，且缺乏彈性。
*   「思考」機制允許模型在回應前進行多次前向傳播，大幅增加測試時的計算量。
*   透過強化學習，模型能夠自主學習並運用各種思考策略，無需人工干預。
*   思考能力提升了模型的效能和可控性，為開發者提供了更精細的成本與品質控制。
*   Gemini 2.5 Pro 是首款內建思考能力的模型，在多項基準測試中表現出色。

**2. 詳細內容**

*   **計算瓶頸：** 傳統模型在接收到請求後，只能進行一次前向傳播就必須產生回應，這限制了模型的思考深度和解決複雜問題的能力。
*   **增大模型規模的局限性：** 雖然增大模型規模可以增加計算量，但成本也會隨之上升，且不同規模的模型選擇仍然有限，無法滿足所有需求。
*   **「思考」的定義與運作方式：** 「思考」是指模型在回應前，可以進行多次前向傳播，每次傳播都會產生一個 token。模型可以根據問題的複雜程度，決定思考的次數，最多可達數萬次。
*   **「思考」的訓練方式：** 模型透過強化學習進行訓練，目標是解決各種任務。在訓練過程中，模型可以自由地運用思考能力，並根據結果獲得獎勵或懲罰。
*   **「思考」的策略：** 模型可以學習到各種思考策略，例如將問題分解為子問題、探索多種解決方案、執行中間計算、以及使用工具等。
*   **「思考」的優勢：**
    *   **更強大的能力：** 思考能力顯著提升了模型在編碼、數學、科學等領域的效能。
    *   **更高的可控性：** 開發者可以透過調整思考預算，來控制模型的成本與品質。
*   **Gemini 2.5 Pro 的亮點：**
    *   在多項學術基準測試中表現出色。
    *   在真實世界的編碼任務中表現出色。
    *   在工具使用和複雜的問答任務中表現出色。
    *   在數學能力方面取得了新的突破。
*   **未來展望：**
    *   提高思考的效率，減少不必要的計算。
    *   提供更精細的思考預算控制。
    *   將思考能力原生整合到所有模型中。
    *   持續探索思考能力在推理和開放式任務中的應用。

**3. 重要結論**

Gemini 的「思考」能力代表了一種新的模型發展方向，它透過增加測試時的計算量，顯著提升了模型的效能和可控性。Gemini 2.5 Pro 的成功證明了這種方法的有效性，並為未來的模型發展奠定了基礎。隨著技術的不斷進步，我們有理由相信，具備更強大思考能力的模型將會在各個領域發揮更大的作用。
