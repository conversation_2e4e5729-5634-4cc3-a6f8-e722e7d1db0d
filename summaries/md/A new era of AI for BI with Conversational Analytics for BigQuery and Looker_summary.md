# A new era of AI for BI with Conversational Analytics for BigQuery and Looker
[會議影片連結](https://www.youtube.com/watch?v=sXzPQRdXi7c)
AI 賦能商業智慧新紀元：BigQuery 與 Looker 的對話式分析

## 1. 核心觀點

本次會議主要探討如何利用 BigQuery 和 Looker 解決企業在商業智慧（BI）和人工智慧（AI）工具中面臨的數據孤島問題，並展示了生成式 AI 如何融入 Looker 平台的核心，特別是對話式分析如何讓使用者透過自然語言與數據互動。核心觀點包括：

*   BigQuery 和 Looker 共同解決數據孤島問題，提升數據價值。
*   Looker 的語意模型確保數據的準確性和一致性，尤其在生成式 AI 環境下更為重要。
*   Looker 的資料庫內架構充分利用 BigQuery 的強大功能和可擴展性。
*   Looker 提供多種工具和指標來管理效能和成本。
*   Looker 的語意模型可在各種工作負載中重複使用，並與其他分析工具整合。
*   AI 驅動的對話式分析透過自然語言介面，讓使用者更輕鬆地獲取數據洞察。
*   可重複使用的代理程式上下文（Reusable Agent Context）確保 Gemini 在回答問題時能考慮到特定的業務術語和規則。

## 2. 詳細內容

**BigQuery 和 Looker 的協同效應：**

*   BigQuery 致力於消除數據孤島，而 Looker 則減少 BI 和分析領域的孤島。
*   Looker 是一個完整的商業智慧平台，提供現代 BI 體驗、自助服務工具、自訂應用程式開發和隨處可用的洞察。
*   Looker 的核心是語意模型，確保數據的信任基礎，這在生成式 AI 環境下尤為重要。
*   語意模型定義了重要的業務指標，並將其與其他業務實體關聯，確保洞察的一致性和準確性。

**Looker 的資料庫內架構：**

*   Looker 的架構直接利用資料倉儲的能力，無需建立數據副本，從而減少延遲、錯誤和成本。
*   Looker 支援 50 多種雲端和本地資料庫，並針對 BigQuery 進行了最佳化，包括支援巢狀欄位、分割區、時區和樞紐分析等功能。
*   Looker 可以直接從儀表板存取 BigQuery 中的向量索引，實現語義搜尋等進階功能。

**效能和成本管理：**

*   Looker 使用語意模型來確定是否可以使用預先建立的摘要表（PDT）來回答問題，從而加快儀表板載入速度並降低查詢成本。
*   Looker 提供彙總感知功能，可以根據使用者探索數據的詳細程度自動選擇使用預先建立的摘要或原始數據。
*   Looker 提供強大的查詢日誌記錄和歷史資訊，幫助使用者了解系統效能並做出明智的決策。
*   Looker 持續投資於效能改進，並將 PDT 和彙總感知摘要表引入 BigQuery，以便在其他地方重複使用。

**語意模型的廣泛應用：**

*   Looker 提供豐富的 API，可以從應用程式查詢語意層。
*   Looker 與 Power BI、Tableau 和 Sheets 等常見分析工具預先整合，讓使用者可以使用受信任的數據。
*   Looker 使 Connected Sheets 更加簡化，允許使用者跨多個 BigQuery 表格進行探索，並使用在 Looker 語意模型中定義的複雜指標，而無需編寫 SQL。
*   Looker Reports 是一個新的功能，提供視覺上引人入勝的報告，具有自由形式的報告畫布和進階格式設定選項。

**Adio 的案例研究：**

*   Adio 是一家全球家居裝修零售商，在 20 多個國家/地區營運，使用 Looker 和 BigQuery 轉型其業務。
*   Adio 將數據工程、分析和 AI 解決方案整合在一起，以實現更成熟的技術應用。
*   Adio 正在從數據驅動轉向 AI 驅動，並建立強大的數據基礎，包括數據元件策略和穩健的語意層。
*   Adio 透過合理化儀表板、建立通用數據模型和採用 AI 數據視覺化來實現轉型。

**對話式分析：**

*   Looker 的對話式分析功能讓使用者透過自然語言與數據互動，無需熟悉 SQL 或 BI 工具。
*   對話式分析由新的代理程式架構提供支援，該架構使用推理引擎和一系列 AI 服務來回答問題。
*   推理引擎可以存取資料來源的上下文、產生查詢、建立視覺化效果、尋找異常和使用 Python 進行進階分析。
*   Looker 的語意層簡化了查詢生成過程，確保數據的準確性和一致性。
*   可重複使用的代理程式上下文允許資料擁有者定義業務術語和規則，以便 Gemini 在回答問題時考慮到這些因素。
*   程式碼直譯器允許使用者使用 Python 進行進階分析，這是傳統 BI 工具無法實現的。

## 3. 重要結論

BigQuery 和 Looker 的結合為企業提供了一個強大的平台，可以解決數據孤島問題，並利用 AI 驅動的對話式分析來獲取更深入的數據洞察。Looker 的語意模型在確保數據的準確性和一致性方面發揮著關鍵作用，尤其是在生成式 AI 環境下。透過採用這些技術，企業可以轉型其業務，並在競爭激烈的市場中獲得優勢。
