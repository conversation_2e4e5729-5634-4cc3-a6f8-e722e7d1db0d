# Ditch the frameworks and embrace core tech Prototyping in the AI era

[會議影片連結](https://www.youtube.com/watch?v=s8alcaLBIKc)
拋棄框架，擁抱核心技術：人工智慧時代的原型設計

## 1. 核心觀點

本次演講主要探討在生成式人工智慧時代，如何透過擁抱核心技術來進行原型設計，並強調快速迭代和持續改進的重要性。核心觀點包括：

*   原型設計的本質是提出問題並尋找答案，而非單純的交付物。
*   生成式人工智慧可以幫助我們更好地提出問題，並快速驗證想法。
*   在原型設計初期，應避免過度依賴框架，而應專注於核心技術的應用。
*   在應用程式規模擴大後，再考慮使用框架來提高安全性、可靠性和可擴展性。

## 2. 詳細內容

演講者首先引用了一句名言：「沒有什麼東西是一開始就被發明和完善的」，強調迭代的重要性。他以一個名為 AIPraiser.com 的虛擬創業公司為例，說明如何利用生成式人工智慧來快速建立原型。

AIPraiser.com 是一個利用多模態人工智慧（Gemini）來評估物品價值的應用程式。使用者可以拍攝照片，然後由 AI 估算物品的價值。

演講者將應用程式的開發分為三個階段：

1.  **原型設計（Prototype）：** 建立一個低保真原型，用於驗證想法並讓團隊成員達成共識。這個階段的重點是快速和可拋棄性。
2.  **最小可行產品（MVP）：** 建立應用程式的早期版本，用於測試市場反應。
3.  **規模化（Scaling）：** 將應用程式推向全球使用者，並提高其可靠性和安全性。

演講者展示了如何使用 Gemini Canvas 快速建立一個原型。透過簡單的提示，就可以生成一個可用的應用程式。這使得產品經理和軟體工程師可以獨立完成原型設計，而無需依賴整個團隊。

接下來，演講者展示了如何使用 Gemini Code Assist 來支援 MVP 的開發。他以添加多幣別支援為例，說明如何使用 Gemini Code Assist 來修改現有的 HTML、JavaScript 和 Python 程式碼。Gemini Code Assist 還會提供來源引用，以便使用者驗證程式碼的正確性。

在應用程式規模化階段，演講者指出存在一個分岔路口。開發者可以選擇繼續使用早期版本，也可以選擇從頭開始進行策略性重建。如果需要考慮安全性、可靠性和可擴展性，則後者是一個更好的選擇。在這個階段，可以開始使用框架來提高應用程式的品質。

演講者最後強調，原型設計的本質是提出問題並尋找答案。生成式人工智慧可以幫助我們更好地提出問題，而真正的力量來自於我們如何利用這些問題來為產品和使用者做正確的事情。

## 3. 重要結論

在生成式人工智慧時代，原型設計變得更加快速和容易。透過擁抱核心技術，我們可以快速驗證想法，並建立出色的產品。在原型設計初期，應避免過度依賴框架，而應專注於核心技術的應用。在應用程式規模擴大後，再考慮使用框架來提高安全性、可靠性和可擴展性。原型設計的本質是提出問題並尋找答案，而生成式人工智慧可以幫助我們更好地提出問題。
