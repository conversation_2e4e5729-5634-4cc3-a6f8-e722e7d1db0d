# Optimize software development with Gemini Code Assist
[會議影片連結](https://www.youtube.com/watch?v=QAiNz0lObkg)
使用 Gemini Code Assist 優化軟體開發

## 1. 核心觀點

本次會議主要探討如何在企業中導入 Gemini Code Assist，並強調這不僅僅是導入一個工具，更重要的是管理企業內部的變革。會議涵蓋了 Gemini Code Assist 在 IDE 內的使用，以及在整個軟體開發生命週期 (SDLC) 中的應用。此外，會議還討論了開發者對 AI 生成程式碼的信任問題，以及如何透過結構化的導入方法來建立信任感，並實現預期的價值。

## 2. 詳細內容

會議首先指出，AI 在企業中的應用正在快速增長，並且來自高層和基層都有推動力量。然而，DORA 報告的研究顯示，儘管開發團隊的生產力有所提高，程式碼品質也在改善，但交付吞吐量和交付穩定性卻出現了負面影響。這可能是因為使用 GenAI 工具導致 Pull Request 的規模變大，進而影響了吞吐量和穩定性。

此外，許多開發者對 GenAI 工具生成的程式碼缺乏信任，這可能是因為導入方式不夠結構化，期望過高，以及缺乏有效的影響力衡量標準。

會議建議，導入 GenAI 進行程式碼輔助應該被視為軟體工程流程中的重大變革管理，需要採用結構化的導入方法，以建立開發者的信任，並實現業務預期的價值。

Alok 接著介紹了 Gemini Code Assist 導入的旅程，包括 Day Zero、Day One 和 Day Two 三個階段，分別對應雲端管理員、開發者和工程領導等不同角色。

*   **Day Zero (雲端管理員):** 負責定義使用案例、協調各方利益相關者，並部署 Gemini Code Assist。
*   **Day One (開發者):** 負責提供訓練 (例如 Prompt 工程、Gemini Code Assist) 和舉辦黑客松，以賦能開發者。
*   **Day Two (工程領導):** 負責衡量影響 (例如生產力)，並決定是否擴大使用範圍和啟用新的使用案例。

Alok 還強調了在導入過程中與工程、安全和合規團隊協調的重要性，並介紹了如何選擇合適的 Code Assist 版本、分配授權、配置 GCP 專案，以及利用 Context 來提高程式碼生成的品質。

為了提高開發者對 AI 生成程式碼的信任，需要提供 Prompt 工程的訓練，並舉辦黑客松等活動，讓開發者看到實際價值。此外，還需要建立 Gemini Code Assist 冠軍，以開發者能理解的語言來溝通。

在 Day Two 階段，工程領導需要衡量採用指標和信任指標，例如活躍用戶數、聊天回應生成數、接受率和接受的程式碼行數。此外，還可以透過調查來評估開發者對不同使用案例 (例如單元測試、文件、程式碼生成和程式碼重構) 的滿意度。

Laks 和 Deepthi 則介紹了 TCS 如何將 Gemini Code Assist 整合到整個 SDLC 中，並為不同的角色 (例如產品負責人、開發者、測試人員) 建立代理 (Agent)，以提升知識對等性，並解決資訊孤島的問題。他們強調，應用程式的知識分散在多個知識庫中，因此需要一個知識結構 (Knowledge Fabric) 來編目和索引這些知識來源，並建立知識之間的關係圖。

Deepthi 詳細介紹了知識結構的架構，以及如何透過先進的 RAG (Retrieval-Augmented Generation) 機制來連接不同的知識來源，並為不同的代理提供上下文資訊。她還展示了一個 Demo，說明如何使用產品負責人代理來建立詳細的使用者故事，並將其傳遞給開發者代理和測試管理員代理，以生成程式碼和測試案例。

## 3. 重要結論

本次會議強調，成功導入 Gemini Code Assist 需要結構化的方法、建立開發者的信任，以及衡量實際價值。此外，將 Gemini Code Assist 整合到整個 SDLC 中，並為不同的角色建立代理，可以進一步提升生產力、程式碼品質和交付速度。
