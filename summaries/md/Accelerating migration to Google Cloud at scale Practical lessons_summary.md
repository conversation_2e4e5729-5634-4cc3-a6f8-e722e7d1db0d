# Accelerating migration to Google Cloud at scale Practical lessons
[會議影片連結](https://www.youtube.com/watch?v=kHjg1fZOF9U)
加速大規模遷移至 Google Cloud 的實用經驗

## 1. 核心觀點

本次演講主要分享了德意志銀行在將數百個應用程式從內部部署遷移到 Google Cloud 的過程中，所遇到的挑戰以及 E-PAM Systems 如何協助他們克服這些挑戰。核心觀點包括：

*   應用程式團隊在設計階段耗費過多時間。
*   Google Cloud 生態系統服務眾多，應用程式團隊難以成為所有服務的專家，也難以完全了解銀行的策略。
*   不同團隊針對相同問題提出不同的解決方案，且可能使用未經銀行批准的服務。
*   設計問題通常在正式審查時才被發現。
*   透過決策樹、解決方案共享和培訓計畫，可以顯著縮短設計時間，提高解決方案品質和一致性，並降低總體擁有成本。

## 2. 詳細內容

E-PAM Systems 在德意志銀行的雲端卓越中心 (CCoE) 中扮演重要角色，負責架構治理、雲端策略定義、可重複使用的雲端組件創建和雲端諮詢。目標是成為值得信賴的顧問，幫助銀行提高遷移速度和品質。

為了應對設計階段耗時過長的問題，E-PAM 實施了一系列措施，其中大部分並非技術性的。

*   **決策樹 (Decision Trees, DTs):** 針對常見任務，提供一系列邏輯佔位符。應用程式團隊可以從銀行批准的技術列表中，根據決策樹選擇最適合其特定場景的技術。決策樹考慮了成本、可用性、RTO、RPO、可擴展性和可移植性等因素，以及銀行是否具備使用該技術的技能，學習曲線的陡峭程度，以及市場上是否提供必要的技能。這大大加快了解決方案設計過程，並為應用程式團隊在設計文件中提供了預先準備好的理由。

*   **解決方案共享:** 建立一個輕量級機制，以眾包方式分享已驗證的常見問題解決方案。當發現可重複使用的解決方案時，會記錄問題、解決方案概述、使用的關鍵技術、實施該解決方案的應用程式、設計連結、程式碼連結，以及了解該解決方案的負責人。這使得其他應用程式團隊可以從有經驗的人那裡獲得高品質的幫助。

*   **雲端架構設計熟悉化 (Cloud Architecture Design Familiarisation, CADFAM) 培訓計畫:** 確保應用程式團隊實際使用這些資源。CADFAM 告訴團隊在雲端旅程中所處的位置、需要的資源、使用資源的順序、最佳實踐（如圖表繪製和 SRE 採用）、如何預約架構審查會議以及如何為此類會議做準備，以及在哪裡獲得幫助。E-PAM 舉辦了培訓課程，培訓了 500 多人，並建立了一個由 1,000 多名架構師、工程師、交付負責人、應用程式負責人和任何其他可能感興趣的人組成的雲端架構設計社群。這個社群用於眾包設計挑戰的解決方案，並及早發現和解決問題。

## 3. 重要結論

透過這些措施，E-PAM 成功地將每個應用程式的平均設計時間減少了 70%。這顯著降低了整體遷移工作量和成本，並提高了解決方案的品質和一致性，降低了總體擁有成本。對於正在進行大規模遷移的組織來說，這可能節省數百萬美元。建議尚未在 CCoE 中安排企業雲端架構師來交付這些措施的組織，應優先考慮此活動。對於大型組織來說，投資回報率將超過 100 倍。
