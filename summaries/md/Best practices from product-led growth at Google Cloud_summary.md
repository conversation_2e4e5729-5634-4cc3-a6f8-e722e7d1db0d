# Best practices from product-led growth at Google Cloud
[會議影片連結](https://www.youtube.com/watch?v=Y8rT1_G6aa8)
Google Cloud 產品主導增長的最佳實踐

## 1. 核心觀點

本次演講主要探討 Google Cloud 如何透過產品主導增長 (Product-Led Growth, PLG) 策略，讓使用者更容易從產品中發現價值，進而帶動業務增長。核心觀點包括：

*   透過質化與量化數據結合，找出產品對客戶的關鍵價值點和阻礙使用者體驗的摩擦點。
*   建立平台組織，賦能各產品團隊自行實施 PLG，並提供最佳實踐、工具、建議和基準。
*   將使用者劃分為不同原型和角色，以便更快地進行投資和實驗，為使用者提供關鍵業務價值。
*   透過摩擦日誌 (Friction Logging) 模擬使用者體驗，找出產品體驗中的優缺點。
*   透過數據科學方法，找出能帶來高價值行動 (High Value Actions, HVAs)，並優化使用者旅程。
*   PLG 只是業務增長的眾多槓桿之一，可與其他行銷方式互補。

## 2. 詳細內容

Google Cloud 從早期就開始實施 PLG，最初的重點是讓使用者體驗盡可能多的產品，以展現 Google Cloud 的強大功能。現在，Google Cloud 轉向平台組織，讓各產品團隊能夠透過最佳實踐、工具和建議自行實施 PLG。

對於擁有眾多產品線的企業來說，平台方法更為合適，因為它將 PLG 的權力賦予最了解其產品體驗和使用者的團隊。

在思考使用者時，Google Cloud 喜歡將他們分為不同的原型和角色。這有助於找到不同產品使用者之間的共同點，並更快地進行投資和實驗，以便儘早為使用者提供關鍵業務價值。這些原型和角色因公司類型而異，因此務必仔細考慮。

從質化的角度來看，下一步是設身處地為使用者著想。Google Cloud 透過摩擦日誌來實現這一點，也就是模擬使用者體驗，並記錄產品體驗中的優缺點。建議兩人一組進行，一人扮演使用者，另一人記錄。透過多次模擬，可以發現模式，並利用 Google Sheets 或 Gemini 來分析這些模式。

在量化方面，重要的是要仔細思考 KPI 樹，了解輸入指標和導致輸出指標的專案和措施。Google Cloud 使用數據科學方法，透過因果推斷來找出能帶來高價值行動 (HVAs)。重要的是要理解 HVAs 是被發現的，而不是被規定的。因此，即使有假設，因果分析的結果也可能出人意料。如果產品已正確地進行了檢測，請相信數據並針對這些關鍵時刻優化使用者旅程。

## 3. 重要結論

產品主導增長是一種重要的策略，可以讓使用者更容易從產品中發現價值，進而帶動業務增長。透過質化和量化數據的結合，企業可以找出產品的關鍵價值點和摩擦點，並優化使用者旅程。PLG 只是業務增長的眾多槓桿之一，可以與其他行銷方式互補。關鍵在於確保使用者儘早實現產品的業務價值。
