The future of AI and databases How Target transformed their retail experience with AlloyDB AI

[會議影片連結](https://www.youtube.com/watch?v=R4xyyK0JkFo)
AI 與資料庫的未來：Target 如何透過 AlloyDB AI 轉型零售體驗

## 1. 核心觀點

本次會議主要探討了 AI 與資料庫的未來發展趨勢，以及 Target 如何利用 Google Cloud 的 AlloyDB AI 轉型其零售體驗。核心觀點包括：

*   **AI 在資料庫中的關鍵作用：** 強調了操作型資料庫在 AI 應用中的重要性，它們包含關鍵的企業數據，是構建準確、相關且基於企業真實情況的 Gen.AI 應用程式的基礎。
*   **AlloyDB AI 的突破性創新：** AlloyDB AI 作為首個將 AI 整合到操作型資料庫中的產品，為資料庫領域帶來了根本性的變革，它不僅提升了效能，還提供了更豐富的 AI 功能。
*   **Target 的成功案例：** Target 透過在生產環境中大規模部署 AlloyDB AI，顯著提升了客戶參與度，改善了搜尋體驗，並實現了業務指標的提升。
*   **Google Cloud 在 AI 領域的領先地位：** Google Cloud 擁有全面的資料庫產品組合，並將最先進的 AI 技術直接融入到資料庫中，幫助企業構建智慧型 AI 應用程式。
*   **AI 驅動的 SQL 的未來：** AI 查詢引擎的引入，將生成式 AI 的力量帶入 SQL，使得開發人員能夠利用 Gemini 模型執行更複雜的查詢，並實現更豐富的資料互動。

## 2. 詳細內容

*   **Amit Ganesh 的開場介紹：** Amit Ganesh 強調了操作型資料庫在 AI 中的關鍵作用，並介紹了 Google 如何將 AI 融入其資料庫產品中，以提升客戶體驗。他還提到了 AlloyDB AI 的突破性創新，以及 Target 如何利用它來轉型零售體驗。
*   **Google 在 AI 領域的領先地位：** Google 在 AI 領域擁有十多年的研究經驗，並在大型語言模型和向量搜尋方面取得了重大進展。這些進展為應用程式開闢了全新的可能性，實現了與各種數據的無縫整合，並創造了更豐富、更直觀、更具情境相關性的客戶體驗。
*   **AlloyDB AI 的核心功能：** AlloyDB AI 提供了全面的 AI 功能，包括與 Vertex AI 的整合、下一代自然語言支援、最先進的向量搜尋、代理空間和代理框架整合等。這些功能使企業能夠利用其可信任的企業數據構建智慧型 AI 應用程式。
*   **AI 查詢引擎的引入：** AlloyDB AI 是業界首個具有 AI 查詢引擎的資料庫，它將生成式 AI 的力量帶入 SQL，使得開發人員能夠利用 Gemini 模型執行更複雜的查詢。透過 AI 命名空間，開發人員可以使用自然語言來表達查詢條件，例如「顯示所有護膚產業中知名品牌的品牌名稱」，從而簡化了查詢的編寫過程。
*   **新的 AI 模型：** AlloyDB AI 引入了三種新的 AI 模型，包括語義排名模型、Gemini 嵌入模型和多模態嵌入模型。這些模型提供了更強大的靈活性和能力，使得開發人員能夠構建更豐富、更具情境相關性的應用程式。
*   **下一代自然語言支援：** AlloyDB AI 引入了基於自然語言的第二個資料庫介面，使得使用者可以使用自然語言與資料庫進行互動。透過與 Google 研究團隊的合作，AlloyDB AI 在提供高度準確、靈活、安全且保護隱私的自然語言查詢結果方面達到了業界領先水平。
*   **與 Agent Space 和 Agent Framework 的整合：** AlloyDB AI 與 Vertex AI 的 Agent Space 助理和 Agent 開發套件整合，使得開發人員可以使用自然語言與結構化資料庫資料進行互動。這種整合使得 Agent Space 使用者可以直接存取資料庫中的即時數據，從而獲得準確且最新的資訊。
*   **向量搜尋的改進：** AlloyDB AI 在向量搜尋方面取得了重大進展，包括推出掃描向量索引和支援 SQL 篩選器的向量搜尋。這些改進使得 AlloyDB AI 在混合 SQL 和向量搜尋查詢方面達到了業界領先的品質和效能。
*   **Sudha Adarsh 分享 Target 的案例：** Sudha Adarsh 分享了 Target 如何利用 AlloyDB AI 轉型其零售搜尋體驗，並提升客戶參與度。透過實施混合搜尋，Target 能夠提供更相關、更快速、更無縫的體驗，並提升業務指標。
*   **Target 的混合搜尋：** Target 的混合搜尋結合了傳統的關鍵字搜尋和基於語義的搜尋，從而提供更具情境相關性的結果。透過 AlloyDB AI 的掃描索引和篩選功能，Target 能夠顯著提升搜尋效能，並改善客戶體驗。
*   **Target 的成果：** 透過實施 AlloyDB AI，Target 在自然語言搜尋方面提升了 20%，並在產品探索和產品種類方面提升了 20%。這些改進使得更多購物者能夠找到他們想要的東西，購買更多商品，並獲得更相關的結果。
*   **Barry Morris 的總結：** Barry Morris 強調了 Google Cloud 對 AI 的投資，以及 AI 在資料庫領域的變革性作用。他還介紹了 Google Cloud 的資料雲，以及 Google Cloud 如何透過各種產品和服務來滿足客戶的需求。

## 3. 重要結論

本次會議清晰地展示了 AI 如何重塑資料庫的未來，以及 Google Cloud 如何透過 AlloyDB AI 等創新產品引領這一變革。Target 的成功案例證明了 AI 驅動的資料庫能夠顯著提升客戶體驗，並推動業務增長。隨著 AI 技術的不斷發展，我們可以預期資料庫將在應用程式開發中扮演更重要的角色，並為企業帶來更大的價值。
