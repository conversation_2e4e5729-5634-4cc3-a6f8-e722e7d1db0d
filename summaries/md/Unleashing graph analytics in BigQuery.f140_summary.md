# Unleashing graph analytics in BigQuery.f140
[會議影片連結]()
Unleashing graph analytics in BigQuery.f140

## 1. 核心觀點

本次會議主要介紹了 BigQuery 中圖分析的應用，重點闡述了圖資料庫的優勢、BigQuery 支援圖分析的解決方案，以及 BioCortex 如何利用圖技術進行藥物研發的案例。核心觀點包括：

*   **圖資料庫的優勢：** 圖資料庫更易於理解、視覺化和解釋，擅長發現隱藏的關係，並能進行假設分析。
*   **BigQuery 圖分析的解決方案：** BigQuery 允許在現有資料上建立圖，無需額外的 ETL 過程，並提供 GQL 查詢語言和視覺化工具。
*   **解決傳統圖分析的挑戰：** BigQuery 圖分析旨在解決資料孤島、維護成本、效能和擴展性問題，以及缺乏圖專業知識的挑戰。
*   **BioCortex 的案例：** BioCortex 利用 BigQuery 圖分析加速藥物研發，特別是在癌症治療領域，透過模擬腫瘤環境中的代謝反應，尋找潛在的藥物靶點。

## 2. 詳細內容

*   **圖資料庫的應用場景：**
    *   零售業：建立客戶 360 度視圖，提供個人化產品推薦。
    *   金融業：偵測金融詐欺，追蹤資金流向。
    *   社交媒體：建立社交圖譜，進行精準受眾定位。
    *   醫療保健：研究化學物質之間的相互作用，加速藥物研發。

*   **BigQuery 圖分析的優勢：**
    *   無需 ETL：直接在 BigQuery 資料上建立圖，避免資料孤島和資料漂移。
    *   可擴展性：利用 BigQuery 的大規模基礎設施，支援數十億節點和邊緣的圖。
    *   互操作性：支援 GQL 查詢語言和 SQL，可以混合使用，充分利用兩者的優勢。
    *   整合性：與 BigQuery 的其他功能（如全文檢索、向量檢索）和生態系統整合。
    *   視覺化：提供圖形化的方式呈現分析結果，更易於理解。
    *   與 Spanner Graph 互通：BigQuery Graph 負責批次和分析型工作負載，Spanner Graph 負責線上和操作型工作負載，形成統一的圖解決方案。

*   **BigQuery 圖分析的使用步驟：**
    1.  建立圖結構描述：在現有關聯式資料上定義節點和邊緣。
    2.  使用 GQL 查詢：遍歷圖中的關係，尋找隱藏的關聯。
    3.  視覺化結果：在 BigQuery Studio Notebook 中呈現圖形化的結果。

*   **BioCortex 的案例：**
    *   利用 BigQuery 圖分析建立了一個包含 15,000 種化學物質和 20,000 種反應的大型知識圖譜。
    *   透過模擬腫瘤環境中的代謝反應，尋找潛在的藥物靶點。
    *   展示了如何使用 BigQuery Graph 快速查詢和視覺化複雜的代謝路徑。
    *   開發了 Carbon Knowledge Agents，利用自然語言處理技術，讓科學家可以用自然語言查詢知識圖譜。

*   **示範：**
    *   利用 BigQuery Graph 進行金融詐欺偵測，展示了如何透過圖查詢找到與可疑帳戶相關聯的其他帳戶。
    *   展示了如何結合向量檢索和圖查詢，找到與可疑帳戶相似的其他可疑帳戶。
    *   展示了如何將 BigQuery 圖資料與其他 BigQuery 表格進行聯結，進行更深入的分析。

## 3. 重要結論

BigQuery 圖分析提供了一種強大的工具，可以幫助企業從資料中發現隱藏的關係，並解決傳統圖資料庫的挑戰。BioCortex 的案例證明了 BigQuery 圖分析在藥物研發領域的巨大潛力。Google Cloud 致力於提供統一的圖解決方案，滿足企業從操作型到分析型的所有圖需求。
