# Simplify compliance in the cloud
[會議影片連結](https://www.youtube.com/watch?v=qIBkU4uiDvY)
簡化雲端合規性

## 1. 核心觀點

本次會議主要探討 Google Cloud 如何解決雲端環境中安全與合規性的複雜問題，並展示相關解決方案如何有效管理雲端環境。核心觀點包括：

*   **風險降低：** 客戶最關心降低安全、合規和隱私風險，並希望靈活地表達、執行和監控風險降低的意圖。
*   **全面覆蓋：** 風險存在於雲端供應商提供的基礎設施、客戶的工作負載和應用程式產生的數據中。
*   **統一視角：** 雲工程師、SRE、CISO 和 GRC 人員需要對風險和補救措施有統一的視角。
*   **端到端解決方案：** Google Cloud 致力於提供涵蓋配置、監控和稽核的端到端安全、隱私和合規解決方案。
*   **框架與雲端控制：** 透過框架（Framework）和雲端控制（Cloud Control）這兩個核心概念，將合規性要求與技術實施連接起來。
*   **稽核管理員（Audit Manager）：** 稽核管理員簡化了合規性稽核流程，幫助客戶生成合規性證據，並大幅縮短稽核所需的時間。

## 2. 詳細內容

*   **Google Cloud 的方法：**
    *   提供單一方式來表達合規、安全和隱私意圖。
    *   提供配置和強制執行意圖的能力。
    *   提供監控、風險接受等功能。
    *   利用相同的意圖來生成合規性證據。

*   **框架（Framework）和雲端控制（Cloud Control）：**
    *   **框架：** 用於模擬任何行業定義的制度、框架或標準，例如 NIST 800、CSA CCM、FedRAMP High 或 IL4/IL5。客戶也可以建立自己的框架。
    *   **雲端控制：** 雲端無關的樂高積木，用於表達強制執行邏輯、監控邏輯或生成合規性證據的邏輯。可以包含組織政策、IAM 政策、複雜的 Cell 表達式或正則表達式。

*   **稽核管理員（Audit Manager）：**
    *   幫助客戶生成合規性證據，證明他們正在做正確的事情。
    *   簡化了合規性稽核流程，將原本需要三到四個月的流程縮短到幾天或幾週。
    *   允許不同團隊在不同階段執行稽核，例如在將應用程式從開發環境部署到生產環境之前。
    *   GRC 人員可以使用稽核管理員執行預先稽核，並在實際稽核開始前識別需要解決的差距。

*   **示範：**
    *   展示了如何管理標準框架（例如 CIS），如何自訂框架以滿足特定需求，如何建立自訂控制，以及如何執行稽核評估。
    *   展示了如何使用 Gemini AI 模型，透過自然語言提示來建立雲端控制。

*   **Deutsche Börse 的案例研究：**
    *   Deutsche Börse 是一家全球公司，其業務基於信任，並受到眾多監管機構的監管。
    *   與 Google Cloud 合作，在資料、分析和數位市場方面進行創新。
    *   面臨雲端轉型方面的挑戰，包括合規性和治理。
    *   CEO 要求建立 100% 自動化的控制平面。
    *   使用稽核管理員來驗證是否僅使用白名單中的服務，資源是否已正確標記，以及是否已根據資源的關鍵性儲存備份。
    *   計劃將稽核管理員推廣給開發人員，以便他們可以在部署之前執行控制。

## 3. 重要結論

Google Cloud 正在透過提供全面的安全、隱私和合規解決方案，簡化雲端合規性。框架和雲端控制的概念，以及稽核管理員的功能，有助於客戶更有效地管理其雲端環境中的風險，並證明其合規性。Deutsche Börse 的案例研究展示了稽核管理員在實際應用中的價值。
