```
# The future of compute The right VMs for every workload
[會議影片連結](https://www.youtube.com/watch?v=fCu0aAoC9X0)
運算的未來：適合各種工作負載的虛擬機器

## 1. 核心觀點

本次會議主要探討 Google Cloud 在運算領域的最新進展，以及如何針對不同的工作負載選擇最適合的虛擬機器（VM）。核心觀點包括：

*   AI 正在快速發展，需要更強大和專業的運算能力。
*   現代應用程式需要高度可擴展性，並充分利用 Google Cloud 的所有功能。
*   傳統企業工作負載仍然至關重要，需要持續關注。
*   Google Compute Engine 的目標是為每個工作負載設計和優化系統，包括運算、儲存和網路。
*   Google Cloud 提供廣泛的 VM 產品組合，以滿足各種客戶需求。

## 2. 詳細內容

*   **Titanium 基礎架構：** Google Cloud 的基礎是 Titanium，它在運算、儲存和網路效能方面取得了重大進展。
    *   **運算：** 提供來自 Intel、AMD 和 Google 自家 Axion 的最新 CPU。
    *   **儲存：** HyperDisk 提供可調整的效能，Titanium SSD 提供更低的延遲。
    *   **網路：** Titanium ML 轉接器和 Cloud RDMA 提供低延遲的網路連接。
*   **裸機實例：** 提供對硬體的直接存取，允許遷移傳統工作負載或運行自定義的 hypervisor 和作業系統。
*   **通用型 VM：**
    *   **C 系列：** 針對最高效能和企業功能進行優化，提供領先的運算能力、進階維護控制、更大的實例大小以及優質的儲存和網路功能。
    *   **N 系列：** 針對價格效能和總體擁有成本（TCO）進行優化，提供靈活性和自定義機器類型。
*   **C4D：** 基於 AMD 第五代 EPYC 處理器（Turin），提供最新的效能，與其他雲端產品相比，效能提升高達 45%。
*   **C4：** 基於 Intel 第六代 Granite Rapids CPU，提供新的實例選項，以擴展資料庫、遊戲伺服器和容器平台。
*   **N4：** 提供卓越的價格效能，比上一代產品提升高達 18%，適用於 Web 和應用程式伺服器、業務應用程式和微服務等通用工作負載。
*   **Shopify 的案例：** Shopify 利用 N4 實現了更好的 P90 延遲，同時減少了供應工作負載所需的副本數量。
*   **Axion：** Google 自家的 ARM 架構處理器，提供卓越的價格效能和能源效率。
*   **專業型運算：**
    *   **HPC（高效能運算）：** 提供針對 HPC 工作負載優化的實例，並支援主要的 ISV 和合作夥伴。
    *   **H4D：** 基於 AMD 第五代 Turin CPU，針對 HPC 工作負載提供最佳的價格效能，效能提升高達 90%。
    *   **Z3：** 針對儲存進行優化，提供高吞吐量和低延遲的本地 SSD 儲存。
    *   **M4：** 針對記憶體進行優化，提供更大的記憶體容量，適用於 SAP HANA 等記憶體內工作負載。
*   **Elastic 的案例：** Elastic 利用 C4A 處理器實現了更好的搜尋延遲和 40% 的索引吞吐量提升。

## 3. 重要結論

Google Cloud 致力於為各種工作負載提供最佳的運算解決方案。透過 Titanium 基礎架構、廣泛的 VM 產品組合以及與客戶的緊密合作，Google Cloud 正在幫助企業充分利用雲端運算的優勢。本次會議強調了選擇適合特定工作負載的 VM 的重要性，並展示了 Google Cloud 在運算領域的持續創新。
```