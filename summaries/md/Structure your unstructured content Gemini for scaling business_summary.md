# Structure your unstructured content Gemini for scaling business
[會議影片連結](https://www.youtube.com/watch?v=oyg_kFuFEOI)
運用 Gemini 結構化非結構化內容以擴展業務

## 1. 核心觀點

本次演講主要介紹了如何利用 Gemini 來結構化非結構化內容，以幫助企業擴展業務。講者分享了 Gemini 2.0 的強大功能，以及如何利用 Box AI 和 SimpleStack 等工具來簡化流程、提高效率和確保安全合規。

## 2. 詳細內容

講者 Adam Manzoni 首先介紹了 Gemini 2.0 擁有市場上最大的上下文窗口，可以容納一本《哈利波特》小說的內容。接著，他分享了 Box 客戶如何利用 Gemini Flash 在 Box AI 中以安全和私密的方式從內容中提取元數據。

Manzoni 接著分享了他在 Funwell 的經驗，他們如何利用 Box AI 從客戶上傳的財務文件中提取信息，用於簡化審核流程。過去，審核團隊需要手動審查每個文件，以確保有足夠的文件開始審核流程。現在，透過 Box AI，他們可以自動提取信息並將其輸入到工作流程中，從而讓審核團隊能夠專注於評估風險。

此外，Manzoni 還提到了他在 Funwell 遇到的另一個問題：每次新員工入職時，他的團隊都需要設置 16 個不同的 SaaS 工具，並確保聯合 ID 已映射，且身份驗證在所有系統中都能正常工作。為了解決這個問題，他開發了 SimpleStack。SimpleStack 旨在幫助企業以安全合規的方式管理其技術堆疊，簡化身份驗證、角色和權限管理，實現無縫的員工入職和離職。

## 3. 重要結論

本次演講強調了 Gemini 在結構化非結構化內容方面的潛力，以及如何利用相關工具來簡化流程、提高效率和確保安全合規。透過 Box AI 和 SimpleStack 等工具，企業可以更好地管理其數據和技術堆疊，從而實現業務擴展。
