# Build scalable AI infrastructure for startups
[會議影片連結](https://www.youtube.com/watch?v=N2kixOL7XRQ)
Build scalable AI infrastructure for startups

## 1. 核心觀點

本次會議主要探討新創公司如何建構可擴展的人工智慧基礎設施。與會的三位企業家和技術專家分享了他們在人工智慧領域的實戰經驗和深刻見解，涵蓋了從化學感測、語音辨識到開源模型優化等不同層面。核心觀點包括：

*   **評估指標的重要性：** 不要過度迷信基準測試，應關注對業務真正重要的關鍵績效指標（KPI），例如轉換率、客戶流失率和客戶滿意度。
*   **專注於核心競爭力：** 新創公司應集中資源開發對產品獨特性至關重要的功能，而非重複造輪子。
*   **與可擴展的合作夥伴合作：** 選擇具有良好擴展記錄的雲端服務供應商和其他合作夥伴，以確保基礎設施能夠隨著業務成長而擴展。
*   **快速實驗和迭代：** 在安全的前提下，鼓勵團隊嘗試新工具和技術，並快速評估其效果。
*   **重視客戶回饋：** 積極收集和分析客戶回饋，並將其納入產品開發和優化過程中。
*   **關注資料安全：** 根據資料的敏感程度，採取適當的安全措施，例如資料加密、存取控制和合規性認證。
*   **謹慎招聘：** 優先考慮具有正確價值觀和文化契合度的人才，因為他們將成為公司的未來領導者。

## 2. 詳細內容

*   **Osmo (Rich):** Osmo 是一家源自 Google Brain 的公司，旨在數位化氣味。他們開發化學感測器和模型，用於讀取和創建氣味，應用範圍涵蓋香水、驅蟲劑和消費品。面臨的挑戰是如何在眾多潛在市場中找到重點。
*   **Assembly AI (Dylan):** Assembly AI 致力於構建業界最佳的語音人工智慧模型和開發者基礎設施。他們為 Zoom 和 Fireflies 等公司提供服務，每月處理數百 TB 的語音資料。Dylan 強調，評估指標對於選擇正確的基礎設施至關重要，並分享了一個客戶透過使用 Assembly AI 將客戶升級事件減少 60% 的案例。
*   **Fireworks AI (Shannik):** Fireworks AI 的使命是普及開源模型，並提供最快的推論平台。他們優化開源模型，使其能夠在 Fireworks 上快速運行。Shannik 提到，他們最初的 CUDA 核心是為 Lama 和 Mistral 等模型優化的，但後來他們發現客戶的使用案例有所不同，因此他們重寫了所有內容。他強調，新創公司應專注於對組織至關重要的事物，並投資於具有擴展能力的工具和合作夥伴。
*   **評估指標 (Dylan):** Dylan 強調，基準測試的效用正在下降，公司應關注對業務真正重要的 KPI。他建議建立評估管道，以自動評估不同人工智慧技術對這些 KPI 的影響。
*   **開源模型 vs. 專有模型 (Shannik):** Shannik 認為，開源模型的品質正在迅速提高，並且在成本、靈活性、隱私和安全性方面具有優勢。然而，專有模型在多模態和整體性能方面仍然具有優勢。
*   **持續改進模型和降低延遲 (Dylan):** Dylan 認為，深入了解客戶價值是持續改進模型的關鍵。他建議新創公司建立關於其服務市場的機構知識，並將其納入研發過程中。
*   **化學感測與自動駕駛 (Rich):** Rich 將化學感測與自動駕駛進行了比較，指出汽車無法感知其化學環境。他解釋說，Osmo 正在將自動駕駛汽車中使用的大規模資料收集、標記和模型構建技術應用於化學感測。
*   **資料安全 (Rich, Dylan, Shannik):** 與會者討論了在採用人工智慧工具時平衡資料安全需求的重要性。Rich 強調，他希望員工嘗試新工具，但同時也需要保護公司的專有資料。Dylan 提到，客戶越來越希望在自己的環境中運行人工智慧模型，以提高資料安全性。Shannik 建議公司根據資料的敏感程度，選擇適當的安全控制措施。
*   **領導力挑戰 (Shannik, Dylan, Rich):** 與會者分享了他們在擴展高成長人工智慧新創公司時面臨的領導力挑戰。Shannik 強調，客戶回饋和招聘合適的人才是至關重要的。Dylan 認為，每個新創公司的旅程都是不同的，公司應專注於解決客戶的問題。Rich 建議公司明確其目標客戶和市場定位。

## 3. 重要結論

本次會議為新創公司提供了關於如何建構可擴展的人工智慧基礎設施的寶貴見解。與會者強調，評估指標、專注於核心競爭力、與可擴展的合作夥伴合作、快速實驗和迭代、重視客戶回饋、關注資料安全以及謹慎招聘是成功的關鍵。透過遵循這些原則，新創公司可以建立能夠支持其成長和創新的強大人工智慧基礎設施。
