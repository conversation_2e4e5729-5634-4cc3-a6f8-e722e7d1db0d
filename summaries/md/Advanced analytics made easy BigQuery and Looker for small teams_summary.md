Advanced analytics made easy BigQuery and Looker for small teams

[會議影片連結](https://www.youtube.com/watch?v=oepub9jaync)
適用於小型團隊的簡易進階分析：BigQuery 與 Looker

## 1. 核心觀點

本次會議主要介紹 Google Cloud Platform (GCP) 如何透過整合 BigQuery、Looker 和 Vertex AI，為小型團隊提供易於使用且功能強大的進階分析解決方案。重點在於解決小型團隊在數據分析方面面臨的挑戰，例如數據孤島、缺乏專業知識和預算限制。

## 2. 詳細內容

會議首先指出，在人工智慧 (AI) 時代，各公司都在努力導入 AI，但小型團隊面臨更多挑戰。GCP 統一平台透過 BigQuery、Looker 和 Vertex AI 的整合，並由 Gemini 提供支援，旨在解決這些問題。

BigQuery 透過 Data Transfer Service 簡化了數據集中化的過程，可以輕鬆地從試算表、Postgres、MySQL 數據庫、Salesforce 等第三方服務以及 Google Ads 和 YouTube 等 Google 服務導入數據。

為了數據清理，BigQuery 推出了正式發布的 Data Preparation 功能，利用 AI 協助清理數據，並將各種 SQL 函數以視覺化的方式整合到控制台中。

BigQuery 還支援機器學習 (ML) 和 AI 整合，允許使用者使用簡單的 SQL 程式碼訓練傳統機器學習模型，並使用 Gemini 和其他 Gen AI 工具對數據執行提示，甚至分析圖像、影片和音訊等多模態數據。

BigQuery 引入了 Data Insights 功能，提供預定義和 Gen AI 產生的問題列表，幫助使用者發現數據集中可能未曾想到的洞察。此外，Data Canvas 提供了一個視覺化體驗，讓數據分析師和業務使用者可以瀏覽數據、合併不同的數據集、執行提示、產生視覺化效果，並與同事分享分析結果。

Looker 則負責數據民主化的最後一哩路，讓業務使用者可以自行執行 BI 分析並建立儀表板。透過 Connected Sheets，使用者可以直接在 BigQuery 數據集和 Looker 數據平台上使用試算表。Looker 還推出了對話式分析功能，讓使用者可以透過代理體驗與數據互動並產生洞察。最後，使用者可以透過嵌入 Looker 儀表板或重新標記整個平台來將數據分析變現。

## 3. 重要結論

GCP 透過 BigQuery、Looker 和 Vertex AI 的整合，為小型團隊提供了一個強大且易於使用的進階分析平台，解決了數據集中化、數據清理、洞察發現和數據民主化等方面的挑戰。這些工具的整合和 AI 的應用，使小型團隊能夠更有效地利用數據，並從中獲得有價值的洞察。
