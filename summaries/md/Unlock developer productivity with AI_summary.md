# Unlock developer productivity with AI

[會議影片連結](https://www.youtube.com/watch?v=BIBO4lqzCxs)
用 AI 解鎖開發者生產力

## 1. 核心觀點

本次演講主要探討 Google 如何利用 AI 提升內部開發者的生產力，並分享了相關的經驗和觀察到的典範轉移。核心觀點包括：

*   AI 對開發者生產力的顯著提升：使用 AI 最多的開發者（前四分之一）在程式碼提交量和開發速度上都有明顯提升。
*   開發者對 AI 的積極態度：大多數 Google 工程師認為 AI 對他們的生產力產生了積極影響。
*   從增強到轉型的典範轉移：業界需要從利用 AI 增強軟體開發，轉向利用 AI 轉型軟體開發，最終實現軟體普及化。
*   擁抱新的軟體開發模式：例如透過自然語言提示與 IDE 互動的「Vibe Coding」。
*   AI 在減少技術債務中的作用：AI 可以幫助改善程式碼品質、測試和修復錯誤。
*   變革需要擁抱、投資和教育：擁抱新的典範、投資使用者體驗、結合輔助型和代理型 AI 技術，並加強開發者教育。

## 2. 詳細內容

Google 發現，使用 AI 最多的開發者，程式碼提交量增加了 13% 以上，在受控環境下的編碼速度提高了約 21%。此外，四分之三的 Google 工程師表示，AI 對他們的生產力產生了積極影響。

演講者強調，業界需要從利用 AI 增強軟體開發，轉向利用 AI 轉型軟體開發。這意味著利用 AI 的代理技術、更好的推理能力和多模態能力。最終目標是實現軟體普及化，讓更多人能夠參與軟體開發。

「Vibe Coding」是一種新的軟體開發模式，開發者可以使用自然語言提示與 IDE 和其他編碼介面互動，快速學習新的程式碼庫和技術，並完成任務。聊天功能可以快速解釋程式碼庫，問答介面可以幫助工程師更快地找到和完成任務。

隨著代理型 AI 能力的出現，工程師可以將任務委派給 AI，讓 AI 自動執行複雜的工作流程，減少開發者的參與。AI 也是減少技術債務的重要工具，可以幫助改善程式碼品質、測試和修復錯誤。調查顯示，超過 80% 的工程師表示曾受到技術債務的阻礙，而團隊通常需要花費 20% 到 50% 的時間來處理技術債務。

演講者總結了幾個重要的經驗教訓：首先，需要擁抱新的軟體開發模式。其次，投資使用者體驗至關重要，以便在低摩擦的變更和高影響的顛覆性變更之間取得平衡。第三，透過仔細結合輔助型和代理型 AI 技術，可以解決相當困難的軟體協調和部署挑戰。最後，需要投資於變更管理和開發者教育，以便大規模地推廣這些能力。

## 3. 重要結論

AI 正在深刻地改變軟體開發，Google 透過內部實踐證明了 AI 對開發者生產力的顯著提升。業界需要擁抱這些新的典範，並投資於相關的技術和教育，才能充分利用 AI 的潛力，最終實現軟體普及化。
