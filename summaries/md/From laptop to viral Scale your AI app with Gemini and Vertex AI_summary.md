# From laptop to viral Scale your AI app with Gemini and Vertex AI
[會議影片連結](https://www.youtube.com/watch?v=eoqC0S2ss20)
從筆記型電腦到爆紅：使用 Gemini 和 Vertex AI 擴展您的 AI 應用程式

## 1. 核心觀點

本次會議主要討論如何將一個在筆記型電腦上開發的 AI 應用程式，擴展到能夠處理病毒式傳播的規模。會議以 AutoCal 這個應用程式為例，展示了如何透過事件驅動架構、應用程式託管和身份驗證等技術，來解決處理大量使用者、運行環境和安全性的問題。核心觀點強調了快速迭代的重要性，以及如何利用 Google Cloud 工具建立 CI/CD 流程，最終實現更高的影響力和價值。

## 2. 詳細內容

會議首先介紹了 AutoCal，這是一個能夠將螢幕截圖轉換為 Google 日曆事件的應用程式。為了讓 AutoCal 能夠應對病毒式傳播帶來的挑戰，需要解決三個主要問題：處理數百萬使用者、提供穩定的運行環境以及確保安全性。

為了解決這些問題，AutoCal 2.0 採用了以下架構：

*   **事件驅動架構：** 用於處理使用者提交的圖片。
*   **Firebase 應用程式託管：** 提供應用程式的運行環境。
*   **身份驗證：** 確保應用程式的安全性。

此外，會議還強調了快速迭代的重要性，並介紹了如何使用 Google Cloud 工具建立 CI/CD 流程，以便快速部署和更新應用程式。

會議中提到，雖然 AutoCal 2.0 的開發過程比最初版本花費了更多時間，也遇到了更多的困難，但最終的成果是顯著的：從只能供個人使用，到能夠被數百萬人使用，實現了更高的影響力和價值。

## 3. 重要結論

本次會議展示了如何利用 Google Cloud 的各種工具和技術，將一個在筆記型電腦上開發的 AI 應用程式，擴展到能夠處理病毒式傳播的規模。透過事件驅動架構、應用程式託管、身份驗證和 CI/CD 流程，AutoCal 2.0 成功解決了處理大量使用者、運行環境和安全性的問題，最終實現了更高的影響力和價值。會議強調了快速迭代的重要性，以及 Google Cloud 工具在實現這一目標中的作用。
