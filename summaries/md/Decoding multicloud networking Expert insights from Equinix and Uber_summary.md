Decoding multicloud networking Expert insights from Equinix and Uber

[會議影片連結](https://www.youtube.com/watch?v=AyWjgni_nyg)
解碼多雲網路：來自 Equinix 和 Uber 的專家見解

## 1. 核心觀點

本次會議主要探討了多雲網路環境下企業面臨的挑戰，以及 Equinix 如何協助企業應對這些挑戰。核心觀點包括：

*   多雲環境的普及：90% 的企業正在使用多個公有雲。
*   多雲環境的挑戰：缺乏標準、可見性不足、效能和延遲問題。
*   Equinix 的解決方案：透過其全球資料中心網路和服務，提供低延遲的雲到雲路由和簡化的多雲連接。
*   Uber 的案例：展示了如何使用 Equinix Fabric Cloud Router 解決數據遷移問題。

## 2. 詳細內容

會議首先指出，儘管多雲策略已成為常態，但企業在使用多個公有雲時面臨許多挑戰。不同雲服務供應商之間缺乏統一的標準，導致在 Google Cloud 上運作良好的應用程式，在 AWS 上可能需要不同的配置。此外，多雲環境缺乏可見性，使得監控和管理變得複雜。效能和延遲也可能因雲服務供應商和網路配置而異。

Equinix 透過其在全球 35 個國家、70 多個都會區的 260 多個資料中心，以及與 3000 多家雲端和 IT 服務供應商和 2000 多家網路服務供應商的合作，提供解決方案。Equinix 位於雲網路、私有網路和網際網路的交匯點，能夠提供低延遲的雲到雲路由。客戶可以使用 Equinix Fabric Cloud Router 或 Equinix Network Edge 等功能，簡化多雲旅程。此外，Equinix 平台上還提供來自 Cisco、Palo Alto、Checkpoint、Aviatrix、F5 等領先供應商的 VNF。

Uber 的案例研究展示了 Equinix 解決方案的實際應用。Uber 需要將大量資料從一個公有雲遷移到 Google Cloud。由於公共網際網路的效能和延遲問題，以及擔心飽和現有的網路基礎設施，Uber 之前的策略無法使用。透過使用 Equinix Fabric Cloud Router，Uber 能夠在不到一天的時間內建立服務，並開始在公有雲和 GCP 之間傳輸流量。完成遷移後，他們可以關閉服務，展現了 Equinix 解決方案的按需特性、低延遲和安全效能連接。

## 3. 重要結論

本次會議強調了多雲網路環境下企業面臨的挑戰，並展示了 Equinix 如何透過其全球基礎設施和服務，協助企業克服這些挑戰。Uber 的案例研究證明了 Equinix 解決方案的有效性。會議鼓勵有興趣的企業訪問 Equinix 網站，註冊免費的 Fabric Cloud Router 或 Network Edge，開始他們的多雲旅程。
