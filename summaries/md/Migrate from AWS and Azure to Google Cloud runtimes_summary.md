# Migrate from AWS and Azure to Google Cloud runtimes
[會議影片連結](https://www.youtube.com/watch?v=Ai0syylcREw)
從 AWS 和 Azure 遷移到 Google Cloud 執行環境

## 1. 核心觀點

本次會議主要探討了將工作負載從 AWS 和 Azure 遷移到 Google Cloud 的關鍵要素，以及在遷移過程中進行現代化的優勢。Nokia 的轉型歷程被作為案例研究，展示了理論知識如何在實際應用中落地。會議強調了評估、理解不同雲服務之間的差異、數據遷移挑戰以及部署流程的重要性。同時，會議也強調了現代化不僅僅是遷移，更應該與業務目標對齊，並持續進行。Google Cloud 的 AI 能力、優化的基礎設施、安全性和可靠性以及降低 TCO 的能力，使其成為有吸引力的遷移目標。

## 2. 詳細內容

會議首先介紹了 Telegraph Media Company 的案例，該公司透過遷移到 Cloud Run 無伺服器架構，顯著降低了成本和碳排放。

接著，會議深入探討了客戶選擇 Google Cloud 的原因：

*   **Google AI：** Google AI 與 Google Cloud 的深度整合，從底層基礎設施到 Gemini 模型，都體現了 AI 的價值。
*   **工作負載優化的基礎設施：** Google Cloud 的基礎設施針對 AI、雲原生和企業工作負載進行了優化，提高了效能並降低了成本。
*   **安全性和可靠性：** Google 憑藉深厚的 SRE 文化，在安全性方面表現出色。
*   **現代化：** Google 在容器化和 Kubernetes 方面具有領先地位，能夠幫助客戶更好地進行現代化轉型。
*   **更低的 TCO：** 透過 Google AI、優化的基礎設施和現代化方案，客戶可以降低總體擁有成本。

會議接著討論了雲端遷移的關鍵要素：

*   **評估：** 全面了解現有環境，包括工作負載、數據、應用程式、依賴關係、支援服務和實體設備。
*   **理解差異：** 了解 AWS 和 Azure 服務與 Google Cloud 服務在功能和架構上的差異，特別是在私有叢集、端點存取控制和身份驗證方面。
*   **數據遷移挑戰：** 建立專門的團隊負責數據遷移，選擇合適的工具，並關注網路頻寬、數據使用情況以及數據傳輸過程中的安全監控。
*   **部署流程：** 確保部署和運營流程（包括作業系統映像、應用程式部署套件和容器映像）與 Google Cloud 環境相容。

會議強調，僅僅進行「搬遷」式的遷移存在局限性，因此建議在遷移的同時考慮現代化。現代化可以帶來以下好處：

*   **業務需求：** 驅動收入增長、降低成本並提供競爭優勢。
*   **評估：** 更深入地評估現有環境，識別依賴關係和技術債務。
*   **可見性和風險：** 採取分階段的方法，先進行搬遷，然後逐步進行現代化。
*   **基礎：** 建立完善的資源層級、安全機制、網路和存取管理。
*   **持續性：** 將現代化視為一個持續的過程，與長期目標保持一致。

Nokia 的案例研究展示了如何將這些原則應用於實際的雲端轉型。Nokia 將其 CICD 管道從 AWS 遷移到 Google Cloud，並在遷移過程中進行了現代化。這次遷移涉及 10 萬多個管道和 27,000 多個作業，服務於 7,000 多個使用者。Nokia 的目標是提高效能、確保業務連續性，並找到能夠幫助他們進行現代化的合作夥伴。

Nokia 評估了其他雲端平台，但最終選擇了 Google Cloud，因為它符合 Nokia 的整體策略。Nokia 設定了嚴格的成功標準，包括效能、業務連續性和合作夥伴的現代化能力。在遷移過程中，Nokia 與合作夥伴緊密合作，解決了數據遷移和停機時間方面的挑戰。最終，Nokia 實現了 30% 的效率提升，並提高了開發人員的生產力。

會議最後展示了 Google Cloud 提供的一系列工具，可以幫助客戶進行雲端遷移和現代化，包括：

*   **Migration Center：** 提供統一的中心來管理遷移。
*   **Migration Center Discovery Client：** 盤點現有雲端基礎設施。
*   **CodeMod：** 使用 AI 分析程式碼，幫助客戶進行程式碼現代化。
*   **Migrate to Virtual Machines：** 將虛擬機器從其他雲端環境遷移到 Google Cloud。
*   **Migrate to Containers：** 將在虛擬機器上執行的應用程式容器化，以便在雲原生架構中更有效率地執行。
*   **Database Migration Service：** 簡化雲端之間資料庫的遷移。
*   **Storage Transfer Service：** 安全地處理大量資料的傳輸。

## 3. 重要結論

將工作負載從 AWS 和 Azure 遷移到 Google Cloud 是一個複雜的過程，需要仔細的規劃、評估和執行。透過將現代化納入遷移策略，企業可以獲得更大的收益，包括降低成本、提高效能和提高開發人員的生產力。Google Cloud 提供了一系列工具和服務，可以幫助客戶簡化遷移過程並實現其雲端目標。Nokia 的案例研究證明了透過與經驗豐富的合作夥伴合作，並採用系統化的方法，可以成功地完成雲端轉型。
