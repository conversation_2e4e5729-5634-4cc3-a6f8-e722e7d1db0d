# Mainframe modernization with AI and the cloud Customer stories
[會議影片連結](https://www.youtube.com/watch?v=rMa3a6_80f4)
大型主機現代化與人工智慧和雲端客戶案例

## 1. 核心觀點

本次會議主要探討了在 Google Cloud 上進行大型主機現代化的各種方法和客戶案例。核心觀點包括：

*   大型主機現代化並非單一解決方案，而是一個多模式問題，需要根據業務優先級選擇合適的現代化模式。
*   Google Cloud 提供多種解決方案，包括評估工具、重新託管、擴增、重新平台化、重構、重寫以及替換等，並與合作夥伴共同提供完整的現代化旅程支援。
*   生成式 AI 在大型主機現代化中扮演重要角色，可用於評估、理解應用程式邏輯，並協助程式碼重寫和應用程式重新設計。
*   雙軌運行（Dual Run）是驗證和降低風險的重要手段，允許客戶在一段時間內並行運行大型主機應用程式和現代化應用程式，以確保功能等效。
*   Google Cloud 強調與合作夥伴的合作，提供廣泛的技術和服務，以應對大型主機環境的多樣性。

## 2. 詳細內容

*   **Google Cloud 的大型主機現代化方案：** David Yalom 介紹了 Google Cloud 在大型主機現代化方面的產品和服務，強調其並非單一方案，而是根據客戶需求提供多種模式。這些模式包括：
    *   **評估工具：** 使用生成式 AI（Gemini）客製化，評估大型主機，逆向工程應用程式，建立知識庫，總結業務邏輯，產生應用程式規格和測試案例。
    *   **重新託管：** 將大型主機應用程式遷移到 Google Cloud。
    *   **擴增：** 使用 Google Cloud 的功能來增強大型主機，例如 Ford Motor Credit 的案例，將資料倉儲工作負載從內部部署遷移到 Google Cloud。
    *   **重新平台化：** 將大型主機應用程式遷移到新的平台。
    *   **重構：** 基於演算法的轉換，例如將 COBOL 轉換為 Java。
    *   **重寫：** 使用客製化的 Gemini 模型，完全重新設計大型主機應用程式。
    *   **替換：** 使用現成的商業解決方案替換大型主機應用程式，例如 Intesa Sanpaolo 的案例，使用商業套裝軟體替換其核心銀行系統。
    *   **雙軌運行（Dual Run）：** 允許客戶並行運行大型主機應用程式和現代化應用程式，以驗證和認證遷移。
*   **AI 在大型主機現代化中的應用：** Google Cloud 利用其 AI 技術堆疊，包括 AI 基礎設施、研究、模型（Gemini）和工具，為大型主機客戶提供企業級產品。生成式 AI 用於評估和重寫大型主機程式碼，通過評估理解應用程式的功能、輸入、輸出和資料流，然後使用重寫解決方案重新設計應用程式。
*   **合作夥伴生態系統：** Rajesh 強調 Google Cloud 與眾多合作夥伴的合作，這些合作夥伴提供交付能力、技術和服務，以應對大型主機環境的多樣性。合作夥伴包括 Accenture、EPAM、Kindle 和 ThoughtWorks，他們共同提供基於生成式 AI 的重寫解決方案。
*   **客戶案例：**
    *   **Intesa Sanpaolo：** Claudio 介紹了 Intesa Sanpaolo 如何使用商業套裝軟體替換其核心銀行系統，並使用雙軌運行（Dual Run）來驗證和認證遷移。他們面臨的挑戰包括延遲、事務管理、系統整合和測試。他們通過測試延遲的影響、引入冪等性模式、建立 API 抽象層和使用雙軌運行來解決這些挑戰。
    *   **Ford Motor Credit：** Sumandeep Singh 和 Ratna Valabineni 分享了 Ford Motor Credit 如何使用大型主機連接器將資料倉儲工作負載從大型主機遷移到 Google Cloud。他們面臨的挑戰包括複雜性、缺乏文檔、資源限制和嚴格的監管要求。他們通過使用大型主機連接器、建立可重用的轉換模式和解決效能問題來克服這些挑戰。

## 3. 重要結論

本次會議展示了 Google Cloud 在大型主機現代化方面的能力和解決方案，以及客戶如何成功地將大型主機工作負載遷移到 Google Cloud。會議強調了多模式方法、AI 的應用、合作夥伴生態系統和雙軌運行（Dual Run）的重要性。Intesa Sanpaolo 和 Ford Motor Credit 的案例證明了 Google Cloud 可以幫助客戶實現大型主機現代化，並獲得業務價值。
