The AI revolution in BI A deep dive into Gemini in Looker
        
[會議影片連結](https://www.youtube.com/watch?v=lAbDDUrCzwU)
AI 驅動的商業智慧革命：深入探討 Looker 中的 Gemini

**1. 核心觀點**

本次會議深入探討了 Gemini 在 Looker 中如何引領 AI 驅動的商業智慧革命。核心觀點包括：

*   AI 的力量取決於其背後的治理。
*   Looker 結合 Gemini，透過先進分析、嵌入式分析、資料建模和自助服務功能，提高效率並觸發工作流程。
*   Gemini 和 Looker 旨在提高分析師和業務使用者的生產力。
*   Looker 的 Agentic AI 架構是所有 Gemini 功能的基礎，提供高品質、高準確性和可靠性的答案。
*   語意模型對於生成式 AI 的準確性至關重要。
*   透過 Python 程式碼解譯器，將資料科學民主化，並將其引入對話式分析領域。
*   透過 API 將對話式 AI 體驗擴展到使用者所在之處。

**2. 詳細內容**

會議詳細介紹了 Gemini 和 Looker 的五個主要功能：

*   **對話式分析：** 透過自然語言處理，使用者可以直接與資料互動，減少對技術團隊的依賴，提高生產力和效率。
*   **自動簡報投影片生成：** 利用 AI 從資料報告中自動生成簡報投影片，並提供圖表摘要，幫助使用者快速理解視覺化資訊。
*   **公式助手：** 協助使用者創建公式，簡化操作流程，提高工作效率。
*   **LookML 助手：** 利用自然語言和 AI 代理，簡化資料模型的創建過程，提供起點，並加速模型建立。
*   **視覺化助手：** 允許使用者以自然語言提問，並自動生成視覺化圖表，簡化圖表建立過程。

此外，會議還深入探討了 Looker 的 Agentic AI 架構，強調其在提供高品質、高準確性和可靠性答案方面的重要性。Agentic AI 架構利用 Gemini 的推理能力，透過一系列工具解決問題，並提供類似人類的對話體驗。語意模型在提高生成式 AI 準確性方面扮演關鍵角色，透過 LookML，可以標準化業務指標，確保資料的一致性和準確性。

會議還介紹了如何使用 Python 程式碼解譯器進行更進階的分析，例如關鍵驅動因素分析、根本原因分析、關聯性和預測等，從而將資料科學民主化，並將其引入對話式分析領域。

Supermetrics 的 CEO Ansi Rutsi 分享了他們如何利用 Google Cloud 和 Supermetrics 的平台，將行銷資料整合到 BigQuery 中，並使用 Looker 和 Gemini 提高效率、解鎖洞察並驅動決策。他們展示了如何使用 Gemini 和 Looker 快速生成簡報投影片，並透過對話式分析即時回答問題。

最後，會議還展示了 Gemini 和 Looker 的未來發展藍圖，包括將對話式 AI 功能作為 API 提供，以便使用者可以將這些體驗擴展到其他應用程式中，以及進一步改進 LookML 的編寫體驗，利用 Gemini 助手自動生成 LookML 模型。

**3. 重要結論**

Gemini 和 Looker 的結合正在引領 AI 驅動的商業智慧革命，透過提高分析師和業務使用者的生產力、簡化資料模型的創建過程、將資料科學民主化以及提供高品質、高準確性和可靠性的答案，幫助企業更好地利用資料，做出更明智的決策。將對話式 AI 功能作為 API 提供，以及進一步改進 LookML 的編寫體驗，將進一步推動 AI 在商業智慧領域的應用。
