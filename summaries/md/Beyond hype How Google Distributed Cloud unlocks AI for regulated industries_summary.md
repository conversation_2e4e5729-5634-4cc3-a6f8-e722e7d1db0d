Beyond hype How Google Distributed Cloud unlocks AI for regulated industries
        
[會議影片連結](https://www.youtube.com/watch?v=a03H3BxQzno)
超越炒作：Google Distributed Cloud 如何為受監管產業釋放 AI 潛力

## 1. 核心觀點

本次會議主要探討 Google Distributed Cloud (GDC) 如何將生成式 AI 的強大功能帶到邊緣，讓資料在本地端進行處理，解決了合規性、資料落地性以及低延遲等問題。GDC 旨在彌合雲端創新與客戶資料本地化需求之間的差距，為客戶提供在安全、離線環境中部署和操作 AI 應用的能力。

## 2. 詳細內容

*   **Google Distributed Cloud (GDC) 的定位與價值：** GDC 是一個軟硬體產品，旨在將 Google AI 的優勢帶到客戶的資料所在地。它不僅包含 AI 服務，還包括構建應用程式所需的完整 IaaS 和 PaaS 服務堆疊。GDC 基於 Kubernetes 和開源元素構建，可以根據工作負載和需求進行擴展。

*   **GDC 的三種部署模式：**
    *   **軟體版本：** 僅包含軟體，可部署在客戶現有的基礎架構上，主要用於容器化和現代應用程式。
    *   **連線版本：** 結合硬體和軟體，提供更具規範性的完整堆疊方法，支援從小規模到大規模的部署。
    *   **氣隙版本：** 專為高度安全的環境設計，從第一天到第二天都可以在完全氣隙的環境中部署。

*   **GDC 氣隙版本的核心原則：** 確保客戶擁有主權部署，可以自行操作和擴展系統，而無需外部干預。整個產品從頭開始構建，充分考慮了氣隙環境的需求。

*   **GDC 的技術堆疊：** GDC 利用合作夥伴的硬體（如 HPE 的伺服器、NetApp 的儲存、NVIDIA 的 GPU 和 Cisco 的網路），並在其之上構建一致的 API，以提供基礎架構即服務。它還提供 VM 服務、Kubernetes 服務和網路服務，以及託管的 PaaS 服務，如日誌管理、運營管理和 KMS 系統。此外，GDC 還提供資料生命週期層的資料庫和分析解決方案，以及 Gemini 的功能，讓客戶可以使用 Google 提供的服務或使用自己偏好的模型花園構建自己的解決方案。

*   **GDC Sandbox：** 為了讓客戶在實際部署之前體驗產品，Google 提供了 GDC Sandbox，它在 GCP 上提供 GDC 體驗，讓客戶可以在一致的框架中構建應用程式。

*   **GDC 的應用案例：**
    *   **SAP Business Technology Platform (BTP)：** GDC 將支援 SAP BTP，這是一個雲原生架構，為大型組織提供現代 ERP 解決方案。SAP BTP 包含數十個微服務，可以在 GDC 的氣隙環境和安全網路上運行，將平台的功能帶到以前無法存取的資料。
    *   **Agent Space：** Agent Space 可以連接到企業資料來源（如 Jira、ServiceNow 或文件儲存桶），並使用 Gemini 來增強搜尋結果，讓使用者可以更輕鬆地找到所需的資訊，並確保只返回使用者有權存取的資料。

*   **客戶案例分享：**
    *   SAIC 的代表分享了他們如何使用 GDC 將 AI 功能帶到偏遠地區，即使在沒有網路連線的情況下也能提供強大的運算能力。
    *   Accenture 的代表分享了公共部門客戶如何利用 GDC 來解決資料安全和合規性方面的挑戰，並加速 AI 的採用。

*   **GDC 的便攜性與適應性：** GDC 的便攜性使其能夠適應不斷變化的環境，並在需要時快速部署到不同的位置。它還具有高度的靈活性，可以與現有的系統和應用程式整合。

## 3. 重要結論

Google Distributed Cloud 提供了一個強大的平台，可以將生成式 AI 的優勢帶到受監管的產業和邊緣環境，解決了資料安全、合規性和延遲等方面的挑戰。透過 GDC，客戶可以在安全、離線的環境中部署和操作 AI 應用程式，並加速創新。GDC 的靈活性、便攜性和可擴展性使其成為各種用例的理想選擇，並有助於推動 AI 在各個行業的採用。
