# Secure, compliant gen AI deployment with Gemini and Google Workspace_2
[會議影片連結]()
安全且合規的 Gemini 和 Google Workspace Gen AI 部署

## 1. 核心觀點

本次會議主要討論如何安全且合規地部署 Gemini 和 Google Workspace，強調在利用生成式 AI 提升生產力的同時，不犧牲安全性。核心觀點包括：

*   **Workspace 的安全基礎：** 利用零信任原則，提供對用戶訪問資訊的細粒度控制，以及他們可以執行的操作。
*   **客製化的資訊保護：** 透過資料外洩防護（DLP）功能和資料主權控制，根據組織的獨特需求保護資訊。
*   **與 SecOps 平台的整合：** 將 Workspace 和 Gemini 的資訊整合到 SecOps 平台，確保組織安全。
*   **Gemini 的合規性支援：** 廣泛的合規性支援，包括 FedRAMP High 授權和 13 項其他標準。
*   **預設安全：** 透過嚴格的模型訓練和內建防護措施，確保 Gemini 的安全操作。
*   **進階資料治理：** 限制用戶只能透過 Gemini 訪問他們已經有權限的資料，防止過度分享。
*   **客製化的資料保護策略：** 提供客製化資料保護策略的能力，以滿足組織的獨特需求。
*   **Gmail 的新功能：** 推出超過 10 項新功能，以保護 Gmail 中的敏感資料，包括標籤功能和自動應用標籤的策略。

## 2. 詳細內容

會議首先介紹了 Google Workspace 內建的安全功能，強調零信任原則和細粒度控制，允許組織控制用戶可以訪問的資訊以及他們可以執行的操作，例如轉發電子郵件或列印文件。此外，還強調了資料外洩防護（DLP）功能和資料主權控制，允許組織根據其獨特需求保護資訊。Workspace 還與 SecOps 平台、Chrome Enterprise 和 Mandiant 整合，以提供更全面的安全性。

接著，會議討論了 Gemini 的安全性和合規性。強調 Gemini 具有廣泛的合規性支援，包括 FedRAMP High 授權和 13 項其他標準。Gemini 預設是安全的，這意味著模型訓練和用戶互動都受到嚴格的保護。Google 開發自己的基礎模型，以確保適當的安全性保護，包括高品質的資料、防止模型中毒和徹底的對抗性測試。此外，Gemini 還具有內建的防護措施，可防止提示注入和其他新興威脅。

會議還強調了進階資料治理的重要性，限制用戶只能透過 Gemini 訪問他們已經有權限的資料，防止過度分享。資訊權限管理（IRM）允許組織控制用戶可以對敏感資料執行的操作，例如列印、轉發或分享。客戶端加密功能進一步限制了對敏感資料的訪問。

會議還展示了增強的 AI 分類功能，該功能允許組織使用自己的資料訓練模型，以自動對文件進行分類。這有助於自動執行敏感度分類，例如將文件標記為機密或公開資訊。

最後，會議介紹了 Gmail 的新功能，這些功能旨在保護 Gmail 中的敏感資料。這些功能包括新的標籤功能，允許組織將相同的標籤應用於 Drive 和 Gmail。組織還可以定義策略，以根據定義的標準自動將標籤應用於電子郵件。此外，還增強了稽核日誌記錄，並與 BigQuery 和 Vault 整合，以提供對敏感資料流動的全面可見性。

## 3. 重要結論

本次會議強調了在部署 Gemini 和 Google Workspace 時，安全性和合規性的重要性。透過利用 Workspace 內建的安全功能、Gemini 的合規性支援和進階資料治理功能，組織可以在不犧牲安全性的情況下，利用生成式 AI 提升生產力。會議還強調了客製化資料保護策略的重要性，以滿足組織的獨特需求。總之，Gemini 和 Workspace 的結合，為企業提供了一個安全且合規的生成式 AI 解決方案。
