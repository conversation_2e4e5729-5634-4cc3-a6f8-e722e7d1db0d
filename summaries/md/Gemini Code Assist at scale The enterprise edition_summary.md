```
# Gemini Code Assist at scale The enterprise edition
[會議影片連結](https://www.youtube.com/watch?v=LvurE7IcavU)
Gemini Code Assist at scale 企業版

## 1. 核心觀點

本次會議主要介紹了 Gemini Code Assist 企業版的功能和優勢，特別是在大規模企業應用場景下的授權管理、安全性、合規性以及資料落地等方面的解決方案。會議強調了 Gemini Code Assist 如何幫助企業提升開發者效率、保障資料安全，並滿足不同地區和行業的合規性要求。此外，還展示了 Gemini Code Assist 在 Apigee 和應用程式整合方面的具體應用，以及如何透過 Gemini Code Assist 簡化 API 管理和應用程式整合流程。

## 2. 詳細內容

**授權管理：**

*   **自動授權管理：** Gemini Code Assist 服務自動管理授權，開發者登入時自動獲取授權，閒置時自動釋放授權，提高授權利用率。可設定閒置時間（例如七天）後自動釋放授權。
*   **客製化 IAM 角色和群組：** 透過建立自訂 IAM 角色和群組，可以更精細地控制授權分配，例如允許部分開發者自動獲取授權，而限制其他開發者。
*   **示範：** 示範了如何建立自訂角色，限制使用者自行獲取授權，並將授權分配給群組，最後使用 Policy Analyzer 驗證設定是否正確。

**安全性：**

*   **稽核日誌：** 可以開啟稽核日誌，收集所有開發者的提示和回應，包括上下文、前綴、後綴以及使用的檔案。日誌資料儲存在使用者控制的儲存桶中，Google 無法存取。
*   **AI 排除檔案：** 可以定義 AI 排除檔案，類似於 `.gitignore`，限制索引器從私有儲存庫中提取的內容，也可以在開發者的本機機器上設定，限制本機上下文。
*   **限制外發資料：** 企業客戶可以限制外發遙測資料（例如活躍使用者數量、程式碼輔助建議接受率等），也可以關閉意見回饋控制項，防止開發者傳送不希望暴露給 Google 的資訊。
*   **網路封鎖：** 可以完全封鎖從企業網路外部使用 Gemini，透過配置代理和自訂標頭來確保流量安全。

**法律風險：**

*   **AI 賠償政策：** Google 對 Gemini 產生的程式碼提供 AI 賠償政策，如果因版權問題受到質疑，Google 將承擔潛在的法律風險。
*   **引用封鎖：** 可以封鎖所有引用，防止開發者使用可能受特定授權約束的程式碼。未來將提供更靈活的控制，允許或封鎖特定授權。

**資料落地：**

*   **全球服務：** Gemini Code Assist 目前是一個全球服務，由美國、歐盟和太平洋地區的資料中心提供服務。
*   **資料靜態儲存：** Gemini Code Assist 模型是無狀態的，不儲存任何資料。
*   **日誌儲存：** 使用者可以選擇日誌儲存的特定區域，並使用日誌路由確保所有日誌都儲存在所選區域。
*   **企業程式碼客製化：** 企業程式碼客製化服務將所有資料（程式碼片段、索引等）儲存在使用者選擇的區域中的 AlloyDB 資料庫執行個體中。
*   **2025 年路線圖：** 簡化資料落地配置，透過單一設定指定索引建立位置、Gemini 推論位置和日誌儲存位置。

**Apigee 和應用程式整合：**

*   **Apigee：** Gemini Code Assist 在 Apigee 中提供生成 OpenAPI 規範、輔助 Proxy 編寫和集中管理 API 等功能。可以利用企業上下文，重用 API Hub 中已註冊的 Schema、中繼資料和安全策略，保持 API 的一致性。
*   **應用程式整合：** Gemini Code Assist 可以生成整合流程，簡化應用程式之間的資料交換。可以將整合流程暴露為 Agent，並透過 ADK（Agent Development Kit）與 SAP 等應用程式連接。
*   **示範：** 示範了如何使用 Google 表單請求授權，並透過應用程式整合流程自動分配授權。

**使用者案例：**

*   Marsh McLennan 使用 Gemini Code Assist 進行雲端遷移，並管理 Apigee 開發者的授權和權限。
*   Gemini Code Assist 可以幫助新手開發者更快上手 Apigee，並解釋 Apigee 的獨特之處。

## 3. 重要結論

Gemini Code Assist 企業版為大型企業提供了全面的程式碼輔助解決方案，不僅可以提升開發者效率，還可以確保資料安全、滿足合規性要求，並簡化 API 管理和應用程式整合流程。透過自動授權管理、客製化 IAM 角色、稽核日誌、AI 排除檔案等功能，企業可以更好地控制 Gemini Code Assist 的使用，並保護敏感資料。未來，Google 將繼續投資於資料落地和區域擴展，以滿足不同地區和行業的客戶需求。
```