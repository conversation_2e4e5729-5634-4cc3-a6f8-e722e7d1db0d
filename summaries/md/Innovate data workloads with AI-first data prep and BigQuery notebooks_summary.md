Innovate data workloads with AI-first data prep and BigQuery notebooks

[會議影片連結](https://www.youtube.com/watch?v=2IqAJ1UoXEw)
運用 AI 優先的資料準備和 BigQuery 筆記本，革新資料工作負載

## 1. 核心觀點

本次會議主要探討了 BigQuery 中多項 AI 功能，以及如何利用這些功能創新資料工作負載。核心觀點包括：

*   **解決時間浪費問題：** 利用 Gemini 賦能的資料準備工具，簡化資料清理和 ETL 流程，從數週縮短到數分鐘。
*   **解決低效的資料分析問題：** 透過 BigQuery 筆記本，整合多種工具和引擎，打破組織內部的資料孤島，讓更多人能夠進行高效的資料分析。
*   **挖掘未開發的資料價值：** 利用 BigQuery 的統一資料平台，打破資料孤島，解鎖metadata，輕鬆獲得洞察。
*   **BigQuery Studio 的未來：** 打造一個無縫、單一、統一的工作空間，讓資料分析師、資料科學家和資料工程師能夠協同工作。
*   **GAF 的實踐經驗：** 分享了 GAF 如何利用 BigQuery 和 Data Prep 解決資料準備的挑戰，並建立可擴展、可信賴的資料平台。

## 2. 詳細內容

*   **Vidya Shanmugam 的開場：**
    *   介紹了 BigQuery 的 AI 和資料平台，以及如何利用它來創新資料工作負載。
    *   強調了 BigQuery 在資料準備、BigQuery 筆記本和 BigQuery Studio 方面的最新進展。
    *   指出了客戶面臨的三大挑戰：時間浪費、低效的資料分析和錯失的機會。
*   **Tim Bizzold 介紹 Gemini 賦能的資料準備：**
    *   宣布 BigQuery 資料準備正式 GA，這是一個 Gemini 功能，可讓使用者有效地準備資料以進行分析。
    *   強調了資料準備如何幫助使用者建立視覺化資料管道，自動產生 SQL，並管理 schema drift。
    *   介紹了 BigQuery 資料管道，這是一個新功能，允許使用者在單一點擊中為資料準備、SQL 查詢或 BigQuery 筆記本建立任務。
    *   強調了與 Dataform 的整合，允許使用者協作處理程式碼資產並將其發布到 Git 儲存庫。
*   **Vidya Shanmugam 介紹 BigQuery 筆記本：**
    *   介紹了 BigQuery 筆記本，這是一個 serverless 工作區，直接整合到 BigQuery console 中。
    *   強調了 BigQuery 筆記本如何支援多個引擎（BigQuery、Apache Spark、local pandas），並提供嵌入式 Gen AI 體驗。
    *   介紹了 Apache Spark 在 BigQuery 中的支援，允許使用者直接在 BigQuery 筆記本中進行批次或 serverless Spark 開發。
*   **Vidya Shanmugam 介紹 BigQuery Studio：**
    *   介紹了 BigQuery Studio，這是一個無縫、單一、統一的工作空間，適用於組織中的所有使用者。
    *   強調了 BigQuery Studio 如何打破資料孤島，解鎖 metadata，並輕鬆獲得洞察。
    *   介紹了 BigQuery Studio 的一些最酷的功能，包括與 Gemini Chat agent、SQL 查詢和程式碼完成以及程式碼助理的整合。
*   **Pooja Panchagnola 分享 GAF 的實踐經驗：**
    *   分享了 GAF 如何使用 Google Cloud Platform 和 BigQuery 作為其企業資料湖和雲端資料倉儲。
    *   介紹了 GAF 如何利用 Data Prep 解決資料準備的挑戰，並建立可擴展、可信賴的資料平台。
    *   強調了 Data Prep 的三個主要優點：抽樣和驗證選項、AI 產生的斷言以及建立可重複工作流程的能力。
    *   介紹了 GAF 的資料架構原則：模組化、可重複和可擴展。
*   **Tim Bizzold 的 Demo：**
    *   展示了如何使用 BigQuery 資料準備來清理、標準化和轉換來自 GAF 合作夥伴的資料。
    *   展示了如何使用 BigQuery 筆記本來分析資料並識別重複項。
    *   展示了如何使用 BigQuery 資料管道將所有這些任務整合到一個端到端管道中。

## 3. 重要結論

BigQuery 透過其 AI 功能和統一的資料平台，正在改變組織處理資料工作負載的方式。透過簡化資料準備、打破資料孤島和提供強大的分析工具，BigQuery 使組織能夠更快地獲得洞察並做出更好的決策。GAF 的實踐經驗證明了 BigQuery 的價值，並為其他希望革新其資料工作負載的組織提供了寶貴的見解。
