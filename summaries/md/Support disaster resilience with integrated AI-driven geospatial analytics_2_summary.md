# Support disaster resilience with integrated AI-driven geospatial analytics_2
[會議影片連結]()
Support disaster resilience with integrated AI-driven geospatial analytics_2

支援透過整合人工智慧驅動的地理空間分析來強化災害復原能力_2

## 1. 核心觀點

本次會議主要探討如何利用 Google 的先進地理空間技術，包括 Earth Engine、BigQuery 和 Weather Next 等，來支援自然災害的復原能力。會議強調了地理空間技術在災害生命週期的各個階段，從風險評估、應急響應到災後重建，所扮演的關鍵角色。核心觀點包括：

*   **地理空間技術的重要性：** 地理空間技術能夠提供對地球表面或近地表發生的事件的觀測數據，對於理解和應對自然災害至關重要。
*   **Earth Engine 和 BigQuery 的整合：** 透過 Earth Engine 和 BigQuery 的整合，可以更輕鬆地處理和分析大量的地理空間數據，從而提供更深入的洞察。
*   **Weather Next 的應用：** Weather Next 是一系列基於人工智慧的天氣預測模型，可以提供更準確的天氣預報，從而幫助人們更好地預測和應對自然災害。
*   **端到端解決方案：** 透過整合各種地理空間工具和數據集，可以構建端到端的解決方案，以應對自然災害的整個生命週期。

## 2. 詳細內容

會議首先介紹了地理空間技術的基本概念，包括向量數據和柵格數據，以及地理空間技術在永續發展方面的重要性。講者提到，隨著越來越多的衛星被發射到太空，我們可以獲得越來越多的地理空間數據，這為我們利用雲端運算和雲端儲存將這些數據轉化為洞察提供了機會。

接著，會議重點介紹了 Earth Engine 和 BigQuery 這兩個重要的地理空間工具。Earth Engine 是一個基於雲端的平台，提供大量的衛星影像和柵格數據，以及處理這些數據的能力。BigQuery 是一個完全託管的資料倉儲，可以進行 SQL 分析。透過將 Earth Engine 和 BigQuery 整合在一起，可以更輕鬆地進行地理空間分析，並從中獲得洞察。

會議還介紹了 Weather Next，這是一系列基於人工智慧的天氣預測模型。Weather Next 模型可以提供全球範圍內的高解析度天氣預報，並且可以預測未來 10 到 15 天的天氣狀況。這些模型已經被證明可以提供比傳統天氣預報模型更準確的預測。

最後，會議透過一個野火的例子，展示了如何利用各種地理空間工具和數據集來構建端到端的解決方案，以應對自然災害的整個生命週期。這個例子涵蓋了野火風險評估、應急響應和災後重建等各個階段。

## 3. 重要結論

本次會議強調了地理空間技術在支援自然災害復原能力方面的重要性。透過整合 Earth Engine、BigQuery 和 Weather Next 等工具，我們可以更有效地處理和分析地理空間數據，從而提供更深入的洞察，並構建端到端的解決方案，以應對自然災害的整個生命週期。會議也鼓勵大家積極探索和利用這些工具，為應對日益頻繁的自然災害做出貢獻。
