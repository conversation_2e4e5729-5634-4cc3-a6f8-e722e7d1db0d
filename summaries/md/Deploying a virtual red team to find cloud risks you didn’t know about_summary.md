# Deploying a virtual red team to find cloud risks you didn’t know about
[會議影片連結](https://www.youtube.com/watch?v=TdGkKm5KGhY)
部署虛擬紅隊，找出您未知的雲端風險

## 1. 核心觀點

本次會議主要探討如何利用虛擬紅隊來發現雲端環境中潛在的風險。講者介紹了 Google 如何透過安全圖（Security Graph）來建模風險和威脅，並利用持續的虛擬紅隊演練來驗證和揭露未知的風險。會議重點包括：

*   **可見性和風險管理：** 強調了解雲端環境中的漏洞、活動和潛在攻擊路徑的重要性。
*   **情境感知的重要性：** 強調在風險評估中考慮資產、數據、漏洞、活動和權限等情境因素。
*   **安全圖（Security Graph）：** 介紹了安全圖作為一種建模雲端環境中資源和關係的工具，以便更好地理解和應對風險。
*   **虛擬紅隊（Virtual Red Teaming）：** 介紹了虛擬紅隊的概念，它使用數位雙生模型來模擬攻擊，以發現潛在的攻擊路徑和風險。
*   **問題（Issues）：** 介紹了 SEC（Security Command Center）中的新概念「問題」，它代表了使用者應該優先處理的最重要的風險。
*   **毒性組合（Toxic Combinations）和扼流點（Choke Points）：** 介紹了虛擬紅隊發現的兩種主要風險類型，毒性組合代表了通往高價值資源的最可能路徑，而扼流點則代表了修復後能帶來最大效益的雲端資源。

## 2. 詳細內容

*   **客戶面臨的挑戰：** 客戶在雲端環境中面臨著可見性、風險評估和優先排序等挑戰。他們需要了解環境中的漏洞、活動和潛在攻擊路徑。
*   **Google 的風險建模方法：** Google 使用安全圖來建模雲端環境中的資源和關係。安全圖允許他們關聯來自不同來源的發現，並理解攻擊者如何利用這些發現來達到他們的目標。
*   **持續虛擬紅隊：** 虛擬紅隊使用數位雙生模型來模擬攻擊。這個模型包含關於資源、威脅、漏洞、身份和其他相關資訊的詳細資料。虛擬紅隊使用來自 Google 威脅情報團隊和 Mandiant 的威脅情報，以確保模擬的攻擊是現實的。
*   **問題（Issues）：** 問題是 SEC 中的一個新概念，它代表了使用者應該優先處理的最重要的風險。問題分為三種類型：安全圖規則、毒性組合和扼流點。
*   **毒性組合（Toxic Combinations）：** 毒性組合代表了通往高價值資源的最可能路徑。它們是漏洞、錯誤配置和弱身份驗證的組合，攻擊者可以利用這些組合來達到他們的目標。
*   **扼流點（Choke Points）：** 扼流點是修復後能帶來最大效益的雲端資源。它們是多個攻擊路徑的共同點，修復它們可以阻止多個攻擊。
*   **安全圖（Security Graph）：** 安全圖是一種建模雲端環境中資源和關係的工具。它允許使用者理解誰可以訪問什麼資源，以及攻擊者如何利用這些訪問權限來達到他們的目標。
*   **風險報告（Risk Report）：** 風險報告是一個執行摘要，其中包含關於組織最關鍵問題的資訊。它包括關於扼流點和毒性組合的資訊，以及關於如何修復這些問題的建議。

## 3. 重要結論

透過部署虛擬紅隊並利用安全圖，組織可以更好地理解和應對雲端環境中的風險。虛擬紅隊可以發現潛在的攻擊路徑和風險，而安全圖可以幫助組織理解誰可以訪問什麼資源。透過優先處理問題並修復扼流點，組織可以顯著降低其攻擊面並提高其安全性。會議最後提供了一個免費安全風險評估的連結，鼓勵大家註冊以了解更多關於自身組織的風險狀況。
