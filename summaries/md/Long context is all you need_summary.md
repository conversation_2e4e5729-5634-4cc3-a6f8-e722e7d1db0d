# Long context is all you need

[會議影片連結](https://www.youtube.com/watch?v=MVhAzBJnyQI)
長上下文才是你需要的

## 1. 核心觀點

本次會議主要討論了長上下文模型的重要性及其在解決大型語言模型（LLM）挑戰中的作用。核心觀點包括：

*   上下文對於模型理解使用者意圖和生成相關回應至關重要。
*   長上下文模型能夠處理大量的背景資訊，從而克服傳統 LLM 在知識儲備和資訊時效性方面的限制。
*   Google 提供的 Gemini 1.5 Pro、2.0 Flashlight、2.0 Flash 和 2.5 Pro 等模型，都支援不同長度的上下文視窗，並在效能上有所提升。
*   2.5 Pro 模型在多項基準測試中，展現出超越其他強大模型的效能。

## 2. 詳細內容

會議首先解釋了上下文的定義，包括當前提示、使用者互動歷史、使用者提供的資料（如 PDF、影片）以及背景資訊（如維基百科頁面）。上下文視窗則衡量模型一次可以處理的 token 數量。

會議強調了傳統 LLM 依賴於訓練資料中的內建記憶，這導致了資訊過時和缺乏罕見或私有資訊等問題。長上下文模型通過允許使用者提供大量的背景資訊來解決這些問題，消除了篩選資訊的需求，並允許模型依賴最新的網路搜尋或企業知識。

會議介紹了 Google 提供的多個模型，包括 Gemini 1.5 Pro、2.0 Flashlight、2.0 Flash 和 2.5 Pro。除了 2.5 Pro 之外，其他模型都已正式發布（GA），而 2.5 Pro 目前處於預覽階段。Pro 模型通常提供 200 萬個 token 的上下文視窗，而 Flash 模型提供 100 萬個 token。2.5 Pro 目前提供 100 萬個 token，但即將擴展到 200 萬個 token。2.5 Pro 的 token 輸出限制為 65,000，遠高於之前的 8,000。Pro 模型的取樣速度通常為中等，而 Flash 模型則更快，Flashlight 模型最快。1.5 Pro 模型支援上下文快取，其他模型也將很快支援。

會議還展示了 2.5 Pro 模型與 O3 Mini High、GPT-4.5、DeepSeq R1 和 Sonnet 3.7 等強大模型的效能比較。在 MRCR、LOFT、LongBench V2 和 Fiction LiveBench 等基準測試中，2.5 Pro 的表現明顯優於其他模型。例如，在 Fiction LiveBench 測試中，2.5 Pro 的準確率從 GPT-4.5 的 64% 提高到約 90%。

會議鼓勵使用者在 AI Studio 中免費試用長上下文模型，並在 ai.dev 上獲取 API 金鑰。

## 3. 重要結論

長上下文模型是解決 LLM 挑戰的關鍵。Google 提供的 Gemini 系列模型，通過支援更長的上下文視窗和提高效能，為使用者提供了更強大的工具，可以生成更相關、更有幫助的回應。2.5 Pro 模型在多項基準測試中的優異表現，證明了長上下文模型在提高 LLM 效能方面的巨大潛力。
