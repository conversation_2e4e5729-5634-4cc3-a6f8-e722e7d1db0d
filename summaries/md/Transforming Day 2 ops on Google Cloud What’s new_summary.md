# Transforming Day 2 ops on Google Cloud What’s new
[會議影片連結](https://www.youtube.com/watch?v=jCI10s42has)
在 Google Cloud 上轉換 Day 2 運營的新功能

## 1. 核心觀點

本次會議主要介紹了 Google Cloud 在 Day 2 運營方面的新功能，重點圍繞統一的可觀測性平台和以應用為中心的管理。透過自動化的可觀測性、OpenTelemetry 原生路由以及新的雲應用管理平台，旨在簡化資料收集、路由和儲存，並提升資料的可用性。此外，Gemini Cloud Assist 的引入，為雲端運營帶來了生成式 AI 的能力，協助使用者設計、修改架構，並解決問題。

## 2. 詳細內容

**統一的可觀測性平台：**

*   **自動化可觀測性：** 針對流行的生成式 AI 工作負載（如 NVIDIA Triton 或 Jetstream）以及 Google Kubernetes Engine 工作負載，提供開箱即用的自動化可觀測性，使用 Prometheus 指標。
*   **LLM 可觀測性改進：** 針對 LLM 提供自動追蹤功能，幫助使用者了解代理程式內部的運作流程，並更好地排除問題。
*   **OpenTelemetry 原生路由：** 引入新的 OpenTelemetry 原生路由器，允許使用者發送 OpenTelemetry 原生資料，確保資料的攝取、儲存和可用性，同時避免與上游專案產生不一致的行為。
*   **統一資料存取：** 透過單一平台存取 metrics、trace 和 logs 等各種類型的資料，並提供 API 和 AI 輔助存取，例如事件註釋、儀表板關聯和儀表板版本控制。

**雲應用管理平台：**

*   **App Hub 資料模型：** 基於 App Hub 資料模型，提供應用程式監控，使用者無需額外配置遙測資料即可獲得黃金訊號和即時警報。
*   **成本管理：** 幫助使用者了解個別工作負載的總成本範圍。
*   **Cloud Hub：** 提供一個新的介面，展示應用程式的效能、成本以及需要採取哪些措施。

**Gemini Cloud Assist：**

*   **應用程式設計中心：** 提供人機互動介面，使用者可以探索、變更和修改架構，然後部署到環境中。
*   **問題調查：** 協助使用者使用 Gemini 解決問題，提供錯誤訊息或症狀，並給出解決方案建議，必要時可以轉交給支援團隊。

## 3. 重要結論

Google Cloud 在 Day 2 運營方面的新功能，旨在透過自動化、統一化和 AI 輔助，簡化雲端環境的管理和維護。統一的可觀測性平台和雲應用管理平台，提供了更全面的資料可見性和更高效的管理工具。Gemini Cloud Assist 的引入，則為雲端運營帶來了新的可能性，協助使用者更好地設計、部署和維護應用程式。Google Cloud 期待使用者提供回饋，以便持續改進這些功能。
