```
# Secure your cloud with Google Cloud’s security and compliance innovations
[會議影片連結](https://www.youtube.com/watch?v=0G82Mlxkp5s)
使用 Google Cloud 的安全性和合規性創新來保護您的雲端

## 1. 核心觀點

本次會議主要介紹 Google Cloud 在安全性與合規性方面的創新，展示如何利用 Google Cloud 的安全構建模塊來保護雲端工作負載，滿足基本安全要求，保護 PII 和其他敏感資料，以及建立安全防護網。會議透過實際應用案例，深入探討 Google Cloud 在硬體和軟體層面的安全措施，強調 Google 與客戶共同承擔安全責任的理念。

## 2. 詳細內容

### Google Cloud 的基礎設施安全基元

*   **Titanium 安全卸載處理器：** 負責加密操作，確保資料的機密性和完整性。包括網路卸載處理器，用於預設加密傳輸中的資料；以及儲存卸載處理器，用於加密儲存在 SSD 上的資料。
*   **Titan 晶片：** 作為系統的加密信任根和身份驗證，防止篡改的機器、卡片或周邊設備啟動。
*   **Axion 處理器：** Google 設計的 SOC，將安全技術直接整合到晶片中，並與業界合作夥伴共同開發 Calyptra 開放標準，以提高雲端基礎設施的安全性。

### 威脅緩解

Google Cloud 的安全措施旨在緩解各種威脅，包括：

*   **橫向移動攻擊：** Titanium、Titan 和 Calyptra 等硬體元件可協助緩解此類攻擊。
*   **持續存取和威脅：** Titan 和 Calyptra 可阻止持續存取和威脅，並協助偵測任何組態或不符之處。
*   **記憶體保護：** 機密運算與 CPU 和 GPU 合作夥伴合作，將技術建置到硬體本身，以保護應用程式的記憶體，並消除對 Hypervisor 的信任需求。

### 實際應用案例：交易分類帳應用程式

會議使用一個實際的交易分類帳應用程式，展示如何應用 Google Cloud 的安全功能。該應用程式使用 GKE 和 Dataproc 等多種 GCP 服務，進行資料儲存和轉換。

### 應用安全控制的步驟

1.  **基準控制：** 使用 Assured Workloads 設定邏輯控制，以達到一系列合規性目標。Assured Workloads 提供預先設定的最佳實務範本，協助滿足不同的合規性等級，並提供監控功能，以確保長期合規性。Assured Workloads Migration Assessment API 則可以評估現有工作負載遷移到合規區域的可行性。
2.  **資料保護：** 使用機密運算來保護敏感資料。機密運算提供隔離的運算環境，以處理敏感資料。可以透過 UI、Terraform 或 G Cloud 等方式啟用機密運算。
3.  **安全防護網：** 建立安全防護網以強制執行安全標準、管理成本和滿足可靠性目標。可以使用自訂組織政策來限制 VM 實例的大小、允許的連接埠和物件儲存的版本控制。

## 3. 重要結論

Google Cloud 透過硬體創新、邏輯控制和安全防護網，提供全面的安全解決方案，協助客戶保護雲端工作負載和資料。會議強調 Google 與客戶共同承擔安全責任，並鼓勵客戶嘗試 Google Cloud 的安全功能，以提升雲端環境的安全性。
```