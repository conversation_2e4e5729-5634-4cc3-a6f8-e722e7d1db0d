# Gemini in BigQuery Your AI assistant for transforming your data workflows

[會議影片連結](https://www.youtube.com/watch?v=n1sFLL7ccTM)
Gemini 在 BigQuery 中，您轉變數據工作流程的 AI 助手

## 1. 核心觀點

本次會議主要介紹了 Google 的 Gemini 與 BigQuery 的整合，以及如何利用 AI 來轉變數據工作流程。核心觀點包括：

*   **AI 的價值取決於 AI-ready 的數據：** 許多組織在 AI 方面投入大量資源，但由於技能差距、數據訪問限制等問題，未能充分發揮其潛力。Gemini 與 BigQuery 的整合旨在幫助客戶釋放數據價值。
*   **Gemini 在 BigQuery 中的廣泛應用：** Gemini 涵蓋數據管道構建、數據發現與分析、查詢編寫效率提升，甚至簡化運營等各個方面。
*   **數據準備的簡化：** 利用自然語言和低代碼方式構建數據管道，並提供數據質量規則和轉換建議，以及數據和架構 SKU 的監控。
*   **自動化元數據生成：** 加速元數據生成過程，使機器和人員能夠更有效地發現和使用數據。
*   **利用 AI 加速 Spark 工作流程：** 支援 Apache Spark 代碼生成，並整合 Gemini Cloud Assist 進行問題排查。
*   **核心功能免費提供：** SQL Code Assist、Python Code Assist、Data Canvas 和 Data Preparation 等核心功能免費提供給所有 BigQuery 客戶。
*   **Agentic 體驗的未來：** 結合 Gemini 模型、BigQuery 知識引擎和 AI 治理功能，提供安全、準確且符合政策的 AI 協助。
*   **BigQuery AI 查詢引擎：** 允許使用者將熟悉的 SQL 與自然語言提示混合使用，簡化複雜的分析。

## 2. 詳細內容

*   **Gemini 與 BigQuery 的整合解決了企業在 AI 應用中面臨的挑戰：** 技能差距、數據訪問限制、緩慢的數據消耗以及工具特定的知識鎖定了數據訪問，阻礙了 AI 計劃的實施。
*   **數據準備功能：** 透過低代碼、自然語言優先的體驗來構建數據管道，並監控數據和架構的 SKU，以解決相關問題。
*   **SQL 翻譯的遷移服務：** 支援 SQL 翻譯的遷移服務已正式推出，並支援批次模式和 API，方便合作夥伴整合到他們的工具中。
*   **自動化元數據生成：** 加速元數據生成過程，確保機器和人員能夠更有效地發現和使用數據。
*   **知識引擎模組：** 透過互動式實體關係視覺化，更容易理解不同數據實體之間的關聯。
*   **Data Canvas：** BigQuery Studio 中的圖形化工作流程工具，允許使用者透過自然語言提示來查找、轉換、分析和視覺化數據。現在引入了輔助聊天功能，並支援在 Data Canvas 節點中嵌入 Python Notebook。
*   **SQL 體驗的改進：** 引入自然語言表達式，允許使用者用業務邏輯編寫 SQL 查詢，並自動轉換為 SQL 程式碼。
*   **加速 Spark 工作流程：** 支援 Apache Spark 代碼生成，並整合 Gemini Cloud Assist 進行問題排查。
*   **VM02 的案例分享：** VM02 透過 BigQuery 和 Gemini 解決了數據孤島問題，並利用自然語言生成程式碼，加速數據洞察。他們使用 Data Preparation 清理混亂的數據，並使用 Data Canvas 進行數據視覺化和分析。
*   **General Mills 的案例分享：** General Mills 利用 AI on BigQuery 和 Gemini Power 加速了其分析平台。他們將工程的複雜性與 AI 的樂趣結合起來，並透過 Gemini 整合簡化了複雜的工程解決方案。他們還建立了一個智慧數據層，利用 AI 生成客製化的元數據，並定義表之間的關係。

## 3. 重要結論

Gemini 與 BigQuery 的整合為企業提供了一個強大的 AI 驅動的數據分析平台，可以簡化數據準備、加速數據洞察、並提高數據工程效率。透過自然語言介面、自動化功能和 AI 輔助，Gemini 降低了數據分析的門檻，使更多人能夠參與到 AI 驅動的決策中。VM02 和 General Mills 的案例展示了 Gemini 和 BigQuery 在實際應用中的價值和潛力。
