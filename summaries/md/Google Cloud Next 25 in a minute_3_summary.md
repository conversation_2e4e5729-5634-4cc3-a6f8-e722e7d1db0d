# Google Cloud Next 25 in a minute_3
[會議影片連結]()
Google Cloud Next 25 一分鐘精華_3

## 1. 核心觀點

本次演講主要介紹 Google Cloud 如何轉型軟體開發，並著重於新一代 Agentic 應用程式的建構能力。核心觀點包括：

*   **Agent 開發工具包 (ADK)、Agent 引擎和 Agent 空間：** 這些工具共同協助開發者建構、運行和管理 Agent。
*   **Gemini 模型的力量：** Gemini 模型提供影片理解、原生圖像生成以及透過 Google 搜尋進行真實資訊接地的能力。
*   **Agent 間的互連互通：** 透過 Agent to Agent (A to A) 協定，簡化不同生態系統或供應商的 Agent 之間的發現和連接。
*   **開發者工具的整合：** Google 致力於讓開發者使用他們喜歡的工具和模型，例如 Visual Studio Code、Tab9、Cognition 和 Ater。
*   **Gemini Code Assist 的新功能：** 2.5 Pro 版本現已推出，Data Science Agent 也即將整合到 BigQuery notebooks 中。
*   **軟體開發的未來願景：** Google 預告了今年稍晚將推出的多項功能，旨在加速和簡化軟體開發流程。

## 2. 詳細內容

演講者強調 Google Cloud 正在透過以下方式轉型軟體開發：

*   **Agent 開發工具：** 介紹了 Agent 開發工具包 (ADK)，該工具包現已公開發布，以及 Agent 引擎，它簡化了基於任何 Agent 框架構建的 Agent 的部署和運行。
*   **Agent 間協定：** 為了簡化 Agent 之間的連接，推出了 Agent to Agent (A to A) 協定。該協定旨在標準化 Agent 的發現和連接，特別是當這些 Agent 來自不同的生態系統或供應商時。
*   **開發者工具整合：** 強調 Google 致力於讓開發者使用他們喜歡的工具和模型，包括 Visual Studio Code、Tab9、Cognition 和 Ater。
*   **Gemini 模型整合：** 介紹了 Gemini 模型在影片理解、原生圖像生成和真實資訊接地方面的能力。
*   **Data Science Agent：** 預告了 Data Science Agent 即將整合到 BigQuery notebooks 中，協助將原始資料轉化為資料應用程式。
*   **軟體開發的未來：** 預告了今年稍晚將推出的多項功能，旨在加速和簡化軟體開發流程。

## 3. 重要結論

Google Cloud 正在透過提供強大的 Agent 開發工具、簡化 Agent 間的互連互通、整合開發者工具、利用 Gemini 模型的力量以及不斷創新，積極轉型軟體開發。Google 鼓勵開發者利用這些工具和技術，共同建構新一代的應用程式。
