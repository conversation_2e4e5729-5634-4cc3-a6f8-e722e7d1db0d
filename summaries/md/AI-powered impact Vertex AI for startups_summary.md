# AI-powered impact Vertex AI for startups

[會議影片連結](https://www.youtube.com/watch?v=-xAsWh-zcoM)
AI 驅動的 Vertex AI 對新創公司的影響

## 1. 核心觀點

本次會議邀請了 Prompt AI、NoBroker.com 和 Resemble AI 三家公司的創辦人，分享他們如何利用 Google Cloud 的 Vertex AI 來構建解決方案，並探討了 Vertex AI 在新創公司中的應用、經驗和未來發展。核心觀點包括：

*   **Vertex AI 的可擴展性：** Vertex AI 提供了高度的可擴展性，能夠快速擴展運算資源，滿足新創公司快速成長的需求。
*   **Vertex AI 的多樣化模型：** Vertex AI 提供了多種預訓練模型，方便新創公司快速構建應用，同時也支持自定義模型的訓練和部署。
*   **Vertex AI 的整合性：** Vertex AI 與 Google Cloud 的其他服務高度整合，方便新創公司構建完整的解決方案。
*   **AI 代理的潛力：** AI 代理在自動化任務、改善客戶服務和提高生產力方面具有巨大潛力，新創公司應積極探索其應用。
*   **負責任的 AI：** 在生成式 AI 快速發展的背景下，確保 AI 的安全和負責任使用至關重要，需要技術和政策共同努力。

## 2. 詳細內容

*   **Vertex AI 的選擇原因：**
    *   Resemble AI 最初使用 ML Engine（現為 Vertex AI）是因為其可擴展性，能夠快速訓練模型。
    *   NoBroker.com 發現 Vertex AI 提供的預訓練模型可以簡化許多任務，節省開發時間和成本。
    *   Prompt AI 認為 Vertex AI 在視覺 AI 領域非常有用，因為他們需要訓練和部署許多不同的模型來應對不同的應用場景。

*   **Vertex AI 的經驗教訓：**
    *   NoBroker.com 早期使用機器學習模型來識別房地產圖像中的物件，但現在可以使用 Vertex AI 提供的 SaaS 模型，更加方便和高效。
    *   Resemble AI 發現 Vertex AI 可以很好地整合到 Google Cloud 的其他解決方案中，方便團隊進行原型設計、實驗和部署。
    *   Prompt AI 發現 Vertex AI 可以幫助他們快速構建解決方案，並快速迭代和部署到市場。

*   **Vertex AI 的具體應用：**
    *   Resemble AI 使用 Vertex AI 來檢測深度偽造圖像、音訊和影片，並自動更新模型以應對新的生成式 AI 模型。
    *   NoBroker.com 使用 Gemini 和 Vertex AI 來估算搬家所需的空間，並自動處理租賃協議。
    *   Prompt AI 使用 Vertex AI 來構建視覺 AI 應用，例如識別寵物並監控其行為。

*   **AI 代理的應用：**
    *   NoBroker.com 使用 AI 代理來改善客戶服務，例如提供客戶歷史記錄和建議，並自動執行一些重複性任務。
    *   Resemble AI 認為每個公司都應該有一個專門的團隊來探索 AI 代理的應用，以改善內部工作流程和提高生產力。

*   **對未來的展望：**
    *   Prompt AI 希望利用 AI 來監控環境，並根據使用者的意圖來提供個性化的服務。
    *   NoBroker.com 希望將其解決方案推廣到全球，並利用 AI 來自動化更多的任務。
    *   Resemble AI 關注生成式 AI 的安全和負責任使用，並希望開發技術來檢測和防止惡意使用。

## 3. 重要結論

Vertex AI 為新創公司提供了強大的 AI 開發平台，可以幫助他們快速構建解決方案、提高生產力並實現創新。AI 代理在自動化任務和改善客戶服務方面具有巨大潛力，新創公司應積極探索其應用。同時，確保 AI 的安全和負責任使用至關重要，需要技術和政策共同努力。
