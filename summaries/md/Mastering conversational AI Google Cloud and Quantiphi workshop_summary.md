# Mastering conversational AI Google Cloud and Quantiphi workshop
[會議影片連結](https://www.youtube.com/watch?v=Ha9h3eNqWzo)
Mastering conversational AI Google Cloud and Quantiphi workshop

## 1. 核心觀點

本次會議主要探討如何運用對話式 AI 和生成式 AI，在 Google Cloud 的基礎上，結合 Quantiphi 的實務經驗，為企業創造實際的商業價值。核心觀點包括：

*   **合作夥伴的重要性：** Google Cloud 合作夥伴在對話式 AI 解決方案的成功部署中扮演關鍵角色，他們的經驗和專業知識至關重要。
*   **生成式 AI 的成熟度：** 生成式 AI 技術已趨於成熟，可以實際應用於商業場景中。
*   **Google Cloud 的優勢：** Google Cloud 提供易於入門的託管產品方法，以及從產品到雲端基礎設施再到模型的垂直整合，降低了企業導入對話式 AI 的門檻。
*   **以人為本的開發方法：** 尋找並學習優秀的人工客服的經驗，將其轉化為生成式 AI 代理的知識和技能。
*   **數據驅動的決策：** 利用數據分析來確定自動化的優先順序，並持續監控和優化解決方案的效能。

## 2. 詳細內容

會議首先強調了商業價值的重要性，指出構建對話式 AI 代理的目的是解決實際問題。Quantiphi 的 Ishan 介紹了實現商業價值的三個步驟：

1.  **定義成功：** 明確期望的成果，通常包括改善客戶體驗、賦能客服人員和降低成本。
2.  **評估差距：** 了解現狀與目標之間的差距，例如缺乏關鍵績效指標（KPI）或基礎設施。
3.  **制定路線圖和商業案例：** 根據評估結果，制定可執行的路線圖，並建立充分的商業理由。

Ishan 強調，許多企業未能將對話式 AI 擴展到試點項目之外，原因在於未能正確執行上述步驟。他建議企業應關注可衡量的 KPI，例如自助服務率、首次通話解決率和平均處理時間。

Quantiphi 的 Gaurav 則分享了他們在對話式 AI 部署方面的經驗，強調與 Google 緊密合作，並將業務優先事項、目標和組織文化納入考量。Gaurav 介紹了 Quantiphi 的加速器套件 collective.cx，該套件旨在加速對話式 AI 解決方案的設計、開發和管理。他還強調了變更管理的重要性，以確保使用者和客服人員能夠順利採用新的 AI 解決方案。

Gaurav 分享了幾個客戶案例，展示了對話式 AI 如何為電信供應商、醫療保健提供者、網路服務供應商和銀行帶來顯著的商業效益，包括降低成本、提高客服人員生產力和改善客戶體驗。

Google Cloud 的 Pak 介紹了 Conversational Agents 的新功能，包括：

*   **低程式碼環境：** 方便業務使用者構建對話式 AI 代理。
*   **混合代理支援：** 支援傳統的確定性代理和新的生成式代理。
*   **生成式輔助提示：** 利用 Gemini 協助建立初始提示。
*   **託管 RAG 工具：** 簡化生成式代理的數據基礎。
*   **連接器：** 連接 CRM 系統和訂購系統等，實現有用的功能。
*   **與客戶端整合：** 與網站、電話和聊天客戶端整合，無需編寫自訂程式碼。
*   **與 BigQuery 和 Insights 整合：** 了解客戶與對話式 AI 代理的互動。
*   **整合評估：** 改善代理的效能。
*   **確定性動作和程式碼工具：** 將確定性整合到生成式代理中。

Pak 強調，尋找優秀的人工客服是構建成功的生成式 AI 代理的關鍵。他提出了四個步驟：

1.  **提示 Gemini：** 利用 Gemini 的合成訓練數據來建立初始提示。
2.  **以數據為基礎：** 使用數據儲存功能將代理與數據連接。
3.  **新增工具：** 使用連接器將代理與外部系統連接。
4.  **強化代理：** 使用條件動作、程式碼區塊和評估來提高代理的可靠性。

Pak 展示了如何使用這些功能在幾分鐘內構建一個代理。

## 3. 重要結論

本次會議強調了 Google Cloud 合作夥伴在對話式 AI 部署中的重要性，並展示了生成式 AI 技術的成熟度和 Google Cloud 平台的優勢。透過結合 Quantiphi 的實務經驗和 Google Cloud 的創新功能，企業可以有效地利用對話式 AI 和生成式 AI 來創造實際的商業價值。會議還強調了以人為本的開發方法和數據驅動的決策，以確保解決方案的成功採用和持續優化。
