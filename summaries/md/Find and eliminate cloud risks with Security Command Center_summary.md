# Find and eliminate cloud risks with Security Command Center
[會議影片連結](https://www.youtube.com/watch?v=uEqi96osNRc)
使用 Security Command Center 尋找並消除雲端風險

## 1. 核心觀點

本次會議主要介紹 Google Cloud 的 Security Command Center (SCC) 如何協助企業快速建立雲端安全基礎、識別並優先排序風險，以及安全地採用 AI 技術。講者分享了 Yahoo 作為 SCC 用戶的經驗，並展示了 SCC 的新功能，包括合規性管理、無代理程式漏洞掃描、威脅偵測、虛擬紅隊模擬和 AI 安全保護等。

## 2. 詳細內容

*   **Yahoo 的雲端安全轉型：** Yahoo 的資訊安全長 Sean Zadig 分享了 Yahoo 將服務遷移到 Google Cloud 的經驗，以及如何利用 SCC 來保護其龐大的網路資產。他強調 SCC 的企業威脅偵測功能，能有效識別 Google 平台上的惡意活動，並協助 Yahoo 更快、更安全地推出新產品。

*   **建立雲端安全基礎：** SCC 提供一系列功能，協助企業快速建立雲端安全基礎。這些功能包括：
    *   **合規性管理：** 允許企業導入預防性和偵測性安全態勢，並使用稽核管理員進行證據式報告。
    *   **無代理程式漏洞掃描：** 無需部署代理程式即可識別多雲環境中的 CVE。
    *   **威脅偵測：** 內建於 Compute Engine 和 GKE 等 Google Cloud 服務中，可自動建立案例並將相關事件分組，提供更高精確度的訊號。
    *   **Gemini AI 輔助：** 利用 Gemini AI 生成策略，簡化安全策略的編寫。

*   **識別並優先排序風險：** SCC 的虛擬紅隊模擬功能，透過建立數位分身來模擬攻擊路徑，識別潛在的風險。它能找出「毒性組合」和「扼制點」，協助企業優先處理最重要的安全問題。SCC 還提供風險報告，提供環境的深入分析，並識別可識別的毒性組合和扼制點。

*   **安全地採用 AI 技術：** SCC 提供 AI 保護功能，協助企業安全地採用 AI 技術。這些功能包括：
    *   **AI 資產探索：** 識別環境中的 AI 資產，例如資料模型和訓練模型。
    *   **風險評估：** 評估與 AI 工作負載相關的風險。
    *   **AI 保護：** 採取行動來保護 AI 資產。
    *   **Model Armor：** 檢查提示和回應，並應用安全策略，以確保 AI 模型的安全使用。

*   **風險保護計畫：** Google Cloud 擴展了其風險保護計畫，新增了 Beasley 和 Chubb 作為合作夥伴，並擴大了與 Munich Re 的關係。這些合作夥伴都承諾提供 AI 保險，透過使用 SCC 降低網路安全保險成本。

## 3. 重要結論

Security Command Center 提供全面的雲端安全解決方案，協助企業快速建立安全基礎、識別並優先排序風險，以及安全地採用 AI 技術。透過 Yahoo 的案例分享和 SCC 的功能展示，本次會議強調了 SCC 在保護雲端環境方面的重要性，並鼓勵企業利用 SCC 的各種功能來提升其安全態勢。
