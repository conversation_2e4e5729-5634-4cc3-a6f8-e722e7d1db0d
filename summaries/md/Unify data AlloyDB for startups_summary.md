# Unify data AlloyDB for startups
[會議影片連結](https://www.youtube.com/watch?v=E0bPhxrv_JY)
統一資料 AlloyDB 給新創公司

## 1. 核心觀點

本次會議主要討論如何將不同類型的資料，例如關聯式資料和向量嵌入，整合到單一的 AlloyDB 資料庫中。以 Neuro 這家自動駕駛新創公司為例，說明如何協助他們整合資料並打破資料孤島。核心觀點包括：

*   將關聯式資料和向量嵌入整合到單一資料庫可以簡化應用程式複雜性，提高效能，並降低管理成本。
*   Scan indexes 的引入大幅縮短了索引重建時間，使得資料整合更加可行。
*   AlloyDB 在處理大量資料和高併發使用者方面表現出色，能夠實現亞秒級延遲。

## 2. 詳細內容

Neuro 是一家自動駕駛公司，利用端到端的人工智慧駕駛技術，並將其應用於各種車輛。他們從聯網車輛收集大量資料，並進行大規模模擬以產生資料和元資料。然後，他們使用這些資料和元資料，透過機器學習模型產生洞見。

在與 Neuro 合作初期，他們的資料是孤立的，關聯式資料儲存在一個 AlloyDB 實例中，而向量嵌入則儲存在另一個 AlloyDB 實例中。這種架構導致了多個問題：

*   應用程式複雜性增加，因為資料分散在不同的資料庫中。
*   效能下降，延遲增加，影響了即時資料分析。
*   開發過程複雜，需要不斷在兩個資料庫之間進行連接，導致開發週期變長。
*   管理成本增加，因為需要維護、修補和保護兩個資料庫。

為了解決這些問題，Google 協助 Neuro 將資料整合到單一的 AlloyDB 資料庫中。Scan indexes 的引入是實現這一目標的關鍵因素。與之前的 HNSW 索引相比，Scan indexes 的索引重建速度更快，這使得在單一資料庫中整合大量資料成為可能。

資料整合後，Neuro 獲得了以下優勢：

*   打破了資料孤島，實現了更有效率的資料分析和洞見。
*   簡化了整體管理和開發流程。
*   提高了效能。
*   優化了成本。

為了評估整合後的效能，Google 在 N-C2 機器上進行了測試。測試使用了兩種機器類型：16 vCPU 和 128 GB 記憶體，以及 32 vCPU 和 256 GB 記憶體。結果顯示，對於 10,000 和 5,000 個並發使用者，使用更高規格的機器類型，每秒交易次數幾乎提高了三倍。此外，延遲的 P95 和 P50 數值也非常出色，均在毫秒級別。

測試還發現，按季度劃分資料比按月劃分資料能提供更低的延遲。使用 32 vCPU 和 256 GB 記憶體的機器類型，每秒交易次數顯著提高。即使使用者數量從 5,000 增加到 10,000，延遲仍然保持在毫秒級別。

## 3. 重要結論

AlloyDB 是一個優秀的產品，可以滿足各種資料需求。透過將關聯式資料和向量資料庫整合到單一實例中，可以實現亞秒級延遲，提高效能，並降低管理成本。Scan indexes 的引入是實現資料整合的關鍵因素。AlloyDB 適用於需要處理大量資料和高併發使用者的應用程式，例如自動駕駛、金融服務和電子商務。
