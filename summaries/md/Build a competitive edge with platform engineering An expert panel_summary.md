Build a competitive edge with platform engineering An expert panel
[會議影片連結](https://www.youtube.com/watch?v=68PN_F-7cwA)
建立平台工程的競爭優勢：專家小組

## 1. 核心觀點

本次會議主要探討平台工程的核心概念、實踐經驗以及如何透過平台工程建立競爭優勢。與會專家分享了各自公司在平台工程方面的經驗，包括成功的案例、遇到的挑戰以及未來的發展方向。核心觀點包括：

*   **平台工程的定義：** 平台工程旨在降低開發人員的認知負擔，提供自助服務，並將底層工具、治理和標準抽象化，使開發人員能夠更專注於程式碼編寫和創新。
*   **平台工程的價值：** 透過解決常見問題、提供可重用的解決方案，平台工程可以提高開發人員的生產力、加速軟體交付、提升安全性，並最終為企業帶來業務價值。
*   **平台工程的挑戰：** 平台工程的實施需要組織文化的轉變、產品管理方法的引入以及對開發人員體驗的關注。此外，衡量平台工程的成功與否也是一個挑戰。
*   **平台工程的未來：** 平台工程將繼續發展，並與雲原生、可觀測性、AI 等新技術相結合，為企業提供更強大的競爭優勢。

## 2. 詳細內容

*   **平台工程的定義與目標：**
    *   Spotify：確保開發人員盡可能高效，服務對象不僅限於工程師，還包括產品經理、設計師、資料科學家等。
    *   HCA Healthcare：降低開發人員的認知負擔，透過自助服務實現更好的程式碼品質。從「左移」轉向「下移」，將核心能力、治理和標準抽象化到平台工程中。
    *   Google：找到工程師面臨的常見問題，並以可重用、可靠的方式解決，釋放工程團隊的創造力。
    *   Sabre：解決與規模相關的常見問題，使軟體創意能夠有效地擴展為成功的業務。

*   **平台工程的實踐經驗：**
    *   **Spotify：** 最初專注於基礎設施，後來轉向關注開發人員體驗。引入產品經理、資料科學家和設計師，並採用變更管理技術。強調早期開始衡量和定義成功指標的重要性。
    *   **HCA Healthcare：** 從基礎設施即程式碼開始，但發現僅僅啟用基礎設施即程式碼是不夠的，需要關注開發人員體驗，建立參考架構模式，並透過自助服務啟用這些模式。
    *   **Google：** 強調共享理解的重要性，避免過早優化。關注品質屬性（如安全性、可靠性），並將其與業務需求聯繫起來。
    *   **Sabre：** 從傳統的 COE 模型轉向平台工程，解決了開發人員需要成為雲端工程師的問題，使他們能夠專注於交付軟體。

*   **平台工程的挑戰與教訓：**
    *   從基礎設施轉向關注開發人員體驗的轉變。
    *   建立有效的衡量和成功指標。
    *   改變營運模式，平台團隊需要負責維護平台，不能將使用者留在困境中。
    *   確保共享理解，避免過早採用技術解決方案。

*   **平台工程的效益：**
    *   **HCA Healthcare：** 將資料科學家存取資料的時間從 4-6 週縮短到 30 分鐘。
    *   **Google：** 透過平台工程工作，抵消了 400 個 FTE（全職等效人力），提高了安全性，並加速了軟體遷移和升級。
    *   **Spotify：** 透過 Backstage 平台，實現了成本節約，並提高了開發人員的生產力。

*   **平台工程的平衡：**
    *   在速度、成本和品質之間取得平衡。
    *   關注品質屬性，並將其與業務風險聯繫起來。
    *   透過平台工程的成熟，逐步消除權衡取捨。
    *   建立護欄，確保快速移動的同時不會破壞安全性。

*   **平台工程與開發人員滿意度：**
    *   平台工程可以提高開發人員的滿意度，因為它可以讓他們更專注於開發工作。
    *   衡量開發人員的滿意度，並將其作為改進平台工程工具的訊號。
    *   建立信任的基礎，並不斷改進回饋迴路。

*   **開發人員入口網站（Developer Portal）：**
    *   Backstage 是一個流行的開源開發人員入口網站，可以幫助開發人員減少認知負擔，並提供一致的體驗。
    *   開發人員入口網站可以作為基礎設施的介面，並解決雲端採用和加速創新的三個基本挑戰：使用哪些資源、如何安全有效地組合它們以及如何估算成本。
    *   鼓勵社群協作，建立外掛程式生態系統，使開發人員能夠貢獻自己的解決方案。

*   **購買、構建或使用開源元件：**
    *   沒有一個通用的答案，需要根據具體的使用案例、需求和環境來決定。
    *   在受監管的環境中，可能需要從頭開始構建某些元件，以確保符合法規要求。
    *   考慮團隊的容量和能力，以及是否需要 SaaS 解決方案來簡化管理和維護。

*   **獲得管理層的支持：**
    *   找到與業務相關的品質屬性（如安全性、效率），並展示平台工程如何解決這些問題。
    *   使用定性和定量指標來衡量平台工程的價值。
    *   與有經驗的供應商合作，獲得參考客戶的支援。

*   **產品所有權：**
    *   平台工程團隊需要產品負責人，負責定義平台的功能、收集開發人員的回饋，並優先處理需求。
    *   確保所有工程師都可以看到相同的資料，並了解生態系統的狀態。
    *   鼓勵共享責任，並根據共享命運的程度來調整開發人員的責任。

## 3. 重要結論

平台工程是一種強大的方法，可以提高開發人員的生產力、加速軟體交付、提升安全性，並最終為企業帶來業務價值。然而，平台工程的實施需要組織文化的轉變、產品管理方法的引入以及對開發人員體驗的關注。透過仔細規劃、執行和衡量，企業可以成功地利用平台工程建立競爭優勢。
