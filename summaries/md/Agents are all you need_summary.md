# Agents are all you need
[會議影片連結](https://www.youtube.com/watch?v=Fbq2ty_3kDY)
代理人就是你所需要的一切

## 1. 核心觀點

本次演講主要探討了 AI 代理（Agent）的概念、組成部分、生態系統以及未來發展趨勢。核心觀點包括：

*   **AI 代理的定義與重要性：** AI 代理是一種可以被要求完成目標，並以一定程度的自主性實現該目標的實體。
*   **AI 代理的組成部分：** 包括模型（尤其是具備思考能力的模型）、工具（用於擴展知識、能力和執行動作）、記憶（短期和長期）以及感知能力。
*   **AI 代理生態系統的現狀：** 涵蓋 AI 代理開發平台、工具、語音 AI 代理、多代理協作等領域。
*   **AI 代理的應用場景：** 包括編碼、研究、客戶支援、螢幕共享、銷售、招聘、教育、金融和個人生產力等。
*   **AI 代理的局限性與挑戰：** 包括邏輯迴圈、記憶限制、規劃能力不足、成本和延遲問題、缺乏標準協定、安全隱憂以及倫理考量。
*   **AI 代理的未來發展趨勢：** 包括代理將變得更加複雜、AI 協同科學家、多代理系統、機器人技術的發展以及高情商代理的出現。

## 2. 詳細內容

*   **AI 代理的定義與演進：** 從聊天機器人到協作助手，再到具備護欄的代理，最終目標是實現特定用例的完全自主代理。
*   **AI 代理的組成部分詳解：**
    *   **模型：** 強調具備思考能力的模型的重要性，例如 Gemini 2.5 Pro 和即將推出的 Gemini 2.5 Flash，後者允許設定思考預算。
    *   **工具：** 分為知識增強、能力增強和執行動作三種類型，例如網路搜尋、程式碼執行和電腦控制工具。
    *   **記憶：** 分為短期記憶（透過上下文視窗或 RAG 實現）和長期記憶（用於跨會話記憶）。
    *   **感知：** 透過感測器使代理能夠感知環境。
*   **AI 代理生態系統的現狀與趨勢：**
    *   **AI 代理開發平台：** 例如 Adept 和 E2B。
    *   **代理工具：** 包括身份驗證、支付、網路搜尋、資料管理和記憶工具。
    *   **語音 AI 代理：** 在客戶支援等領域具有快速變現的潛力。
    *   **多代理協作：** 例如 CrewAI，是下一個發展前沿。
*   **AI 代理的應用案例：**
    *   **編碼代理：** 使用 Gemini 應用程式建立遊戲，展示了代理在背景改善方面的思考能力。
    *   **研究代理：** 使用 Deep Research 進行深入分析，並生成 Google 文件和表格。
    *   **瀏覽器控制代理：** 自動尋找公司網站和聯絡資訊，可用於銷售。
    *   **遊戲代理：** 使用 Gemini 實時 API 提供遊戲建議。
*   **Google 提供的 AI 代理開發工具：**
    *   **模型：** Gemini 2.5 Pro 和即將推出的 Gemini 2.5 Flash。
    *   **工具：** 網路搜尋和程式碼執行。
    *   **功能呼叫：** 支援單一、平行和組合功能呼叫。
    *   **API：** 常規聊天 API 和實時 API。
    *   **Vertex 代理開發套件：** 協助建立多代理協作。
    *   **MCP 支援：** 透過生成式 AI SDK 支援 MCP。
    *   **代理間協定：** 允許不同公司建立的代理進行互動。
*   **AI 代理的局限性與挑戰：**
    *   **邏輯迴圈：** 代理可能陷入自我強化的邏輯迴圈。
    *   **記憶限制：** 長期記憶的存取和回憶仍然是挑戰。
    *   **規劃能力：** 需要改進規劃的效率和有效性。
    *   **成本和延遲：** 需要透過微調來降低成本和延遲。
    *   **缺乏標準協定：** 代理之間的互通性存在問題。
    *   **安全隱憂：** 包括資料操縱、市場操縱、侵犯版權和隱私。
    *   **倫理考量：** 包括偏見、目標不一致和責任歸屬。

## 3. 重要結論

AI 代理正在快速發展，並在各個領域展現出巨大的潛力。儘管存在許多挑戰和限制，但隨著技術的進步和標準的建立，AI 代理將在未來扮演越來越重要的角色，並對社會和經濟產生深遠的影響。
