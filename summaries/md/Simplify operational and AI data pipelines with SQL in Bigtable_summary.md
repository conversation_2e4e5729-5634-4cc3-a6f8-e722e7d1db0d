Simplify operational and AI data pipelines with SQL in Bigtable
[會議影片連結](https://www.youtube.com/watch?v=N8MAvo5Ivug)
使用 SQL 簡化 Bigtable 中的運營和 AI 數據管道

## 1. 核心觀點

本次會議主要探討如何利用 Bigtable 及其新增功能，特別是 SQL 支援，來簡化運營和 AI 數據管道，實現即時分析。核心觀點包括：

*   **Bigtable 適用於即時分析：** 儘管通常被認為是鍵值儲存，但 Bigtable 在即時分析方面有著廣泛的應用，例如 ML 特徵儲存、生成式 AI 的上下文儲存以及應用內分析。
*   **關鍵能力：** 成功的即時分析解決方案需要快速攝取大量數據、轉換不同形狀的數據，以及支援多種存取模式（點查詢和批次讀取）。
*   **SQL 支援：** Bigtable 增加了對 SQL 的支援，允許使用者在非結構化數據上執行轉換和查詢，從而簡化了 ELT 流程。
*   **DataBoost：** DataBoost 允許在不影響操作延遲的情況下進行大規模批次讀取，從而消除了將數據匯出到其他系統進行 ML 模型訓練的需求。
*   **持續物化視圖：** 持續物化視圖允許建立同一數據的不同視圖，適用於大規模運營報告、廣告技術和媒體、零售和支付平台以及應用內指標和儀表板。
*   **與 BigQuery 的整合：** Bigtable 可以與 BigQuery 整合，用於反向 ETL，將批次計算的特徵快速提供給即時應用程式。

## 2. 詳細內容

*   **AI 和數據處理：** AI 的發展需要處理更多、更多樣化的數據。數據品質至關重要，任何延遲都會影響決策品質。選擇合適的資料庫可以幫助解決這些問題。
*   **Google 的資料庫產品：** Google 提供多種資料庫產品，包括記憶體快取、關係資料庫和 Spanner。Bigtable 專注於 NoSQL 解決方案，並提供與 Apache Cassandra 和 HBase 的相容介面。
*   **Bigtable 的常見用例：** 除了傳統的鍵值儲存用例（例如產品目錄、社交媒體 feed、DNS 服務），Bigtable 也廣泛用於即時分析。
*   **分散式計數器：** 分散式計數器是 Bigtable 的一項新功能，可優化計數器更新，提高吞吐量並降低延遲。這對於時間序列報告和應用程式中的點擊計數等用例非常有用。
*   **SQL 支援的轉換：** Bigtable 擴展了 SQL 功能，以支援寬欄位表的操作。這允許使用者查詢 JSON 數據、執行最近鄰搜尋以及執行時間點查詢。
*   **邏輯視圖：** 邏輯視圖允許使用者儲存複雜的查詢，並將 JSON 數據呈現為關係資料庫中的平面表。
*   **DataBoost 的優勢：** DataBoost 允許使用者在 Bigtable 中執行大規模批次讀取，而不會影響操作延遲。這消除了將數據匯出到 GCS 或 BigQuery 進行 ML 模型訓練的需求，從而降低了成本、解決了數據治理問題並避免了訓練服務偏差。
*   **反向 ETL：** 反向 ETL 允許使用者將 BigQuery 中的批次計算特徵匯入到 Bigtable 中，以便即時應用程式可以快速存取這些特徵。
*   **Bigtable 的未來改進：** Bigtable 將增加對 GROUP BY 支援、結構化鍵和樞紐轉換的支援。
*   **持續物化視圖的用例：** 持續物化視圖可用於大規模運營報告、廣告技術和媒體、零售和支付平台以及應用內指標和儀表板。
*   **Zeotap 的案例研究：** Zeotap 是一家客戶數據平台，使用 Bigtable 和 BigQuery 構建即時數據平台。他們使用 Bigtable 進行 ID 縫合、即時客戶 360 度檢視和數據 API。Bigtable 幫助他們降低了 TCO、提高了效率並簡化了 AI 管道。

## 3. 重要結論

Bigtable 透過新增 SQL 支援、DataBoost 和持續物化視圖等功能，已成為簡化運營和 AI 數據管道的強大工具。它能夠快速攝取、轉換和查詢大量數據，並支援多種存取模式，使其成為即時分析的理想選擇。Bigtable 與 BigQuery 的整合進一步增強了其功能，使其能夠處理批次和即時工作負載。Zeotap 的案例研究證明了 Bigtable 在實際應用中的價值，展示了其如何幫助企業降低成本、提高效率並加速 AI 模型的部署。
