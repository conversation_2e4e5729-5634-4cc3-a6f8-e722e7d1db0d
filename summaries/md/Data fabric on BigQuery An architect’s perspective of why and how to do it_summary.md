# Data fabric on BigQuery An architect’s perspective of why and how to do it
[會議影片連結](https://www.youtube.com/watch?v=hquNW0MtoEo)
BigQuery 上的資料網格：架構師對為何以及如何實作的觀點

**1. 核心觀點**

本次會議主要探討了如何使用 Google Cloud 技術構建資料網格，以及為何這對企業至關重要。講者們分享了資料網格的概念、組成部分，以及 Virgin Media O2 在資料轉型方面的經驗和架構。重點強調了資料網格在解決資料孤島、加速資料洞察、強化資料治理和推動 AI 應用方面的重要作用。

**2. 詳細內容**

會議首先闡述了當前 AI 時代資料的重要性，指出 AI 的發展離不開高品質的資料。然而，資料管理面臨著資料孤島、快速變化和缺乏資料所有權文化等挑戰。資料網格作為一種資料管理架構，旨在提供對資料的統一存取和治理，簡化團隊存取資料的方式，縮短從原始資料到可用資料的時間，並有效管理安全性、合規性和風險。

資料網格包含四個主要組成部分：資料整合、元資料驅動的資料管理、資料治理和資料啟用。資料網格與資料網狀架構並非相互競爭的概念，而是互補的。資料網狀架構側重於聯合資料治理和分散式所有權，而資料網格則提供統一的資料整合和治理。

BigQuery 作為 Google 的統一資料和 AI 平台，提供了構建資料網格所需的各種功能，包括多模態資料基礎、統一的治理層和整合的 AI 功能。BigQuery 簡化了資料管理和資料使用的流程，加速了資料洞察的獲取。

Virgin Media O2 的講者分享了他們在過去六年中使用 Google Cloud 技術和 BigQuery 進行資料轉型的經驗。他們將多個大型資料平台從本地遷移到 GCP，並構建了一個結合資料網格和資料網狀架構的統一資料平台。他們強調了資料整合的重要性，並分享了他們在資料工程、建模、資料民主化和價值創造方面的實踐。

在問答環節，講者們分享了他們的經驗教訓，包括從小處著手、關注業務價值和建立良好的 FinOps 計劃。他們還討論了不同遷移方法的優缺點，以及資料網格和資料網狀架構的實施策略。

**3. 重要結論**

資料網格是一種有效的資料管理架構，可以幫助企業解決資料孤島、加速資料洞察、強化資料治理和推動 AI 應用。BigQuery 作為 Google 的統一資料和 AI 平台，提供了構建資料網格所需的各種功能。Virgin Media O2 的經驗表明，透過有效的資料轉型，企業可以改善客戶體驗、提高營運效率和加速產品上市。
