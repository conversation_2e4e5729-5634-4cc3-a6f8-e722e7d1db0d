# Remote development in 2025 Your new default
[會議影片連結](https://www.youtube.com/watch?v=A-hmvl7i1cI)
遠端開發在 2025：你的新預設

## 1. 核心觀點

本次會議主要探討了遠端開發的現狀、優勢以及未來發展趨勢，特別是在晶片短缺、資料安全、地緣政治等因素影響下，遠端開發如何成為新的常態。會議涵蓋了從工具選擇、開發流程到最佳實踐等多個方面，旨在幫助開發者和企業更好地利用遠端開發提高效率、保障安全。

*   **遠端開發的必要性：** 晶片短缺、多架構需求、資料安全、地緣政治等因素共同推動遠端開發成為趨勢。
*   **遠端開發的定義：** 將開發環境遷移至遠端伺服器，本地設備僅作為客戶端，實現協作、安全和效率的提升。
*   **遠端開發的目標：** 提高開發者生產力，縮短反饋迴圈，降低認知負荷。
*   **Google Cloud 提供的解決方案：** Cloud Shell Editor 和 Cloud Workstations。
*   **最佳實踐：** 網路配置、映像檔客製化、專案結構、內部市場、實驗文化等。

## 2. 詳細內容

*   **遠端開發的背景與動機：**
    *   晶片短缺導致本地開發資源受限。
    *   需要支援多種架構（如 ARM 和 x86）。
    *   資料安全和程式碼保護日益重要。
    *   地緣政治因素影響資料落地和處理。
*   **遠端開發的定義與優勢：**
    *   將開發環境遷移至遠端伺服器，本地設備僅作為客戶端。
    *   優勢包括協作、安全、工作流程優化和開發者生產力提升。
*   **開發流程的內迴圈與外迴圈：**
    *   內迴圈：程式碼編寫、建置、測試。
    *   外迴圈：整合、測試、發布、部署。
    *   遠端開發可以優化內外迴圈，提高開發效率。
*   **Google Cloud 提供的解決方案：**
    *   **Cloud Shell Editor：**
        *   基於 Code OSS，提供終端和編輯器。
        *   預設啟用 Gemini Code Assist。
        *   支援 Web 預覽，方便前端開發。
        *   計算資源是臨時的，但磁碟是持久的。
    *   **Cloud Workstations：**
        *   在使用者自己的 VPC 中執行，提供更高的安全性和控制力。
        *   支援多種 IDE，如 JetBrains。
        *   可以管理叢集、配置和工作站。
        *   可以配置 GPU、機密 VM 等。
        *   可以自訂映像檔，預先安裝擴充套件和工具。
        *   可以透過 Visual Studio Code 進行遠端連線。
*   **Cloud Workstations 的使用案例：**
    *   使用 Streamlit 進行 Web 開發。
    *   使用內部 IP 位址連線 Cloud SQL 執行個體。
*   **遠端開發的最佳實踐：**
    *   啟用 Private Google Access。
    *   客製化映像檔，預先安裝常用工具和擴充套件。
    *   建立沙箱專案，用於實驗和測試。
    *   建立內部市場，方便開發者分享和使用工具。
    *   建立實驗文化，鼓勵開發者嘗試新技術和流程。

## 3. 重要結論

遠端開發已成為一種重要的開發模式，可以幫助開發者和企業提高效率、保障安全。Google Cloud 提供的 Cloud Shell Editor 和 Cloud Workstations 等工具，為遠端開發提供了強大的支援。透過採用最佳實踐，可以更好地利用遠端開發的優勢，提升開發者體驗和生產力。
