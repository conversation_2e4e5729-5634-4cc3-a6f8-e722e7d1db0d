# Accelerating data product development with Gen AI
[會議影片連結](https://www.youtube.com/watch?v=eLd5JSdy260)
利用生成式 AI 加速數據產品開發

## 1. 核心觀點

本次會議主要探討如何利用生成式 AI（Gen AI）來加速數據產品的開發，解決數據品質問題，並找出符合業務需求的數據切片方式。核心觀點包括：

*   **數據是新的石油：** 企業需要從原始數據中提煉出有價值的資訊，並將其轉化為可用的產品。
*   **數據品質至關重要：** 數據品質問題會導致生產力下降、錯失機會和成本增加。
*   **業務驅動技術：** 數據產品開發應以業務需求為導向，避免技術先行。
*   **AI 不是萬能的：** 領域專家知識在解決數據品質問題和確保數據產品品質方面至關重要。
*   **生成式 AI 加速數據產品開發：** 生成式 AI 可以幫助企業更快地創建數據產品，並將數據轉化為可用的商業價值。
*   **數據品質是持續的過程：** 數據品質需要在整個數據生命週期中不斷監控和改進。

## 2. 詳細內容

會議首先指出，許多企業在利用數據資產創造價值方面面臨挑戰。儘管數據被譽為「新的石油」，但許多企業未能從中獲利。主要原因包括：

*   **數據分散且難以信任：** 企業不清楚數據的儲存位置，以及哪些數據來源是可靠的。
*   **數據品質問題：** 數據品質問題導致大量時間和資源浪費在數據清理和整合上。
*   **錯失市場機會：** 由於數據準備時間過長，企業可能錯失市場先機。

為了解決這些問題，會議提出了以下解決方案：

*   **數據發現：** 企業需要了解自身擁有哪些數據，以及數據的品質和潛在價值。
*   **數據品質檢測與修復：** 利用 AI 技術自動檢測和修復數據品質問題，並結合人工干預，確保數據的準確性和完整性。
*   **數據產品定義：** 根據業務需求，定義數據產品的結構和內容，以便快速生成可用的數據產品。

會議展示了 McKinsey 開發的 AI 工具，用於數據品質檢測、異常檢測和實體解析。這些工具利用機器學習和生成式 AI 技術，可以自動識別數據中的問題，並提供修復建議。同時，會議也強調了人工干預的重要性，領域專家可以提供反饋，幫助 AI 模型更好地理解數據的上下文，並提高數據品質。

此外，會議還展示了如何利用生成式 AI 加速數據產品的開發。通過向 AI 提供業務背景、源數據表和目標模式，AI 可以自動生成數據產品的結構和轉換邏輯。這可以大大縮短數據產品的開發時間，並降低開發成本。

會議分享了在多個客戶部署這些工具的經驗，並總結了以下幾點經驗教訓：

*   **數據品質是業務問題，而不僅僅是 IT 問題：** 業務部門需要參與數據品質的定義和改進過程。
*   **數據品質問題可能只是症狀，而不是根本原因：** 企業需要從整體角度看待數據問題，並找出根本原因。
*   **可解釋性很重要：** 在某些行業，企業需要解釋 AI 模型的決策過程。
*   **數據品質需要在整個數據生命週期中進行監控和改進：** 數據的形狀、上下文和價值會隨著時間的推移而變化，因此需要持續監控和改進數據品質。

## 3. 重要結論

利用生成式 AI 可以顯著加速數據產品的開發，並提高數據的商業價值。然而，數據品質仍然是成功的關鍵。企業需要建立完善的數據治理機制，並結合 AI 技術和人工干預，才能確保數據的準確性和完整性，並從數據中獲得真正的價值。
