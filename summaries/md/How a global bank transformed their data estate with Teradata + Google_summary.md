# How a global bank transformed their data estate with Teradata + Google
[會議影片連結](https://www.youtube.com/watch?v=Pwso9duzorw)
一家全球性銀行如何透過 Teradata + Google 轉型其數據資產

## 1. 核心觀點

本次會議主要探討了匯豐銀行（HSBC）如何與 Teradata 和 Google Cloud 合作，將其關鍵數據資產從內部部署環境遷移到雲端，並實現現代化轉型。核心觀點包括：

*   **信任與合作夥伴關係至關重要：** 匯豐銀行強調與 Teradata 和 Google Cloud 建立深厚的信任關係，並將其視為合作夥伴，共同解決遷移過程中的挑戰。
*   **生態系統複雜性是主要挑戰：** 遷移的難點不在於 Teradata 本身的功能，而在於與銀行現有 IT 生態系統的整合，包括網路、安全、合規等。
*   **遷移目標是提升能力和靈活性：** 匯豐銀行希望透過遷移到雲端，提升數據處理速度、改善功能、並能利用 Google Cloud 的原生雲端產品。
*   **成本效益和業務連續性是關鍵考量：** 遷移不僅要提升性能，還要降低成本，並確保業務連續性，滿足監管要求。
*   **AI 和機器學習是未來發展方向：** 遷移到雲端為匯豐銀行提供了利用 AI 和機器學習技術，改善客戶體驗和業務流程的機會。

## 2. 詳細內容

*   **遷移的動機：**
    *   提升處理速度：將原本需要數小時甚至數天的流程縮短到幾分鐘。
    *   改善功能：利用平行處理等技術，提升數據處理能力。
    *   利用原生雲端產品：讓業務部門能夠使用 Looker、BigQuery、Vertex AI 等 Google Cloud 服務。
    *   避免廠商鎖定：透過將數據儲存在 S3 等開放格式中，降低對單一廠商的依賴。
    *   提升可用性：改善業務連續性，滿足監管要求。

*   **遷移的挑戰：**
    *   生態系統複雜性：與 150 多個下游和上游應用程式的整合。
    *   合規和監管：獲得歐洲銀行等機構的批准，需要耗費大量時間。
    *   測試環境：難以在預生產環境中複製生產環境的複雜性。
    *   35 小時的停機時間限制：需要在極短的時間內完成數據遷移。

*   **遷移的方法：**
    *   建立信任的合作夥伴關係：與 Teradata 和 Google Cloud 密切合作，共同解決問題。
    *   關注生態系統：將重點放在解決與現有 IT 生態系統的整合問題上。
    *   儘早進行生態系統測試：儘早將系統部署到預生產環境中，以發現潛在問題。
    *   並行處理合規和監管流程：儘早開始與監管機構溝通，以避免延誤。
    *   採用大爆炸式遷移：一次性遷移所有數據和應用程式，以簡化遷移過程。

*   **遷移的成果：**
    *   降低成本：透過 Teradata 和 Google Cloud 的合作，降低了運行成本。
    *   提升性能：改善了數據處理速度和功能。
    *   改善業務連續性：提升了系統的彈性和可用性。
    *   更容易滿足合規和監管要求：簡化了與監管機構的溝通。
    *   為 AI 和機器學習奠定基礎：為利用 AI 和機器學習技術改善客戶體驗和業務流程奠定了基礎。

*   **未來展望：**
    *   利用 AI 和機器學習技術，改善客戶體驗和業務流程。
    *   將 Gemini 等 Google Cloud AI 服務與 Teradata 數據倉庫整合。
    *   探索在內部部署環境中使用 Google Cloud AI 服務的可能性。

## 3. 重要結論

匯豐銀行透過與 Teradata 和 Google Cloud 的合作，成功地將其關鍵數據資產遷移到雲端，並實現了現代化轉型。這次遷移不僅提升了數據處理能力和靈活性，還降低了成本，改善了業務連續性，並為利用 AI 和機器學習技術奠定了基礎。匯豐銀行的經驗表明，建立信任的合作夥伴關係、關注生態系統複雜性、並儘早開始與監管機構溝通，是成功進行雲端遷移的關鍵。
