# Strengthen data privacy Multiparty confidential collaboration at scale
[會議影片連結](https://www.youtube.com/watch?v=oxjCeghgGtY)
強化數據隱私，實現大規模多方機密協作

## 1. 核心觀點

本次會議主要介紹了 Google Cloud 的機密空間（Confidential Space）產品，以及它如何幫助不同行業的公司實現新的應用場景，尤其是在多方協作和保護數據隱私方面。核心觀點包括：

*   機密空間創建了一個安全的協作環境，稱為可信任執行環境（Trusted Execution Environment，TEE），數據僅由受信任和批准的程式碼處理。
*   允許多個實體在無需完全信任彼此的情況下進行協作，各方保留對其數據的控制權、所有權和機密性。
*   機密空間不僅適用於廣告技術中的安全數據匹配、跨銀行的欺詐檢測、醫療保健環境中的聯合模型訓練或聯合分析，還能加強 SaaS 公司的基礎設施安全，防止內部人員未經授權訪問用戶數據。
*   支援各種機器學習和生成式 AI 用例，例如與第三方共享專有機器學習模型，同時保留對智慧財產權的完全控制。

## 2. 詳細內容

*   **Google Ads 的應用：** Google Ads 去年推出了基於 TEE 的首個功能，稱為機密匹配（Confidential Matching）。它允許 Google Ads 客戶以確保數據按照預期處理的方式與 Google 共享第一方數據，並且沒有人（包括 Google）會獲取不應獲取的新資訊。這解鎖了各種廣告用例，例如在不損害用戶隱私的情況下進行衡量和受眾分析。

*   **Symfony 的應用：** Symfony 是一家為金融服務行業提供安全通訊服務的公司。由於其客戶非常重視隱私，因此最初的部署大多在本地進行。這使客戶能夠完全控制其數據和通訊隱私，但也意味著他們必須承擔運行和管理本地軟體元件的所有成本和維護開銷，並減緩了創新和部署修復程式及新功能的速度。Symfony 利用機密空間構建了所謂的機密雲（Confidential Cloud），並開始將其客戶從本地遷移到真正的基於雲的 SaaS 服務，同時仍然提供客戶所需的數據隱私保證。

*   **機密空間的優勢總結：** 機密空間允許多個實體和團隊在保留數據隱私保證的情況下進行協作。數據僅由受信任和經過審查的程式碼處理，沒有其他人可以訪問。即使允許第三方使用數據或模型，每個人都保留對其擁有的數據或模型的控制權。數據受到內部人員（例如好奇的操作員、SRE、SaaS 管理員和其他內部人員）的保護。

## 3. 重要結論

Google Cloud 的機密空間產品提供了一個安全可靠的環境，允許多方在保護數據隱私的前提下進行協作，適用於廣告、金融、醫療保健等多個行業。它不僅可以幫助企業實現新的應用場景，還可以加強基礎設施安全，保護數據免受未經授權的訪問。
