Unlock the power of Looker Performance secrets and modeling mastery
        
[會議影片連結](https://www.youtube.com/watch?v=1Fh2TBVTjJw)
        
解鎖 Looker 效能秘密與模型建立精通之道

## 1. 核心觀點

本次會議主要探討如何提升 Looker 的效能，並精進 LookML 模型的建立技巧。會議涵蓋了 Looker 平台架構、管理員職責、HubSpot 的 Looker 使用經驗，以及開發者工具和 Spectacles 的演示。核心觀點包括：

*   Looker 能夠擴展至最嚴苛的工作負載，其關鍵在於其資料庫內架構，能充分利用資料庫的規模。
*   效能是共同的責任，Looker 管理員、使用者和模型開發者都會對其產生影響。
*   LookML 的撰寫方式會直接影響查詢的效率，因此優化 LookML 至關重要。
*   管理員可以透過設定限制、監控排程等方式來管理 Looker 執行個體的效能。
*   Spectacles 等工具可以協助開發者驗證 LookML，並確保程式碼變更不會破壞現有內容。

## 2. 詳細內容

會議內容主要分為四個部分：

**Looker 平台概述：**

*   Looker 作為資料生態系統的一部分，將來自不同資料來源的資料整合到一個受管理的模型層中。
*   Looker 的語意層可以被 Looker 應用程式本身以及其他 BI 工具和 AI 功能所使用。
*   Looker 的主要使用案例包括現代 BI 分析、整合洞察、資料驅動工作負載和嵌入式分析。

**管理員職責：**

*   管理員需要關注 Looker 執行個體的生命週期、使用者佈建、外部連線和使用者權限。
*   診斷效能問題可能很困難，但可以使用系統活動日誌等工具來識別瓶頸。
*   管理員可以透過限制並行連線數、設定自動重新整理間隔和管理合併查詢來控制執行個體範圍內的行為。
*   定期清理未使用內容、小心使用角色和權限、避免過多的巢狀資料夾，並監控自動重新整理和排程。

**HubSpot 的 Looker 使用經驗：**

*   HubSpot 擁有超過 8,000 名 Looker 使用者，並採用中心輻射模型來管理 Looker 專案。
*   HubSpot 透過集中資料團隊、設定策略目標和引入「良好摩擦」來標準化 Looker 環境。
*   HubSpot 與 Google Professional Services 合作進行健康檢查，並實施內容管理、移除不活躍使用者和縮減儀表板規模。
*   HubSpot 正在部署 Spectacles，以驗證 SQL、LookML 和管理內容。

**開發者考量：**

*   開發者應對 LookML 模型的效能負責，並做出明智的選擇。
*   良好的 LookML 模型應與功能區域對齊，並充分利用 Looker 的組織工具。
*   開發者應主動解決效能問題，並使用內容驗證器等工具來識別損壞的內容。
*   Looker CI 是一種持續整合工具，可以自動執行測試，以確保程式碼變更不會破壞現有內容。

## 3. 重要結論

本次會議提供了關於如何提升 Looker 效能和精進 LookML 模型建立技巧的寶貴見解。透過了解 Looker 平台架構、管理員職責、HubSpot 的使用經驗以及開發者工具，與會者可以更好地利用 Looker 來滿足其資料分析需求。會議強調了效能是共同的責任，並鼓勵管理員、使用者和開發者共同努力，以確保 Looker 執行個體能夠高效且可靠地運行。
