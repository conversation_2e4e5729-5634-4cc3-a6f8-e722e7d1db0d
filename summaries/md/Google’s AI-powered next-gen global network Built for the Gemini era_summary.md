Google’s AI-powered next-gen global network Built for the Gemini era

[會議影片連結](https://www.youtube.com/watch?v=oZN9kUIVLOU)
Google 以 AI 驅動的下一代全球網路，為 Gemini 時代而生

## 1. 核心觀點

本次會議主要介紹 Google 如何利用 AI 技術重新架構其全球網路，以應對 AI 時代對網路效能、可靠性、安全性和效率提出的新挑戰。核心觀點包括：

*   **AI 時代的網路需求：** 傳統網路架構已無法滿足 AI 應用對高頻寬、低延遲、高可靠性和安全性的需求。
*   **Google 的解決方案：** Google 透過四大原則重新設計其網路：指數級擴展性、超越 9 的可靠性、意圖驅動的可程式化能力和自主網路。
*   **與合作夥伴的協同：** Google 與 British Telecom (BT) 等合作夥伴合作，將其網路與 Google Cloud WAN 整合，為客戶提供端到端的網路解決方案。

## 2. 詳細內容

*   **AI 時代的挑戰：**
    *   AI 訓練和推論需要大量的計算資源和資料傳輸，對網路頻寬和延遲提出了極高的要求。
    *   AI 應用對網路可靠性要求極高，任何網路中斷都可能導致昂貴的 TPU 和 GPU 資源浪費。
    *   AI 模型和資料的安全性至關重要，需要保護資料在傳輸過程中的完整性和機密性。
    *   在提供世界一流的 AI 基礎設施的同時，還需要考慮成本效益。

*   **Google 的四大原則：**
    *   **指數級擴展性：** 採用多分片網路架構，可以根據需求彈性擴展網路容量，滿足 AI 應用對高頻寬的需求。
    *   **超越 9 的可靠性：** 透過獨立的資料、控制和管理平面，以及軟體保護重路由等技術，實現極高的網路可靠性和快速故障恢復。
    *   **意圖驅動的可程式化能力：** 透過可程式化的網路，可以根據客戶的特定需求，提供精細化的網路服務，例如延遲敏感型應用程式的優化和資料主權的保障。
    *   **自主網路：** 利用 AI 和機器學習技術，實現網路的自動化管理和優化，包括流量預測、主動恢復和效能調整。

*   **技術展示：**
    *   **多分片網路的可靠性：** 演示了在單分片網路和多分片網路中，當發生全球性網路故障時，多分片網路如何透過保護重路由技術，無縫地將流量切換到其他分片，確保應用程式的持續運行。
    *   **自主網路的故障排除：** 演示了如何利用 AI 驅動的自主網路運營工具，在幾分鐘內診斷和解決網路故障，大大縮短了故障排除時間。

*   **與 British Telecom (BT) 的合作：**
    *   BT 將其全球網路與 Google Cloud WAN 整合，為客戶提供端到端的網路解決方案。
    *   透過共享網路智慧，可以實現更佳的網路效能、可靠性和安全性。
    *   客戶可以透過 BT 購買 Google Cloud 服務，並獲得統一的 SLA 保證。

*   **Google Cloud WAN：**
    *   Google Cloud WAN 是一個完全託管的 Google 支援的解決方案，可統一您的私有 WAN、軟體定義的 WAN 和應用程式所需的安全邊緣。
    *   可以將您的資料中心、園區、分支機構、SaaS、主機代管、公有雲連接到單一統一的 WAN 中。

## 3. 重要結論

Google 正在利用 AI 技術重新架構其全球網路，以應對 AI 時代對網路提出的新挑戰。透過指數級擴展性、超越 9 的可靠性、意圖驅動的可程式化能力和自主網路等四大原則，Google 正在打造一個為 AI 時代而生的網路。與 British Telecom 等合作夥伴的協同，將進一步擴展 Google 網路的覆蓋範圍和能力，為客戶提供更佳的網路服務。Google Cloud WAN 是一個關鍵產品，可讓客戶利用這些創新技術。
