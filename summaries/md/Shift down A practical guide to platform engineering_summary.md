# Shift down A practical guide to platform engineering
[會議影片連結](https://www.youtube.com/watch?v=UkgfuXNzUh4)
Shift down 平台工程實務指南

## 1. 核心觀點

本次演講主要探討平台工程在 Google 內部的實踐，以及如何透過「Shift Down」策略，將常見問題的解決方案嵌入平台，從而提升開發效率、降低成本並促進創新。核心觀點包括：

- **平台工程的核心目標：** 改善開發者體驗、提升長期可持續性、對抗軟體熵。
- **Shift Down 的概念：** 將已解決的、可重複使用的問題解決方案嵌入平台，讓開發者專注於更具創新性的工作。
- **平台不僅僅是技術：** 它是一個社會技術學科，結合了工具、流程和人員，以驅動創新。
- **質量屬性是架構的體現：** 安全性、可靠性、效能等質量屬性深深植根於系統架構中。
- **共享命運需要共享責任：** 生態系統的成功取決於平台和使用者的共同努力。
- **持續改進的重要性：** 開發生態系統不斷演進，需要不斷調整和改進。

## 2. 詳細內容

### 平台工程與 Google 的旅程

- 平台工程旨在透過提供基礎設施、流程和工具來推動開發流程，從而提高速度、規模和創新，最終驅動實際的業務價值。
- 平台應具備以下要素：
    - **平台即產品的思維：** 深入了解使用者需求，建立回饋迴路。
    - **開發者自主存取能力：** 提供隨需應用的能力，減少人工干預。
    - **隱藏底層複雜性：** 透過良好的抽象化，讓開發者專注於功能開發。
    - **提供安全預設和最佳實踐：** 遵循 SRE 原則，例如自動化部署、監控和安全控制。
- 平台工程是開發生態系統的基石，透過平台工程嵌入控制項，並透過生態系統設計來利用這些控制項。

### 抽象化與耦合的平衡

- 抽象化簡化複雜系統、管理風險、促進重用和標準化。
- 開發生態系統將抽象化連接起來，創造出大於各部分之和的整體。
- 質量屬性是系統的非功能性需求，難以透過孤立的元件實現。
- 平台工程在實現和強化這些質量屬性方面發揮著至關重要的作用。

### Shift Down 的力量

- 以 API 棄用和遷移為例，說明 Shift Down 如何將開發者的責任轉移到平台本身。
- Google 的大規模變更基礎設施可以自動化處理數百萬行程式碼的重構。
- 這種嵌入式能力可以擴展到支援其他類型的程式碼轉換。

### 實用見解與觀察

- 評估 Shift Down 的成本效益，需要考慮業務影響和生態系統的控制能力。
- 使用地圖模型來匹配平台投資與業務需求，並識別需要改進的領域。
- Shift Down 改變了開發者的工作內容和關注點，需要相應地調整支援方式。
- 共享命運意味著生態系統中的元件相互依賴，需要共享責任以避免「公地悲劇」。
- 衡量開發、部署和運營流程的改進，以評估平台工程的影響。
- 平衡功能開發和平台開發之間的張力，找到激勵和重視兩者的策略。

### 生態系統類型

- 生態系統類型從零（YOLO，自由探索）到四（Assured，高度整合）不等。
- 最佳的生態系統類型取決於業務需求。

## 3. 重要結論

透過從一開始就設計質量屬性、策略性地將功能嵌入平台、培養共享責任感並擁抱持續改進，可以建立一個有效的開發生態系統，從而驅動業務價值。Shift Down 策略不僅可以提高開發效率，還可以降低風險、促進創新，並最終提升 Google 的競爭力。
