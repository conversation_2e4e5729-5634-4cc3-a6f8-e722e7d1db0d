New ways to automate your work and integrate with Google Workspace

[會議影片連結](https://www.youtube.com/watch?v=ZO3CKEUOWS0)
工作自動化和整合 Google Workspace 的新方法

## 1. 核心觀點

本次會議主要介紹 Google Workspace 在工作自動化和整合方面的新功能和方法，重點展示了 Google Workspace Flows、Workspace Connector Platform 以及與第三方應用程式（如 HubSpot、ServiceNow）的整合。核心觀點包括：

*   **Google Workspace Flows：** 一種新的自動化體驗，允許在工作流程中整合 AI 代理，消除傳統工作流程的邏輯障礙。
*   **Workspace Connector Platform：** 將第三方工具直接整合到核心 Workspace 應用程式中，提供無摩擦的設定體驗和統一的安全隱私保護。
*   **與第三方應用程式的深度整合：** 展示了 HubSpot 和 ServiceNow 如何利用 Google Workspace 的功能，提升銷售團隊的生產力並簡化 IT 支援流程。
*   **AI 驅動的自動化：** 強調利用 Gemini 的強大功能，實現更智慧、更具情境感知的工作流程自動化。

## 2. 詳細內容

*   **Workspace Platform 概述：**
    *   強調 Workspace 平台的安全性、雲端原生架構和大規模應用。
    *   介紹了 AppSheet（無程式碼）、AppScript（低程式碼）和 ProDev（專業開發）三種不同的開發方式。
    *   重點介紹了 Smart Chips、Add-ons 和 Chat Apps 等人機協作功能。
*   **Google Workspace Flows 詳解：**
    *   展示了如何使用 Flows 建立自動化工作流程，例如監控客戶回饋、優先處理問題等。
    *   介紹了如何將 Gemini 整合到 Flows 中，以分析資料、做出決策並產生內容。
    *   展示了如何使用預先建立的或自訂的 Gemini 模型（Gems）來執行特定任務，例如產品品質評估和客戶服務。
    *   強調了 Flows 的易用性，以及如何透過自然語言指令快速建立和修改工作流程。
    *   介紹了如何使用 Apps Script 建立自訂的觸發器和動作，並將其整合到 Flows 中。
*   **Workspace Connector Platform 詳解：**
    *   強調了 Connector Platform 如何將第三方工具直接整合到 Workspace 應用程式中，簡化使用者的工作流程。
    *   介紹了 Connector Platform 在 Flows 和 Gemini 擴充功能中的應用。
    *   列出了即將推出的 Connector，包括 Asana、Confluence、HubSpot、Jira、MailChimp 等。
*   **HubSpot 整合案例：**
    *   展示了 HubSpot 如何利用 Gemini for Google Workspace，幫助銷售團隊提高生產力。
    *   介紹了銷售代表如何在 Gmail 中直接使用 Gemini 分析電子郵件、建立聯絡人、查看交易資訊和新增筆記。
    *   強調了這種整合如何節省銷售代表的時間，避免在不同應用程式之間切換。
*   **ServiceNow 整合案例：**
    *   展示了 ServiceNow 如何將其分析技術整合到 Google Chat 應用程式中，提供自助服務和即時代理支援。
    *   介紹了使用者如何透過 Now Virtual Agent 請求支援服務、訂購設備和重設密碼。
    *   強調了這種整合如何主動解決問題，並將 Google Drive 內容整合到 ServiceNow 平台中。
*   **Google Workspace Marketplace：**
    *   強調了 Marketplace 上有超過 6,000 個應用程式可供使用，並提供了產品內探索和管理功能。

## 3. 重要結論

Google Workspace 正在透過 Flows、Connector Platform 和 AI 驅動的自動化功能，不斷提升工作效率和整合能力。與第三方應用程式的深度整合，使得使用者可以在熟悉的 Workspace 環境中，更輕鬆地使用各種工具和服務。這些新功能旨在消除工作流程中的摩擦，讓使用者能夠更專注於重要的任務，並提高整體生產力。
