# Closing the air gap Securing industrial operations in the cloud
[會議影片連結](https://www.youtube.com/watch?v=7NdhFnM0Zqc)
彌合氣隙：確保雲端工業營運安全

## 1. 核心觀點

本次會議主要探討工業控制系統（OT）環境中，傳統「氣隙」安全防護概念的消逝，以及在 OT 系統日益連接雲端的情況下，如何確保其安全性。核心觀點包括：

*   傳統的 OT 安全「氣隙」已不復存在，連接已成必然。
*   重點應轉向如何保護日益連接的 OT 系統。
*   雲端技術在 OT 環境中的應用，可提升效率、擴展性及安全性。
*   良好的安全基礎建設（如網路分段、存取控制）至關重要。
*   應將安全納入 OT 系統的設計和部署階段。
*   AI 和機器學習等新興技術，可增強 OT 環境的安全性。
*   跨部門協作（IT、OT、安全）對於成功部署雲端 OT 解決方案至關重要。

## 2. 詳細內容

會議首先強調了 OT 與 IT 之間的界線日益模糊，甚至可以說 IT 即 OT，OT 即 IT。隨著 OT 系統越來越多地連接到雲端，傳統的「氣隙」防護已不再有效。

與會者討論了推動企業在 OT 環境中採用雲端技術的主要因素，包括：

*   **效率提升：** 雲端可實現 OT 系統的自動化和簡化。
*   **擴展性：** 雲端可根據需求輕鬆擴展 OT 資源。
*   **安全性：** 雲端可提供先進的安全功能，例如威脅檢測和事件回應。
*   **韌性：** 雲端可提高 OT 系統的韌性和可用性。

與會者也強調，在將 OT 系統遷移到雲端時，需要考慮一些重要的安全因素，包括：

*   **架構：** 確保 OT 系統的架構能夠安全地與雲端整合。
*   **身分驗證和存取控制：** 實施強大的身分驗證和存取控制措施，以防止未經授權的存取。
*   **網路分段：** 將 OT 網路與 IT 網路分段，以限制攻擊的影響範圍。
*   **監控和檢測：** 實施全面的監控和檢測功能，以識別和回應安全事件。

此外，與會者還討論了 AI 和機器學習在 OT 安全中的應用，例如：

*   **威脅檢測：** 使用 AI 和機器學習來檢測 OT 網路中的異常活動。
*   **漏洞管理：** 使用 AI 和機器學習來識別和修復 OT 系統中的漏洞。
*   **事件回應：** 使用 AI 和機器學習來自動化事件回應流程。

會議中也提到，在 OT 環境中導入新技術時，可能會遇到一些阻力，主要是因為對安全性和可靠性的擔憂。為了克服這些阻力，需要與 OT 團隊建立信任，並確保安全措施不會影響 OT 系統的效能。

## 3. 重要結論

本次會議強調，在 OT 環境中，傳統的「氣隙」安全防護已不再適用。企業應積極擁抱雲端技術，並將安全納入 OT 系統的設計和部署階段。透過跨部門協作、實施良好的安全基礎建設，以及利用 AI 和機器學習等新興技術，可以確保 OT 系統在日益連接的世界中保持安全和可靠。
