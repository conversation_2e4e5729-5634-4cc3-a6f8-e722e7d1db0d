Master observability with expert strategies for unified troubleshooting

[會議影片連結](https://www.youtube.com/watch?v=_lf3HzpCkWc)
掌握可觀測性：運用專家策略實現統一故障排除

**1. 核心觀點**

本次會議主要探討如何運用專家策略，以統一的方式解決複雜系統中的故障排除問題，核心觀點包括：

*   將可觀測性問題視為數據問題，並透過數據的產生、收集、路由、儲存和消費來解決。
*   利用開放原始碼工具（如 OpenTelemetry）建立強大的數據基礎，實現廠商中立的數據收集和路由。
*   將分散式追蹤應用於生成式 AI 代理的可觀測性問題，以了解 AI 系統的內部運作。
*   建立可觀測性數據湖，集中管理遙測數據，並透過語義層提供不同視角，以支援各種分析和故障排除場景。

**2. 詳細內容**

*   **複雜性挑戰：** 現代系統的複雜性不斷增加，包括分散式系統、微服務、多雲環境和生成式 AI，這使得可觀測性變得更加困難。
*   **可觀測性即數據問題：** 將可觀測性問題視為數據問題，需要關注數據的產生、收集、路由、儲存和消費。
*   **OpenTelemetry 的重要性：** OpenTelemetry 是一個開放原始碼專案，提供標準化的 API、SDK 和工具，用於收集和傳輸遙測數據，有助於實現廠商中立的可觀測性。
*   **Colibra 的經驗：** Colibra 分享了他們使用 OpenTelemetry 五年的經驗，包括如何建立數據基礎、路由數據到不同的後端系統，以及如何利用語義慣例來豐富數據。
*   **生成式 AI 代理的可觀測性：** 將分散式追蹤應用於生成式 AI 代理，可以了解 AI 系統的內部運作，包括模型調用、令牌使用情況和延遲。
*   **Cloud Observability 的應用：** Cloud Observability 提供了一系列工具，用於收集、儲存和分析遙測數據，包括 Trace Explorer、Observability Analytics 和 Log Analytics。
*   **Observability Analytics 的擴展：** Observability Analytics 將 Log Analytics 擴展到所有形式的可觀測性數據，包括日誌、追蹤、指標和成本，提供了一個統一的平台，用於分析和故障排除。
*   **可觀測性數據湖：** 建立可觀測性數據湖，集中管理遙測數據，並透過語義層提供不同視角，以支援各種分析和故障排除場景。
*   **數據模型和語義層：** 使用寬事件模型收集所有相關屬性，並使用語義慣例和分析視圖來豐富數據，使其更易於查詢和分析。
*   **查詢語言和 UI：** 提供強大的查詢語言（如 SQL）和 UI 工具，用於探索數據、建立圖表和設定警報。

**3. 重要結論**

本次會議強調了可觀測性的重要性，並提供了實用的策略和工具，用於解決複雜系統中的故障排除問題。透過將可觀測性問題視為數據問題，並利用開放原始碼工具和雲端平台，企業可以建立強大的可觀測性系統，提高系統穩定性、降低成本並加速創新。
