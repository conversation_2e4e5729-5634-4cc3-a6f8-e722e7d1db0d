# Tackle the complexities of managing GKE at scale
[會議影片連結](https://www.youtube.com/watch?v=vsm5uDONJWA)
應對大規模管理 GKE 的複雜性

## 1. 核心觀點

本次會議主要探討了在大規模環境下管理 Google Kubernetes Engine (GKE) 所面臨的挑戰與解決方案，並分享了 Dun & Bradstreet (D&B) 如何在其生產環境中實施這些解決方案的案例。核心觀點包括：

*   **規模化運行的複雜性：** 在應用程式數量和規模不斷增長的情況下，如何有效地管理和擴展 GKE 叢集。
*   **成本效益：** 在滿足擴展需求的同時，如何降低成本並優化資源利用率。
*   **異構部署：** 如何在不同的環境（多個叢集、多個專案、多個區域、甚至本地部署）中管理應用程式。
*   **AI/ML 工作負載的特殊需求：** 如何應對 AI/ML 模型訓練對 GPU 資源和大規模運算的需求。
*   **Fleet 和 Team 的概念：** 利用 Fleet 管理多個叢集，並使用 Team 管理應用程式團隊的權限和配置。
*   **集中化平台的重要性：** 建立集中化的 AI 平台，以實現對 AI 開發和部署的控制、安全和合規性。

## 2. 詳細內容

*   **雲端運行時策略：** Google 的雲端運行時策略旨在簡化應用程式的運行方式，專注於託管服務和容器化應用程式，並提供最佳的容器運行環境，以實現規模化、變更適應性、容錯性和可管理性。
*   **規模化和成本效益的挑戰：** 在規模化的同時，需要考慮成本效益，包括異構部署的複雜性、價格與效能的平衡，以及 AI 解決方案對可擴展性和可獲取性的極限挑戰。
*   **客戶使用案例：** 客戶通常有多個叢集分佈在不同的環境或專案中，或者跨多個叢集部署應用程式，這些叢集可能位於不同的區域、站點，甚至本地。
*   **Fleet 和 Team 的應用：** Fleet 是一個 Kubernetes 叢集的集合，可以代表生產環境或開發環境。Team 則允許應用程式團隊在 Fleet 中管理其應用程式的配置和權限，而不會影響其他應用程式。
*   **平台管理員和應用程式運營商的角色分離：** 平台管理員負責配置 Fleet 和 Team，設定租戶策略，並查看團隊統計資訊。應用程式運營商可以自助式地加入，查看其工作負載狀態和日誌，並管理其應用程式的成本、安全性和運營問題。
*   **AI/ML 工作負載的特殊考量：** AI/ML 工作負載對 GPU 資源提出了更高的要求，需要考慮可移植性（在不同 GPU 類型上運行）、高效的協調（跨數千個 GPU）以及最大化效能價格比。
*   **GKE 的核心功能：** GKE 提供了四維自動擴展（Pod 和節點的垂直和水平擴展）、成本優化配置、僅為消耗的資源付費、對 Spot VM 和 T2D VM 的支援，以及多實例 GPU（將一個 GPU 分割成多個虛擬 GPU）。
*   **GKE 的規模優勢：** GKE 支援高達 65,000 個節點，遠超過其他雲端供應商。
*   **D&B 的案例：** D&B 是一家數據和分析公司，擁有龐大的全球商業數據雲。他們正在構建一個生成式 AI 賦能平台，以集中化和控制 AI 的開發和部署。
*   **D&B 的 AI 平台策略：** D&B 的 AI 平台策略旨在賦能數據科學團隊，同時確保數據使用的合規性、安全性和可控性。該平台提供自助式配置、數據訪問、開發框架訪問、模型訪問和工程支援。
*   **D&B 的經驗分享：** D&B 強調了與 Google Professional Services Organization 合作的重要性，以及在設計階段獲得專家支援、共同學習和解決問題的價值。他們還強調了雲端不可知論和模型不可知論的重要性，以便能夠快速更換模型並利用最新的技術。

## 3. 重要結論

本次會議強調了在大規模環境下管理 GKE 的複雜性，並提供了利用 Fleet 和 Team 等概念來簡化管理的方法。D&B 的案例展示了如何通過建立集中化的 AI 平台來實現對 AI 開發和部署的控制、安全和合規性。會議還強調了與雲端供應商合作的重要性，以及在設計階段獲得專家支援、共同學習和解決問題的價值。
