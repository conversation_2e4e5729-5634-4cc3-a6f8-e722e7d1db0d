Connect, secure, and simplify with new Cross-Cloud Network innovations
[會議影片連結](https://www.youtube.com/watch?v=fMwxXgihxSg)
使用全新跨雲網路創新，連接、保護並簡化

## 1. 核心觀點

本次會議主要介紹 Google Cloud 在雲端網路方面的最新進展，特別是圍繞跨雲網路（Cross-Cloud Network）的創新。重點包括：

*   **跨雲網路的廣泛採用：** 許多大型企業已採用跨雲網路解決方案，用於多雲環境、保護網路應用程式和使用 CloudWAN。
*   **AI 時代的網路需求：** AI 和機器學習工作負載對網路提出了新的要求，包括更高的效能、可靠性和安全性。
*   **GKE Inference Gateway：** 一種新的解決方案，旨在優化 AI 推論工作負載的效能、降低延遲和成本，並提供一致的安全性。
*   **Agentic Networking：** 一種新的網路架構，旨在支援基於代理程式的應用程式，這些應用程式使用自然語言提示進行通訊和協作。
*   **服務導向網路的簡化：** 透過 Cloud App Hub 和簡化的服務發布流程，使開發人員更容易構建和部署服務。
*   **網路安全性的增強：** 透過 DNS Armor、第 7 層 SNI 過濾和與第三方安全解決方案的整合，提供更強大的網路安全性。

## 2. 詳細內容

*   **Google Cloud Network 的演進：** Google Cloud Network 不斷演進，以滿足 AI 時代的需求，提供卓越的效能、可靠性和可擴展性。Andromeda 3.0 SDN 層支援多個 VPC 之間的通訊，並針對大規模 AI 訓練和推論工作負載進行了優化。
*   **跨雲互連的增強：** Google Cloud 推出 400G 跨雲互連和雲端互連，提供高頻寬、完全託管且具有 SLA 保證的連接。客戶可以配置應用程式感知策略，以保護重要應用程式的流量。
*   **GKE Scale 的引入：** GKE Scale 支援多達 65,000 個 GPU 和 TPU 節點，實現萬億參數 AI 模型。它提供效能改進、流量隔離、雙宿主和網路分段。
*   **GKE Inference Gateway 的詳細介紹：** GKE Inference Gateway 透過智慧流量路由，根據待處理請求佇列和 KV 快取等參數優化 GPU 利用率，從而降低延遲、提高吞吐量並降低成本。它還支援跨多個 Google Cloud 區域的容量池，並與 Model Armor 等安全解決方案整合。
*   **Agentic Networking 的願景：** Anna Berenberg 闡述了 Agentic Networking 的概念，其中代理程式使用自然語言提示進行通訊和協作。這需要標準化的模型、資料和工具存取，以及基於提示的路由和統一的策略。
*   **Event Arc Advanced 的發布：** Event Arc Advanced 透過代理程式和服務擴展，為訊息匯流排提供安全性和網路控制，允許整合模型盔甲和自訂策略。
*   **網路安全性的新功能：** Google Cloud 正在引入新的網路安全功能，包括雲端原生防火牆策略、第 7 層 SNI 過濾和 DNS Armor。DNS Armor 與 Infoblox 合作，提供 DNS 流量保護。
*   **Global Front-End 的增強：** Global Front-End 現在可以為 Google Cloud、內部部署或任何其他雲端上的所有後端提供服務，而無需公開公用 IP 位址。它還支援服務擴展呼叫，以實現模型盔甲和邊緣可程式性。
*   **CloudWAN 的強調：** CloudWAN 是一種完全託管、具有 SLA 保證的骨幹網路，適用於所有企業需求，提供效能改進、安全整合和 TCO 節省。

## 3. 重要結論

Google Cloud 持續在雲端網路領域進行創新，以滿足 AI 時代不斷變化的需求。透過跨雲網路、GKE Inference Gateway、Agentic Networking 和增強的安全性功能，Google Cloud 正在幫助客戶連接、保護和簡化其雲端基礎架構，並為 AI 和機器學習工作負載提供支援。
