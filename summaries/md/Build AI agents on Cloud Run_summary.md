# Build AI agents on Cloud Run
[會議影片連結](https://www.youtube.com/watch?v=GwL8e5Z1tl4)
在 Cloud Run 上構建 AI 代理

## 1. 核心觀點

本次會議主要探討如何使用 Google Cloud Run 構建 AI 代理。核心觀點包括：

*   **AI 代理的定義與架構：** AI 代理是結合大型語言模型（LLM）、資料來源、計算能力和工具的應用程式，透過循環執行 LLM，並根據工具的輸出結果不斷迭代，直到完成任務。
*   **LangGraph 的作用：** LangGraph 是一個低階編排框架，旨在彌合傳統鏈（Chain）的可靠性與代理的靈活性之間的差距，透過圖形化的方式表示代理的控制流程，結合預定的程式碼流程和 LLM 邏輯。
*   **Cloud Run 的優勢：** Cloud Run 是一個 serverless 運行時環境，具備自動擴展、成本效益、低延遲、高可靠性、良好的開發者體驗、與 LLM 模型整合以及支援串流等優勢，非常適合用於部署 AI 代理。
*   **工具的重要性：** AI 代理需要使用各種工具來完成任務，包括數學計算、資料庫存取、API 調用、圖像生成、網頁瀏覽和程式碼沙箱等。
*   **CodeRabbit 的案例：** CodeRabbit 是一個基於 AI 的程式碼審查工具，利用 Cloud Run 的沙箱環境安全地執行程式碼，並使用 AI 代理進行程式碼審查。

## 2. 詳細內容

*   **AI 代理的演進：** 從最初的 LLM 應用，到基於檢索增強生成（RAG）的鏈，再到基於工具呼叫的代理，AI 應用程式的複雜性和靈活性不斷提高。LangGraph 的出現旨在解決傳統代理的可靠性問題，透過結合程式碼和 LLM 邏輯，實現更可靠和可控的代理行為。
*   **LangGraph 的核心特性：**
    *   **可控性：** 允許開發者完全控制代理的行為，沒有隱藏的提示或認知架構。
    *   **串流：** 支援串流輸出，讓使用者可以即時了解代理的執行進度。
    *   **持久性：** 支援狀態持久化，實現記憶功能和容錯能力。
    *   **人機迴圈：** 支援人機協作，允許使用者在代理執行過程中提供回饋和指導。
*   **Cloud Run 的特性：**
    *   **自動擴展：** 根據需求自動擴展 Cloud Run 實例，處理高併發請求。
    *   **成本效益：** 採用按使用量計費的模式，僅在代理程式碼執行時才收取費用。
    *   **內建憑證：** 內建憑證，方便呼叫 Gemini API 和其他 Google Cloud API。
    *   **靈活性：** 支援任何程式語言和 AI 框架。
    *   **串流支援：** 支援 HTTP 區塊傳輸編碼、WebSockets 和 HTTP2，實現快速串流輸出。
*   **在 Cloud Run 上構建 AI 代理的架構：**
    *   **Cloud Run 服務：** 用於服務和編排 AI 代理，執行 LangGraph 或其他代理框架。
    *   **GenAI 模型：** 使用 Gemini API、Vertex AI 端點或自託管的開放模型，提供推理能力。
    *   **記憶體：** 使用 Firestore 或 Memorystore Redis 儲存短期和長期記憶體。
    *   **資料庫：** 使用 Cloud SQL for PostgreSQL 或 AlloyDB for PostgreSQL 進行向量搜尋和資料檢索。
    *   **工具：** 使用各種工具來完成任務，例如網頁瀏覽器和程式碼沙箱。
*   **Vidze 的示範：** Vidze 展示了一個使用 LangGraph 構建的客戶支援代理，該代理可以自動回覆客戶問題、連結相關實體和執行標準作業程序。
*   **AI 代理的工具：**
    *   **瀏覽器工具：** 允許 AI 代理瀏覽網頁，可以使用 headless Chrome 和 Puppeteer/Playwright 等工具。
    *   **程式碼沙箱工具：** 允許 AI 代理執行程式碼，可以使用 Cloud Run 的沙箱環境來確保安全性。
*   **CodeRabbit 的案例：** CodeRabbit 使用 Cloud Run 構建了一個 AI 驅動的程式碼審查代理，該代理可以自動審查程式碼、提供回饋和建議，並使用沙箱環境執行程式碼分析工具。

## 3. 重要結論

Cloud Run 提供了一個強大且靈活的平台，用於構建和部署 AI 代理。透過結合 LangGraph 等代理框架、各種 Google Cloud 服務和自訂工具，開發者可以構建功能強大且可靠的 AI 代理，解決各種實際問題。CodeRabbit 的案例證明了 Cloud Run 在構建安全且可擴展的 AI 應用程式方面的能力。
