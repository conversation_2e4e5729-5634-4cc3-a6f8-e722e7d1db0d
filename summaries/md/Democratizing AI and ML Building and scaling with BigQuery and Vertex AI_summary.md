# Democratizing AI and ML Building and scaling with BigQuery and Vertex AI
[會議影片連結](https://www.youtube.com/watch?v=g4v1XEbiqgk)
Democratizing AI and ML Building and scaling with BigQuery and Vertex AI

## 1. 核心觀點

本次會議主要探討了如何利用 BigQuery 和 Vertex AI 來普及人工智慧（AI）和機器學習（ML），使構建和擴展智慧應用程式變得更加容易。核心觀點包括：

*   BigQuery 和 Vertex AI 解決了企業在 AI 時代面臨的工具分散、員工技能不足等問題。
*   Google Cloud 提供了一個以 BigQuery 為中心的統一平台，簡化了資料分析和機器學習流程。
*   BigQuery ML 允許在 BigQuery 內部訓練和部署模型，無需導出資料，簡化了資料治理和基礎設施管理。
*   BigQuery ML 透過 SQL 介面賦予更多使用者使用機器學習的能力。
*   BigQuery 與 Vertex AI 的整合，特別是與 Gemini 模型的整合，為資料操作帶來了變革性的潛力。

## 2. 詳細內容

會議首先強調了企業在 AI 時代面臨的挑戰，包括工具分散、員工技能不足、成本高昂、複雜性高、缺乏專業知識和可訪問性等。Google Cloud 透過以 BigQuery 為中心的統一平台來解決這些問題，提供統一的資料體驗、計算引擎、協作和治理。

傳統的機器學習流程涉及多個步驟，包括資料倉儲、資料導出、模型訓練和部署，這帶來了資料治理、基礎設施管理和學習不同工具的挑戰。BigQuery ML 透過將機器學習直接引入資料，簡化了這些流程。使用者可以使用熟悉的 SQL 語言在 BigQuery 內部訓練和部署模型，無需導出資料或管理額外的基礎設施。

BigQuery ML 提供了一個全面的機器學習平台，涵蓋特徵工程、訓練、推論和模型管理。它還支援外部 TensorFlow 模型和匯出到 Vertex AI。此外，BigQuery ML 還透過 SQL 介面連接到 Vertex AI，允許使用者利用 Gemini 模型和開源模型。Gemini 模型是多模態的，可以處理各種資料類型，而 BigQuery 支援結構化文字、圖像和影片資料，從而實現廣泛的應用。

會議還介紹了 BigQuery 和生成式 AI 整合的應用案例，包括提取關鍵實體、豐富資料、理解客戶情緒、生成個人化內容和分析影片。

## 3. 重要結論

BigQuery ML 透過使用熟悉的 SQL 語言，賦予更多使用者使用機器學習的能力，消除了對專業知識的需求。它還簡化了基礎設施管理，並確保資料和模型的安全。BigQuery ML 和 Vertex AI 使整個資料團隊能夠構建和擴展 AI，將原始資料轉化為可操作的情報，從而實現 AI 的普及，並推動整個組織的創新。
