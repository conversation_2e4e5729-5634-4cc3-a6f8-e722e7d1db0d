# A new BigQuery capability Integrated Earth data for better decision making
[會議影片連結](https://www.youtube.com/watch?v=xL25aGeKi4s)
BigQuery 全新功能：整合地球數據以實現更佳決策

## 1. 核心觀點

本次會議主要介紹了 Google Cloud BigQuery 的一項新功能，該功能整合了地球觀測數據，旨在幫助使用者做出更明智的決策。核心觀點包括：

*   **BigQuery 擴展了地理空間能力：** 除了原有的向量資料處理能力外，現在還能處理柵格資料，特別是地球觀測數據，例如衛星影像。
*   **地球觀測數據的價值：** 這些數據包含地球表面、水體和大氣層的資訊，可以從中提取有價值的洞察，用於監測變化、評估風險等。
*   **ST\_REGION\_STATS 函數的推出：** 這是一個新的 SQL 函數，允許使用者直接在 BigQuery 中使用 Earth Engine 的柵格分析功能，無需離開 BigQuery 編輯器。
*   **簡化了地理空間分析流程：** 透過 ST\_REGION\_STATS 函數，使用者可以使用熟悉的 SQL 語法來分析地球觀測數據，降低了學習門檻。
*   **實際應用案例：** 會議展示了如何使用這些功能來驗證供應鏈，並評估其是否符合歐盟反森林砍伐法規 (EUDR)。

## 2. 詳細內容

*   **地理空間數據的介紹：**
    *   地理空間數據包含位置資訊，常見的格式有向量和柵格。
    *   向量數據代表地圖上的位置或形狀，例如經緯度、建築物輪廓或道路。
    *   柵格數據是影像，在本次會議中主要指衛星影像和衍生數據集。
*   **地球觀測數據的來源和應用：**
    *   地球觀測數據來自衛星和航空感測器等遠端感測平台。
    *   這些數據可以幫助評估和監測自然和人造世界的變化。
    *   Google Earth Engine 是一個用於創建地球觀測衍生數據集的工具，它結合了 PB 級的衛星影像目錄和行星級的分析能力。
*   **歐盟反森林砍伐法規 (EUDR) 的背景：**
    *   EUDR 規定，受監管的七種商品（棕櫚油、大豆、橡膠、咖啡、可可、木材和牛肉）及其製品，必須提供盡職調查聲明，證明它們不是在 2020 年 12 月之後被砍伐的土地上生產的。
    *   這是一個地理空間問題，地球觀測數據和衍生柵格可以提供幫助。
*   **使用地球觀測數據進行供應鏈驗證的示例：**
    *   組織可以使用森林數據合作夥伴 (Forest Data Partnership) 的社群商品地圖等圖層，來了解特定區域的土地利用情況，並檢查聲稱在那裡種植的作物是否實際存在。
    *   例如，可以檢查某個多邊形區域在 2023 年種植棕櫚、橡膠或可可的可能性。
    *   還可以使用森林持久性 (Forest Persistence) 圖層來評估該區域在 1984 年至 2020 年間的森林覆蓋情況，以了解是否存在森林砍伐活動。
*   **使用 Python 和使用者定義函數 (UDF) 豐富 BigQuery 數據：**
    *   可以使用 Python API 與 Earth Engine 互動，並使用 Pandas 和 GeoPandas 等資料科學工具來分析數據。
    *   也可以建立雲端函數 (Cloud Function) 並將其部署為 UDF，以便在 BigQuery 中呼叫 Earth Engine 的功能。
    *   Cardo Workflows 等低程式碼工具可以簡化 UDF 的建立過程。
*   **ST\_REGION\_STATS 函數的介紹：**
    *   ST\_REGION\_STATS 是一個新的 SQL 函數，允許使用者直接在 BigQuery 中使用 Earth Engine 的柵格分析功能。
    *   該函數可以計算指定區域內像素的統計資訊，例如計數、最小值、最大值、標準差、總和和平均值。
    *   使用者可以指定要使用的柵格數據集、要分析的波段，以及分析的比例。
    *   該函數支援 Earth Engine 柵格和儲存在 Google Cloud Storage 儲存桶中的雲端最佳化 GeoTIFF (COG)。
*   **使用 ST\_REGION\_STATS 函數進行供應鏈分析的示例：**
    *   可以使用 ST\_REGION\_STATS 函數來計算 2023 年商品（例如可可）的面積，並將其與 2020 年的森林面積進行比較。
    *   如果 2023 年的商品面積大於 2020 年的非森林面積，則可能存在森林砍伐問題。
    *   可以使用 BigQuery 的分析中心 (Analytics Hub) 來尋找可用的柵格數據集和 SQL 示例。

## 3. 重要結論

BigQuery 的這項新功能透過整合地球觀測數據和簡化地理空間分析流程，為使用者提供了強大的決策支援工具。ST\_REGION\_STATS 函數的推出降低了使用 Earth Engine 柵格分析功能的門檻，使得更多使用者能夠利用這些數據來解決實際問題，例如供應鏈驗證和環境監測。
