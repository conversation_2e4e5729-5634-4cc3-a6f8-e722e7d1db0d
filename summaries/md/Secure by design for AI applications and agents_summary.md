# Secure by design for AI applications and agents
主題演講
[會議影片連結](https://www.youtube.com/watch?v=aw5Pu342tM8)
AI 應用程式與代理程式的安全設計

## 1. 核心觀點

本次會議主要探討在企業環境中，AI 應用程式和代理程式快速發展所帶來的資安挑戰，以及如何透過安全設計來應對這些挑戰。核心觀點包括：

*   AI 技術正在企業中快速普及，特別是 ChatGPT 的出現加速了企業對 AI 的採用。
*   AI 代理程式（Agents）的數量預計將在未來幾年內大幅增長，對企業 IT 架構產生深遠影響。
*   傳統的 IT 架構無法滿足 AI 時代的需求，需要重新思考如何設計和交付架構。
*   AI 帶來了新的資安威脅，包括資料中毒、模型外洩、提示注入、基礎設施攻擊和治理問題。
*   企業需要採取系統性的方法來應對這些威脅，並在 AI 應用程式的設計初期就考慮安全性。
*   安全不僅僅是技術問題，更是一個共享責任模型，需要雲端供應商、基礎設施供應商、安全廠商和企業共同努力。

## 2. 詳細內容

*   **AI 的快速普及：** ChatGPT 的出現引發了企業對 AI 的高度關注，AI 應用程式和代理程式的數量正在快速增長。預計到 2030 年，每個企業員工將擁有 5 個 AI 代理程式。
*   **IT 架構的轉變：** 傳統的 IT 架構以集中式資料和控制為中心，而 AI 時代的 IT 架構將更加分散，資料和智慧將分佈在各個地方。這對企業如何設計和交付架構提出了新的挑戰。
*   **新的資安威脅：** AI 帶來了新的資安威脅，包括：
    *   **資料中毒：** 惡意修改訓練資料，導致 AI 模型產生錯誤的結果。
    *   **模型外洩：** 攻擊者透過提示工程等手段，逆向工程 AI 模型，竊取模型的智慧財產權。
    *   **提示注入：** 惡意使用者透過精心設計的提示，欺騙 AI 模型執行未經授權的操作。
    *   **基礎設施攻擊：** 針對 AI 基礎設施（例如 GPU 伺服器）發動阻斷服務攻擊。
    *   **治理問題：** AI 模型的訓練資料可能包含敏感資訊，導致違反法規或倫理規範。
*   **攻擊速度加快：** 攻擊者也在利用 AI 技術來加速攻擊，例如，利用 AI 將資料外洩的時間從數週縮短到數小時。
*   **Google Cloud 的解決方案：** Google Cloud 透過 Vertex AI 平台，提供企業開發、部署和管理 AI 應用程式的能力。同時，Google Cloud 也與 Palo Alto Networks 等安全廠商合作，提供安全解決方案，保護 AI 應用程式免受威脅。
*   **Palo Alto Networks 的解決方案：** Palo Alto Networks 採用 Precision AI 技術，結合機器學習和生成式 AI，提高威脅檢測的準確性。同時，Palo Alto Networks 也提供 Copilot 等工具，簡化使用者與安全產品的互動。
*   **NVIDIA 的解決方案：** NVIDIA 提供 Nemo Guardrails 等開源工具，幫助企業構建安全的 AI 應用程式。Nemo Guardrails 可以攔截惡意提示，防止提示注入攻擊。
*   **共享責任模型：** AI 安全是一個共享責任模型，需要雲端供應商、基礎設施供應商、安全廠商和企業共同努力。雲端供應商負責保護雲端基礎設施的安全，基礎設施供應商負責保護硬體和軟體的安全，安全廠商負責提供安全工具和服務，企業負責制定安全策略和實施安全措施。
*   **Home Depot 的案例：** Home Depot 透過 Magic Apron 等 AI 應用程式，提升客戶體驗。同時，Home Depot 也非常重視 AI 安全，建立了負責的 AI 諮詢委員會，制定了明確的 AI 治理政策。Home Depot 還採用 Palo Alto Networks 的 XIM 平台，提升 SOC 運營效率。

## 3. 重要結論

AI 技術正在快速改變企業的運作方式，但也帶來了新的資安挑戰。企業需要及早意識到這些挑戰，並採取系統性的方法來應對。在 AI 應用程式的設計初期就考慮安全性，建立共享責任模型，並與雲端供應商、基礎設施供應商和安全廠商合作，才能確保 AI 應用程式的安全可靠。
