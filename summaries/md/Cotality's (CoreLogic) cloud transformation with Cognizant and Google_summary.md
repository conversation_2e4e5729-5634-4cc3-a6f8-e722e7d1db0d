Cotality's (CoreLogic) cloud transformation with Cognizant and Google

[會議影片連結](https://www.youtube.com/watch?v=ih6RLCsseN0)
Cotality's (CoreLogic) cloud transformation with Cognizant and Google

## 1. 核心觀點

本次會議主要探討了Quartality（前身為CoreLogic）如何與Cognizant和Google Cloud合作，成功完成雲端轉型，並從中獲得的效益。核心觀點包括：

*   **雲端轉型的必要性：** 解決傳統資料中心帶來的成本高昂、安全風險以及創新受限等問題。
*   **自動化的重要性：** 透過自動化一切流程，降低人為錯誤，提升效率和安全性。
*   **工程文化的轉變：** 從各自為政的開發模式轉向統一的平台和工程文化，促進協作和創新。
*   **AI在營運中的應用：** 利用AI提升營運效率，改善客戶體驗。
*   **安全性的提升：** 透過雲端轉型，實現更強大的安全防護，應對新型安全挑戰。

## 2. 詳細內容

*   **轉型前的挑戰：** Quartality面臨著數十個資料中心、不同的營運模式、高昂的重複成本，以及對是否應該上雲的疑慮。
*   **轉型的目標：** 節省成本、提升安全性、促進創新、簡化和擴展營運。
*   **轉型的原則：** 採用雲原生原則，並強調自動化一切。
*   **營運模式：** 採用集中管理但可擴展和聯合的營運模式，允許成熟的工程團隊自主管理其雲端環境。
*   **計畫執行：** 將雲端轉型視為企業級、高層級的計畫，並聘用專業的雲端計畫經理、技術專案經理和解決方案架構師。
*   **轉型後的效益：** 事件數量減少75%，事件解決時間減少50%，不再需要資料中心，並節省了2500萬美元的年度營運成本。
*   **安全性的提升：** 實現資產的短暫性，能夠在兩週內重新配置整個伺服器環境，並定期進行環境重置。將安全控制和掃描左移至應用程式管道中，在漏洞進入生產環境之前阻止部署。
*   **工程文化的轉變：** 建立統一的工程平台，鼓勵工程師專注於應用程式程式碼，並以使用者為中心設計軟體。
*   **開發者體驗的改善：** 透過內部開發者入口網站Driveway，簡化開發流程，提供標準化的技術、營運視圖和平台服務。
*   **AI在營運中的應用：** 透過Neuro Business Process Workflow，整合不同的系統和應用程式，簡化使用者介面，並利用AI和機器學習模型提升營運效率。
*   **Araya平台：** 展示了基於雲端和AI技術構建的房地產情報平台Araya，為房地產專業人士提供工具和洞察。

## 3. 重要結論

Quartality透過與Cognizant和Google Cloud的合作，成功完成了雲端轉型，不僅節省了大量成本，還提升了安全性、促進了創新，並改善了工程文化和營運效率。這次轉型為Quartality在房地產資料服務領域保持領先地位奠定了堅實的基礎。
