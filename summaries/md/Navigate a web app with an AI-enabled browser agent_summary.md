# Navigate a web app with an AI-enabled browser agent
[會議影片連結](https://www.youtube.com/watch?v=p_dztB1-ECM)
使用 AI 瀏覽器代理導航 Web 應用程式

## 1. 核心觀點

本次會議主要探討如何使用具備 AI 功能的瀏覽器代理來導航 Web 應用程式。核心觀點包括：

*   **Web 代理的定義與運作方式：** Web 代理是一種 AI 代理，用於代表使用者導航和互動網站。其運作流程通常包括接收任務、定義所需執行的動作，以及實際執行這些動作。
*   **Web 代理的應用場景：** 確保 Google Cloud 產品的文件和使用者流程的有效性。透過讓代理理解文件和使用者流程，並自主執行任務，可以顯著提高效率，尤其是在處理大量文件和不同環境、語言時。
*   **Web 代理的優化：** 透過儲存代理在特定領域（如公司票務系統）中執行任務的歷史記錄，並將其應用於微調模型，可以提高代理執行此類任務的能力。
*   **Web 代理系統的關鍵組件：** 包括底層模型、代理環境、驅動引擎和瀏覽器。
*   **基準測試的重要性：** 基準測試對於開發有效的 Web 代理至關重要，它可以進行效能評估、比較不同模型，並透過優化來改進代理。

## 2. 詳細內容

*   **Web 代理的運作迴圈：** 首先將任務和上下文提供給模型，然後使用 Gemini 預測要在頁面上執行的動作。使用瀏覽器自動化函式庫實際與控制台頁面互動。完成後，透過提示 Gemini 來判斷是否已完成該步驟，從而評估該動作。然後，繼續執行下一步或繼續嘗試完成當前步驟。
*   **基準測試的關鍵領域：** 包括任務完成、資訊檢索、使用者互動和導航。
*   **基準測試的關鍵指標：** 在定量指標方面，使用測試成功率、完成時間和步驟數。在定性指標方面，可以使用使用者滿意度、輸出的清晰度和可解釋性。
*   **基準測試的挑戰：** 包括可重複性、通用性、穩健性，甚至定義 Web 代理的成功標準。
*   **淺層驗證：** 透過一系列連結導航，並使用 Gemini 檢測一系列問題。Gemini 的靈活性使得代理能夠輕鬆適應並找到所有問題，包括翻譯版本中意外出現的英文、深色模式下呈現的錯誤，以及文件主題和控制台 UI 之間的不匹配。
*   **Web 代理的實際應用：** 代理接收來自 Cloud Run 快速入門指南的指令，並逐步執行每個步驟。對於每個步驟，它會註釋 Cloud Console 頁面中可互動的元素。然後，使用 Gemini 預測要採取的最佳動作。使用瀏覽器自動化函式庫執行該動作並等待其完成。之後，在代理中使用評估階段來確定是否已完成當前步驟並可以繼續執行下一步。代理能夠成功地從提供的自然語言步驟建立 Cloud Run 服務。

## 3. 重要結論

Web 代理具有廣泛的應用前景，可以自動化許多 Web 應用程式的導航和互動。透過不斷的基準測試和優化，Web 代理可以變得更加高效和可靠。未來，Web 代理有望在更多領域得到應用，例如自動化測試、資料收集和客戶服務。
