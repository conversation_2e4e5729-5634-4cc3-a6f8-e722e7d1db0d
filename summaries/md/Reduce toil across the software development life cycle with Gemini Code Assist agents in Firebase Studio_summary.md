Reduce toil across the software development life cycle with Gemini Code Assist agents in Firebase Studio

[會議影片連結](https://www.youtube.com/watch?v=dQNrL5mglPw)
透過 Gemini Code Assist 代理程式在 Firebase Studio 中減少軟體開發生命週期的繁瑣工作

## 1. 核心觀點

本次會議主要介紹了 Google Cloud 團隊開發的 AI 代理程式，旨在透過消除繁瑣的任務來賦能開發人員，讓他們能夠專注於程式碼編寫。會議重點介紹了兩個代理程式：一個用於理解和聊天程式碼庫，另一個用於確保生成式 AI 返回安全的結果。

## 2. 詳細內容

**程式碼文件代理程式：**

*   該代理程式提供了一種新穎的方式來表示程式碼，從而提供深入的概述，並提供更強大的方式來與程式碼進行互動。
*   它會從程式碼庫建立一個階層式的、類似 Wiki 的程式碼表示。
*   這種表示方式在兩個方面展現價值：首先是文件本身，可以像在任何 Wiki 樣式的文件中一樣瀏覽、展開和摺疊；其次，相同的材料為基於 AI 的聊天提供了出色的上下文。
*   使用者可以放大到相關部分，了解特定項目在更大範圍內的定位，這對於理解大型程式碼庫非常重要，尤其是在有許多貢獻者可能不再參與專案的情況下。
*   改進的聊天體驗會連結到 Wiki 文件的相關部分，使用者可以放大和縮小以查看更大的範圍，並結合 AI 對問題的回答。
*   它還連結到原始程式碼本身，有助於輕鬆理解和採取行動。

**AI 測試代理程式：**

*   該代理程式允許自動進行紅隊演練，並確保發布的內容對目標使用者是安全的。
*   客戶的擔憂通常圍繞確保 AI 系統按照預期的方式運作，並基於相關受眾。
*   例如，可能需要確保應用程式僅討論與兒童相關的主題，或者確保產品不會建立包含潛在冒犯性內容的貼文。
*   AI 對抗性測試是針對希望測試其生成式 AI 實施以確保使用者更安全的應用程式開發人員的解決方案。
*   其運作方式如下：首先，對抗性提示產生器使用未經過濾的模型建立對抗性提示。然後，它將這些提示饋送到 LLM 中。收集這些回應，並根據相關的內容政策對其進行評估，給予通過或失敗的評分。最後，匯總所有資訊並建立一份報告，可以與團隊分享並採取行動。
*   採取行動的方式之一是使用 Model Armor。Model Armor 允許進階功能，例如提示注入保護、凝膠噴霧保護和 AI 防護欄。
*   使用者可以透過 Firebase Studio 或 Gemini Code Assist 觸發 AI 測試代理程式。
*   代理程式會告知使用者它將要執行的操作，並包括它將涵蓋的所有內容政策，例如危險內容、騷擾和仇恨言論。
*   使用者可以點擊以開始。
*   AI 測試評估卡位於看板的「需要採取行動」部分。
*   使用者需要連接模型才能啟動評估。
*   連接模型後，卡片會移至「執行中」部分。
*   代理程式會透明地顯示流程中的每個步驟。
*   它會連接模型、產生提示、評估模型回應，然後開始產生報告。
*   報告準備就緒後，使用者可以在看板上看到結果的重點。
*   使用者可以查看通過和失敗的回應百分比，並點擊以查看更多資訊。
*   完整的報告包含結果摘要、內容政策的視覺表示以及通過或失敗的回應數量。
*   它還包含模型實施的優勢和需要審查的領域。
*   使用者可以過濾內容政策，並下載報告以與團隊分享。
*   使用者還可以點擊進入 Model Armor 以採取行動。

## 3. 重要結論

Google Cloud 團隊開發的 AI 代理程式旨在透過消除軟體開發生命週期中的繁瑣任務來賦能開發人員。程式碼文件代理程式提供了一種新穎的方式來表示程式碼，從而提供深入的概述，並提供更強大的方式來與程式碼進行互動。AI 測試代理程式允許自動進行紅隊演練，並確保發布的內容對目標使用者是安全的。這些工具可以幫助開發人員提高效率，並專注於他們最擅長的事情：編寫程式碼。
