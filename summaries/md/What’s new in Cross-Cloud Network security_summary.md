# What’s new in Cross-Cloud Network security
[會議影片連結](https://www.youtube.com/watch?v=7hAjDvA4fpQ)
Cross-Cloud Network 安全性的新功能

## 1. 核心觀點

本次會議主要介紹了 Cross-Cloud Network 安全性的最新進展，重點關注兩個核心使用案例：保護分散式應用程式和保護全球前端應用程式。Google Cloud 強調在現有資料路徑上提供保護，同時避免增加網路複雜性。產品組合圍繞三大主題：實現強大的姿態管理、提供進階威脅防護，以及促進開放生態系統。

## 2. 詳細內容

針對分散式應用程式，Google Cloud 提供了一系列全託管安全產品，讓使用者無需更改網路設計即可應用必要的安全控制。Cloud Next Generation Firewall 是一個全託管的分散式防火牆，內建於網路結構中，可直接在每個工作負載旁邊提供保護。主要增強功能包括使用網路類型進行規則設定，以及使用單一標籤來標記整個組織中的實例，然後編寫基於標籤的防火牆規則，以將特定控制應用於所有標記的實例。此外，還宣布了網路安全整合，允許使用者將經過認證的合作夥伴產品連接到防火牆結構中。Cloud NAT 也進行了更新，例如將 NAT 擴展到混合連線和 NAT64，使 IPv6 來源能夠連線到 IPv4 位址。

對於需要雲原生出口控制的使用者，全託管的安全網路代理 SWIP 是一個理想的選擇。SWIP 的主要增強功能包括將 SWIP 用作透明插入的下一個樞紐，無需任何程式碼變更，以及 VPC 服務控制相容性。

Google Cloud 還宣布了 DNS Armor，這是一項新的雲原生服務，旨在保護使用者免受 DNS 攻擊，預計在今年下半年推出。它結合了 InfoBlocks 的進階 DNS 威脅偵測技術和 Google Cloud 原生產品體驗，提供全託管的簽名更新，並與 Logging、Security Command Center 和 Google SecOps 整合。

針對全球前端應用程式，Google 的全球前端解決方案讓使用者能夠交付、擴展和保護面向網際網路的 Web 服務，無論這些服務託管在 Google Cloud、其他雲端，甚至是在地端。這是透過負載平衡器和 CDN 的全球基礎架構實現的。會議涵蓋了四個產品的創新。

Cloud Armor 是 Google Cloud 的 DDoS 保護和 Web 應用程式防火牆。透過推出分層安全策略，簡化了安全管理，這意味著使用者可以在組織層級建立安全策略，然後將其繼承到下面的資料夾和專案中。這讓使用者能夠為整個組織建立一致的保護。其他增強功能包括新的 DDoS 儀表板和對位址群組的支援。

ReCAPTCHA Enterprise 是 Google Cloud 的欺詐和濫用保護服務。新的亮點包括網路防禦、無需任何應用程式程式碼變更的欺詐機器人保護，以及用於偵測和防止 SMS 欺詐的 SMS 防禦。

服務擴充功能提供了一種開放生態系統，可以與 Google Cloud 的安全合作夥伴整合，或為使用者的特定需求自訂資料平面。一個新功能，外掛程式，讓使用者可以使用 WebAssembly、RASM 將自訂程式碼直接插入到負載平衡器資料路徑中。它也針對低延遲進行了最佳化。

最後，Google Cloud 推出了一個新產品 Model Armor，提供多模型、多雲端支援，以保護 AI 工作負載。它可以降低資料洩漏、冒犯性內容和提示注入攻擊等風險。它內建了與 Vertex AI 的整合，並即將推出負載平衡器服務擴充功能。

## 3. 重要結論

Google Cloud 持續增強 Cross-Cloud Network 的安全性，透過提供全託管服務、簡化安全管理、促進開放生態系統，協助使用者保護分散式應用程式和全球前端應用程式，應對不斷變化的網路安全挑戰。
