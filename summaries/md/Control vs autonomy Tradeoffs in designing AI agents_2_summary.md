# Control vs autonomy Tradeoffs in designing AI agents_2
[會議影片連結]()
Control vs autonomy Tradeoffs in designing AI agents_2

控制與自主性在設計AI代理中的權衡

## 1. 核心觀點

本次會議主要分享了在 Google Cloud 上開發並部署 AI 代理的經驗，特別是關於如何利用 Google 開源的 Agent Development Kit (ADK)。會議重點在於展示實際案例，包括雷諾汽車和 VML 如何使用 ADK 構建用於電動車充電項目和新型營銷服務的代理。此外，Google Applied AI 工程團隊也分享了他們在 Trusted Tester Program 中幫助客戶使用 ADK 的經驗，包括框架的易用性、Gemini 的整合以及開發環境的優勢。

## 2. 詳細內容

會議首先介紹了 ADK，這是一個 Google 發布的開源框架，旨在幫助全球企業開發 AI 代理。雷諾汽車分享了他們選擇 ADK 來構建用於電動車充電項目的複雜多代理系統的過程，並強調了他們在選擇 ADK 之前對比了業界其他框架的經驗。VML 則分享了他們多年來在 Agent TKI 方面的探索，最終使用 ADK 構建了一個多代理系統，該系統旨在顛覆他們自己的業務模式，提供由 AI 代理驅動的全新服務。VML 強調了迭代開發代理、建立強大的評估框架以及最終發布代理的過程。Google Applied AI 工程團隊分享了他們在 Trusted Tester Program 中幫助客戶使用 ADK 的經驗，包括架構模式、Gemini 的整合以及開發環境的易用性。他們還提到了 Vertex AI 中的 Agent Garden，用戶可以在其中找到開發代理的範例並開始使用。

## 3. 重要結論

本次會議強調了在 Google Cloud 上使用 ADK 開發和部署 AI 代理的實際經驗。雷諾汽車和 VML 的案例展示了 ADK 在不同行業和應用中的潛力。Google Applied AI 工程團隊的分享則強調了 ADK 的易用性和 Gemini 的整合，使其成為開發 AI 代理的有力工具。會議鼓勵大家嘗試這個新的開源框架，並期待為企業構建 AI 代理提供支持。
