# Best practices for monitoring Vertex AI models
[會議影片連結](https://www.youtube.com/watch?v=2bBKMTM8lBE)
監控 Vertex AI 模型的最佳實踐

## 1. 核心觀點

本次會議主要講解了監控 Vertex AI 模型以確保 AI 應用程式使用者獲得快速效能和成功工作流程的最佳實踐。重點涵蓋了監控的定義、Vertex AI 模型的範圍、實際場景演示、以及如何使用和客製化監控儀表板和警示。

## 2. 詳細內容

*   **定義與範圍：**
    *   **監控：** 理解速率、錯誤和持續時間指標，以掌握模型的速度、正常運行時間和成本。
    *   **Vertex AI 模型：** 主要關注模型花園中的託管模型，例如 Gemini 和 Anthropic Claude。

*   **場景演示：**
    *   以花農為例，展示如何使用 Gemini API 自動生成產品描述。
    *   透過程式碼演示，展示如何使用 Vertex AI 查詢 Gemini API，並獲取產品描述。

*   **Vertex AI 監控儀表板：**
    *   介紹了 Vertex AI 提供的開箱即用監控儀表板，可從 Vertex AI 或 Cloud Monitoring 訪問。
    *   儀表板提供模型調用速率、延遲、token/字元吞吐量等指標。
    *   可以按模型和位置查看資料，並深入分析各個指標。

*   **指標分析：**
    *   **吞吐量：** 顯示每秒模型請求數（QPS）和 token 吞吐量。
    *   **延遲：** 顯示首次 token 延遲和模型調用總延遲。
    *   **錯誤：** 顯示不同類型的錯誤（例如 400、403、429、500）的速率和百分比。
    *   **Token/字元：** 區分新舊模型，新模型以 token 計費，舊模型以字元計費。
    *   **佈建吞吐量：** 顯示使用量與購買配額的比率，適用於預先佈建吞吐量的模型。

*   **客製化儀表板：**
    *   可以複製預設儀表板並進行客製化，例如調整大小、刪除不相關的圖表、新增計分卡等。
    *   建議在儀表板中嵌入團隊文件和故障排除建議，方便應急人員快速處理問題。
    *   可以從頭開始建立儀表板，並將 Vertex AI 指標與其他 Google Cloud 資源的指標結合顯示。
    *   所有儀表板都可以 JSON 格式匯出和匯入，方便在不同專案之間共享。
    *   所有指標都寫入 Google 的時間序列資料庫，可以使用 Monitoring API 從 Grafana 等其他視覺化工具查詢。

*   **警示設定：**
    *   Vertex AI 提供開箱即用的警示，例如 429 請求過多。
    *   可以建立自訂警示，並在警示訊息中包含詳細的處理說明。

*   **其他：**
    *   提及了 Cloud Trace 中 GenAI 追蹤功能，可以追蹤單個請求的詳細資訊，包括系統提示和使用者提示。
    *   展示了 Cloud Monitoring 的黑暗模式。

## 3. 重要結論

監控 Vertex AI 模型對於確保 AI 應用程式的效能、可靠性和成本效益至關重要。Vertex AI 提供了豐富的監控工具和指標，可以幫助使用者快速發現和解決問題。透過客製化儀表板和設定警示，可以進一步提升監控效率。
