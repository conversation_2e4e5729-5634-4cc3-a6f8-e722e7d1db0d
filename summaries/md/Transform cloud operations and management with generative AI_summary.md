# Transform cloud operations and management with generative AI
[會議影片連結](https://www.youtube.com/watch?v=_uAzRxTlq8U)
運用生成式 AI 轉型雲端運營與管理

## 1. 核心觀點

本次會議主要探討如何利用生成式 AI（Generative AI）來轉型雲端運營與管理。核心觀點包括：

*   **AI 的定位：** AI 不是獨立存在的「東西」，而是使現有「東西」（例如應用程式或平台）變得更好的工具，如同醬料或香料之於菜餚。
*   **應用程式中心管理：** 強調以應用程式為中心，而非以專案為中心來管理雲端環境，以便更全面地了解應用程式的成本、健康狀況和效能。
*   **Gemini Cloud Assist 的應用：** 介紹 Gemini Cloud Assist 如何透過自然語言聊天、智慧操作和調查功能，協助使用者設計、操作和優化雲端環境。
*   **新工具的整合：** 介紹 App Hub、Application Design Center、Cloud Hub、Application Monitoring 和 Cost Explorer 等新工具，以及它們如何與 Gemini Cloud Assist 整合，以提供更強大的雲端管理能力。
*   **AI 輔助的故障排除：** 強調利用 AI 提供的上下文資訊，加速故障排除流程，縮短平均修復時間（MTTR）。

## 2. 詳細內容

*   **Bobby 的開場：** Bobby 以自身夏季旅行的災難性經歷為例，說明企業在追求宏大目標時可能遇到的挑戰，並強調不應為了最終目標而合理化痛苦的過程。他提出三個「Bobby-isms」：
    *   並非所有新的事物都是好的，也並非所有舊的事物都是壞的。
    *   盡可能學習新知識，但不要忘記已知的知識。
    *   AI 不是「東西」，而是使「東西」變得更好的工具。
*   **Ryan 介紹 Gemini Cloud Assist：** Ryan 具體介紹 Gemini Cloud Assist 的功能，包括：
    *   **自然語言聊天：** 在雲端控制台和行動應用程式中使用自然語言與 AI 互動，獲取產品知識、配置說明和最佳實踐。
    *   **智慧操作：** 在控制台中提供便捷的操作，簡化雲端工具的使用。
    *   **Cloud Assist 調查：** 從 Gemini 聊天面板或日誌條目啟動調查，快速找到問題根源。
    *   **與 Application Design Center 整合：** 使用自然語言表達設計意圖，加速應用程式設計流程。
    *   **實際範例：** 展示如何使用 Gemini Cloud Assist 查詢 VM 數量、CPU 使用率、成本建議和服務健康狀況，以及如何使用自然語言查詢日誌、生成 G Cloud 命令和 Terraform 程式碼。
*   **Afreena 介紹應用程式中心管理：** Afreena 強調需要超越以專案為中心的管理方式，轉向以應用程式為中心，以便更全面地了解應用程式的成本、健康狀況和效能。她介紹了以下工具：
    *   **App Hub：** 用於定義應用程式的正式身份，了解應用程式的架構和業務目標。
    *   **Application Design Center：** 用於建立應用程式基礎架構範本，並與 Gemini Cloud Assist 整合，使用自然語言迭代設計。
    *   **Cloud Hub：** 作為一個指揮中心，集中顯示應用程式的運營、健康狀況和成本相關數據。
    *   **Application Monitoring：** 將日誌、指標和追蹤數據按應用程式分組，方便監控和故障排除。
    *   **Cost Explorer：** 用於分析應用程式的成本，進行精細的報告和成本分攤。
    *   **Gemini 調查代理：** 透過分析日誌條目，提出相關的觀察和假設，協助使用者快速找到問題根源。
*   **總結與展望：** 會議最後，講者鼓勵大家參與 Trust Your Tester 計畫，並參加後續的深度研討會，以更深入地了解這些工具和功能。

## 3. 重要結論

本次會議強調了生成式 AI 在雲端運營與管理中的變革性作用。透過 Gemini Cloud Assist 和應用程式中心管理等工具，企業可以更有效地設計、操作和優化雲端環境，降低成本，提高效率，並加速故障排除流程。會議也強調了 AI 的定位，即作為一種增強現有工具和流程的手段，而非獨立存在的解決方案。
