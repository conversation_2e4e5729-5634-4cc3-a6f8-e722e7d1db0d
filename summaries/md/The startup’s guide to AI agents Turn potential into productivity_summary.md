# The startup’s guide to AI agents Turn potential into productivity
[會議影片連結](https://www.youtube.com/watch?v=SEfqGv_b5mk)
新創公司的人工智慧代理指南：將潛力轉化為生產力

## 1. 核心觀點

本次會議主要探討了人工智慧代理（AI agents）的定義、應用、投資回報（ROI）、建構方法以及對新創公司和更廣泛生態系統的影響。核心觀點包括：

*   **AI 代理的定義：** 從廣義上講，AI 代理是基於大型語言模型（LLM）的軟體，具有工具存取權限，並能根據設定的自主程度解決複雜或簡單的問題。
*   **ROI 的重要性：** 採用 AI 代理的目標是創造實際的商業價值，例如提高收入、增加使用率或降低成本。對於傳統企業而言，未能採用 AI 代理可能構成生存風險。
*   **建構 AI 代理的步驟：** 包括明確用例、與 LLM 進行有效溝通、簡化設計、進行測試，並建立防護措施。
*   **新創公司的機會：** 相較於大型企業，新創公司在決定 AI 代理的自主程度方面擁有更大的彈性和機會。
*   **未來趨勢：** 未來 AI 代理的發展重點將放在使用者體驗（UX）的改善，以及建立使用者對 AI 代理的信任感。

## 2. 詳細內容

*   **AI 代理的定義：**
    *   AI 代理是一種軟體，它基於 LLM，並具有工具存取權限。
    *   自主程度是關鍵，公司可以決定代理在解決問題時的自主性。
    *   AI 代理可以解決複雜或簡單的問題。
    *   聊天機器人（Chatbots）可能被視為 AI 代理，但通常處理更簡單、線性的回應。
    *   更技術性的定義是，AI 代理的自主性越高，LLM 決定應用程式內部控制流程的程度就越高。
    *   重要的是要區分基於 LLM 的代理和傳統的電腦科學代理。
    *   Gartner 認為，AI 代理打破了炒作週期（hype cycle）的概念。
    *   AI 系統可以被建模為圖形，其中最基本的代理是具有工具存取權限的提示（prompt）。
    *   更高層次的代理可以控制執行流程、存取工作流程之外的系統，甚至跨多個工作流程進行互動。

*   **ROI 的考量：**
    *   AI 代理應被視為一種軟體，旨在創造實際的商業價值，例如提高收入、增加使用率或降低成本。
    *   對於傳統企業而言，未能採用 AI 優先的代理方法可能構成生存風險。
    *   評估 AI 代理的 ROI 時，應考慮其是否能建立新的工作流程，或增強現有的工作流程。
    *   投資實驗基礎設施，以便比較 AI 代理與現有工作流程的效能。
    *   AI 代理可以提高工程師的生產力，但也可能導致他們工作時間減少。
    *   客戶支援是一個容易量化 ROI 的領域，例如透過降低客戶流失率。
    *   ROI 不僅僅是降低成本，還包括人才競爭力和員工滿意度。
    *   最終目標是在收入方面實現 ROI，例如透過更快的決策和更高的客戶參與度。
    *   AI 代理還可以提高市場佔有率。

*   **建構 AI 代理：**
    *   第一步是專注於要建構的用例。
    *   如果可以寫下人類執行任務的逐步步驟，那麼這個任務就很適合透過 AI 代理自動化。
    *   與 LLM 進行有效溝通至關重要，就像指導聰明的實習生一樣。
    *   從簡單開始，然後根據需要增加複雜性。
    *   測試至關重要，因為使用者可能會以意想不到的方式使用解決方案。
    *   需要真正的工程團隊和主題專家來建構有效的 AI 代理。
    *   可以將問題建模為工作流程，並將其分解為一系列具有功能的步驟。
    *   AI 開發應採用測試驅動開發方法，包括實驗、評估、部署和監控。
    *   AI 是非線性的，需要跨職能的利益相關者參與開發過程。

*   **採用和整合：**
    *   許多公司都處於實驗階段，未能實現規模化。
    *   在概念驗證（proof of value）階段，必須著眼於規模化驗證（proof of scale）。
    *   及早引入治理（governance），以便擴大規模。
    *   公司必須決定是採用點解決方案還是平台。
    *   採用通常受到公司哲學的驅動。
    *   員工對 AI 代理可能帶來的影響感到恐懼。
    *   從簡單的用例開始，並逐步建立信心。
    *   AI 代理可能會取代部分知識工作，但也可能創造更多工作。
    *   簡化的使用者介面和直觀的工作流程對於企業採用至關重要。

*   **安全性和標準：**
    *   安全對於 AI 代理的採用至關重要。
    *   企業已經在安全系統上進行了大量投資，AI 代理應與這些系統整合。
    *   避免對特定技術或雲端供應商產生過度依賴。
    *   最大的障礙仍然是建構能夠可靠運作的 AI 代理。
    *   可靠性、延遲和成本是主要問題。
    *   在解決可靠性問題後，安全和成本問題將會浮出水面。
    *   協議很重要，因為代理需要存取工具。
    *   LLM 仍然會在基本任務中犯錯。
    *   需要可追溯性，以便快速恢復。
    *   點解決方案可能會加劇安全問題。
    *   必須考慮客戶隱私。

*   **AI 代理的未來：**
    *   AI 代理正在重新定義商業模式。
    *   對於新創公司而言，這是一個令人興奮的時代，他們可以顛覆傳統企業。
    *   使用者體驗是 AI 代理未來發展最有趣的領域。
    *   信任是新創公司可以建立的護城河。

## 3. 重要結論

AI 代理正在迅速發展，並為新創公司和傳統企業帶來了巨大的機會和挑戰。為了成功採用 AI 代理，企業需要專注於明確用例、建立可靠的系統、解決安全問題，並優先考慮使用者體驗。此外，建立使用者對 AI 代理的信任感至關重要。
