# AI in action Optimize your AI infrastructure
[會議影片連結](https://www.youtube.com/watch?v=UEM4XMPKB_g)
AI in action 最佳化您的 AI 基礎架構

## 1. 核心觀點

本次會議主要探討了 Google Cloud 的 AI Hypercomputer 如何幫助企業最佳化其 AI 基礎架構，並分享了 Moloco、Shopify 和 UAE's Technology Innovation Institute 等公司利用 Google Cloud 解決方案的實際案例。核心觀點包括：

*   **AI Hypercomputer 的架構與優勢：** 透過整合硬體、軟體和動態工作負載排程器，提供端到端的 AI 解決方案，提高效率和靈活性。
*   **Google Cloud 在 AI 領域的領先地位：** 強調 Google 在 AI 研究和應用方面的深厚積累，以及如何將這些經驗應用於 Google Cloud 服務。
*   **企業如何利用 Google Cloud 擴展 AI 能力：** 分享了不同企業在模型訓練、推論和資料處理等方面使用 Google Cloud 的經驗，以及如何實現效能提升和成本降低。
*   **持續最佳化的重要性：** 強調 AI 領域的快速發展，企業需要不斷調整和最佳化其 AI 基礎架構，以保持競爭力。

## 2. 詳細內容

*   **AI Hypercomputer 架構：**
    *   **AI 基礎架構層：** 提供專為 AI 工作負載設計的硬體，包括運算、儲存和網路。
    *   **最佳化軟體層：** 透過軟體最佳化，更有效地利用硬體資源，簡化 AI 開發和部署流程。
    *   **動態工作負載排程器：** 根據需求動態分配資源，提高資源利用率。

*   **Moloco 的案例：**
    *   利用 Google Cloud 快速擴展規模，滿足不斷增長的需求。
    *   使用 Dataflow 進行資料處理和特徵工程。
    *   使用 TPU 進行模型訓練，GPU 進行模型推論。
    *   使用 Google Kubernetes Engine (GKE) 管理 AI 管道。
    *   從 CPU 遷移到 TPU 後，效能提升 10 倍。

*   **Shopify 的案例：**
    *   長期使用機器學習，並將 AI 融入到產品開發的基礎中。
    *   使用視覺語言模型 (VLM) 統一產品元資料提取管道。
    *   使用 Dataflow 進行串流資料處理。
    *   使用 GKE 託管模型推論服務，並使用 NVIDIA 硬體加速。
    *   強調在模型品質、推論框架和硬體成本之間取得平衡。

*   **UAE's Technology Innovation Institute 的案例：**
    *   在 AI Hypercomputer 上訓練 Falcon LLM。
    *   透過與 Google 團隊合作，最佳化儲存、網路和訓練流程。
    *   從其他雲端供應商遷移到 Google Cloud 後，發現並解決了許多效率問題。
    *   使用 Parallel Store 解決隨機存取資料集的問題。
    *   使用佇列系統和動態工作負載排程器，有效地共享資源。
    *   透過最佳化，模型訓練時間縮短 25%，總體擁有成本 (TCO) 降低 20%。

*   **最佳化策略：**
    *   **量化：** 使用較低的精度（例如 Int8）來減少模型大小和提高推論速度。
    *   **平行處理：** 使用 3D 平行處理等技術來加速模型訓練。
    *   **快取：** 使用快取來減少延遲，提高效能。
    *   **非同步檢查點：** 使用非同步檢查點來減少訓練中斷的風險。
    *   **持續監控和調整：** 根據實際情況不斷調整和最佳化 AI 基礎架構。

## 3. 重要結論

Google Cloud 的 AI Hypercomputer 提供了一個強大的平台，幫助企業構建、部署和最佳化其 AI 應用。透過整合硬體、軟體和服務，AI Hypercomputer 能夠提高效率、降低成本，並加速 AI 創新。Moloco、Shopify 和 UAE's Technology Innovation Institute 等公司的案例表明，透過與 Google Cloud 合作，企業可以充分利用 AI 的潛力，實現業務目標。然而，AI 領域的快速發展意味著企業需要不斷學習和調整，才能保持領先地位。
