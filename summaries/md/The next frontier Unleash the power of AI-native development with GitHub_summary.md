# The next frontier Unleash the power of AI-native development with GitHub

[會議影片連結](https://www.youtube.com/watch?v=HqD6AUoB5eo)
下一個前沿：利用 GitHub 釋放 AI 原生開發的力量

## 1. 核心觀點

本次會議主要探討了 AI 原生開發的未來，以及 GitHub 如何透過 AI 技術，特別是 Copilot，來改變軟體開發生命週期（SDLC）。核心觀點包括：

*   **AI 原生開發的定義：** 強調 AI 應深入系統核心，而非僅是輔助工具，如同母語般自然流暢。
*   **GitHub 的願景：** 讓每個人都能創造軟體，確保軟體預設具備高品質、安全性和可擴展性，並讓 AI 在軟體的建構和運作中扮演關鍵角色。
*   **Copilot 的演進：** 從 AI 輔助（AI-infused）轉向 AI 原生（AI-native），成為開發過程中的積極協作者。
*   **Project Padawan：** 將 Copilot 整合到 GitHub 的每個儲存庫，使其成為每個專案的貢獻者。
*   **Copilot Code Review：** 提供即時程式碼審查，協助開發者更快交付更穩健的軟體。
*   **Verily 的 VIDA：** 一個 VS Code 插件，利用 Copilot 執行特定指令，將開發者留在 IDE 內，提高效率。

## 2. 詳細內容

*   **GitHub 的世界觀：**
    *   任何人都能創造軟體，無論年齡或經驗。
    *   軟體必須預設具備可擴展性、高品質和安全性。
    *   AI 將在軟體的建構和運作中扮演重要角色。

*   **AI 原生 vs. AI 輔助：**
    *   AI 輔助系統（如 Siri）使用 AI 來增強現有功能，但並非系統的核心。
    *   AI 原生系統將 AI 融入核心，更流暢、更能預測和執行任務。

*   **GitHub 的 AI 原生策略：**
    *   將 GitHub 從協作平台擴展到整個 SDLC，包括程式碼建立、協作和雲端託管。
    *   透過代理層（agentic layer）連接開發流程的內外迴圈，讓 AI 代理與開發者協同工作。
    *   Copilot 將主動參與開發過程，在適當的時間提供協助。

*   **Copilot 的 Agent Mode：**
    *   Copilot 不僅迭代自身的輸出，還迭代輸出的結果，直到完成任務。
    *   支援多種模型，包括 Anthropic 的 Sonnet 和 Google 的 Gemini 2.0。

*   **Project Padawan：**
    *   將 Copilot 作為貢獻者加入 GitHub 的每個儲存庫。
    *   開發者可以將 issue 指派給 Copilot，Copilot 將產生經過完整測試的 pull request。

*   **Copilot Code Review：**
    *   提供即時程式碼審查，找出錯誤、強制執行最佳實務，並提供可行的改進建議。
    *   協助開發者更快交付更穩健的軟體。

*   **Verily Developer Assistant (VIDA)：**
    *   一個 VS Code 插件，允許開發者在 IDE 內執行指令，並與 Copilot 互動。
    *   VIDA 可以從 JIRA issue 提取需求，並使用 Copilot 產生實作計畫。
    *   VIDA 也可以用來解釋 Terraform stack 將建立的權限，或產生架構分解的 schema。

## 3. 重要結論

GitHub 正在積極擁抱 AI 原生開發，透過 Copilot 和其他 AI 工具，賦予開發者更強大的能力，加速軟體開發流程，並提高軟體品質。GitHub 的目標是讓每個人都能參與軟體開發，並讓 AI 成為開發過程中的重要夥伴。Verily 的 VIDA 展示了如何利用 Copilot 建立高度客製化的開發工具，進一步提升開發效率。GitHub 鼓勵開發者積極嘗試這些新工具，並共同塑造 AI 原生開發的未來。
