Start secure, stay secure Build a strong cloud security foundation
[會議影片連結](https://www.youtube.com/watch?v=MvoLvTZiNAk)
安全啟動，持續安全，建立強大的雲端安全基礎

## 1. 核心觀點

本次會議主要探討 Google Cloud 提供的基礎安全控制，以及 Google 如何將安全方案打包，協助使用者有效管理風險。核心觀點包括：

*   **AI 時代雲端安全的重要性：** 隨著 AI 技術的快速發展，雲端採用率不斷提高，雲端安全變得至關重要。
*   **客戶對雲端安全的三大需求：** 主動威脅防護、平衡雲端創新與合規需求、以及獲得客製化的安全指導。
*   **Google 的共享信任模型：** Google 不僅提供基礎設施，更積極與客戶合作，共同管理雲端安全和風險。
*   **Security Foundation 的核心價值：** 提供一套精心策劃的產品和功能，為 Google Cloud 環境提供堅實的保護。
*   **Security Blueprints 的加速作用：** 提供可公開下載的指南和程式碼，協助使用者快速部署安全系統，並符合多種法規標準。
*   **DBS 銀行在雲端安全上的實踐：** 分享了 DBS 銀行在 Google Cloud 上的雲端安全策略演進，以及如何平衡風險控制與開發效率。
*   **Google 的風險保護計畫：** 透過與網路保險公司的合作，提供客戶風險轉移機制，管理技術無法完全緩解的殘餘風險。

## 2. 詳細內容

*   **AI 時代的雲端安全挑戰：**
    *   **主動威脅防護：** 防禦 DDoS 攻擊、供應鏈攻擊、零日漏洞攻擊，以及針對 AI 模型的威脅。
    *   **平衡創新與合規：** 在採用最新雲端技術的同時，滿足合規和監管標準。
    *   **客製化指導：** 針對不同組織的需求，提供量身定制的安全建議。

*   **Security Foundation 的組成：**
    *   **身份與存取管理 (IAM)：** 確保適當的人員和工作負載可以存取正確的資料和資源。
    *   **資料保護：** 保護靜態、傳輸中和使用中的敏感資料。
    *   **邊界防護：** 透過網路安全在網路層級偵測和阻擋威脅。
    *   **安全監控與態勢管理：** 監控雲端環境、記錄活動，並及早發現威脅和漏洞。
    *   **主權與合規：** 確保符合法規要求，同時不影響雲端創新。

*   **Security Foundation 的關鍵產品和功能：**
    *   **VPC Service Controls：** 建立安全區域，防止資料外洩。
    *   **Cloud Armor：** 提供 DDoS 防護和 Web 應用程式防火牆功能。
    *   **Assured Workloads：** 在全球區域執行受監管的工作負載，同時保持雲端創新。
    *   **Sensitive Data Protection：** 識別、分類和保護敏感資料。

*   **Security Foundation 的應用場景：**
    *   **AI 和 GenAI：** 保護 AI 工作負載，並利用 AI 驅動的安全功能。例如，AI Protection Platform 和 Model Armor。
    *   **基礎架構現代化：** 保護虛擬機器和容器。
    *   **應用程式現代化：** 在生命週期早期發現和修復錯誤配置和漏洞。
    *   **資料與分析：** 提供端到端的保護，從強大的資料治理到細緻的存取控制。

*   **Security Blueprints 的實作：**
    *   **分層方法：** 建立在預設安全性的基礎上，提供基礎藍圖和平台藍圖，加速業務目標的達成。
    *   **三大支柱：** 指南、程式碼和生命週期。
    *   **法規合規：** 符合 NIST、CIS 和 CSA 標準。
    *   **垂直領域無關：** 模組化設計，可根據需求進行調整。
    *   **具體藍圖：** 基礎藍圖、開發人員藍圖、GenAI 藍圖和資料網格藍圖。

*   **DBS 銀行在雲端安全上的經驗分享：**
    *   **風險接受模型：** 從一開始就限制開發人員的權限，逐步增加風險承受能力。
    *   **Evolved 框架：** 建立包含操作最佳實務、架構最佳實務和控制項的服務包。
    *   **Evolved Light 框架：** 透過透明代理掃描 API 呼叫，在提供開發人員自由的同時，確保安全性。
    *   **未來展望：** 透過 AI 和 GenAI 技術，實現更自動化、更少人工干預的安全運營。

*   **Google 的風險保護計畫：**
    *   **網路保險中心：** 協助使用者衡量和管理 Google Cloud 上的風險，並與網路保險公司分享資訊。
    *   **DDoS 保護計畫：** 為 Cloud Armor Enterprise 客戶提供主動支援和財務補償。
    *   **加密貨幣挖礦保護計畫：** 為 Security Command Center 客戶提供高達一百萬美元的補償，以應對未偵測到的加密貨幣挖礦攻擊。
    *   **與網路保險公司合作：** 與 Beasley、Chubb 和 Munich Re 合作，擴大計畫的覆蓋範圍，並提供更全面的保障。
    *   **保單更新：** 明確涵蓋生成式 AI 和量子風險。

## 3. 重要結論

本次會議強調了在 AI 時代建立強大雲端安全基礎的重要性。Google 透過 Security Foundation、Security Blueprints 和風險保護計畫，提供全面的解決方案，協助客戶應對不斷變化的安全挑戰。DBS 銀行分享的實踐經驗，以及 Google 與網路保險公司的合作，進一步展示了 Google 在雲端安全領域的領導地位。
