Oracle and Google Cloud Design for the future with advanced analytics and AI

[會議影片連結](https://www.youtube.com/watch?v=4KBFP58tbHQ)
Oracle 與 Google Cloud 透過先進分析和 AI 設計未來

## 1. 核心觀點

本次會議主要討論了如何利用 Oracle 資料庫的資料，結合 Google Cloud 的分析和 AI 能力，為企業帶來更深入的商業洞察。核心觀點包括：

*   結合 Oracle 的資料和 Google 的 AI，可以從各種來源的資料中提取商業營運洞察。
*   在 Google Cloud 上直接運行 Oracle 資料庫，可以靈活選擇資料庫版本，並利用現有技術進行遷移和管理。
*   Google Cloud 的 HyperDisk 提供了高吞吐量和 IOPS，解決了 Oracle 資料庫的儲存瓶頸問題。
*   利用 Google 的原生解決方案，如 DataStream，可以將各種資料來源的資料整合到 BigQuery 中，進行進階分析。
*   Sabre 成功案例展示了在 Google Cloud 上運行 Oracle 資料庫，並利用 CI/CD 流程和 Looker 進行分析的優勢。

## 2. 詳細內容

會議首先強調了 GenAI 對企業的轉型作用，並指出分析在其中扮演的重要角色。傳統的分析方法僅限於從資料庫中提取資料，而現在可以結合來自公共來源和其他媒體的資料，利用 AI 驅動更深入的商業洞察。例如，行動餐車可以利用 AI 分析新鞋發布等事件，預測人潮，從而提高收益。

會議接著介紹了在 Google Cloud 上運行 Oracle 資料庫的優勢。Google Cloud 提供了標準的架構，可以在 Compute Engine 上部署 VM，並安裝任何版本的 Oracle 資料庫，即使是較舊的版本，如 11.2。可以使用 Data Guard 或 Golden Gate 等標準技術進行遷移，並使用 Enterprise Manager 進行監控和管理。Google Cloud 提供了高達 192 個 vCPU 的 VM 實例，以及高達 10 GB/s 的吞吐量，可以滿足極端規模的 Oracle 資料庫需求。

HyperDisk 是 Google Cloud 提供的一種儲存解決方案，可以解耦效能和磁碟大小的關係。即使是 1TB 的磁碟，也可以提供高達 2.4 GB/s 的吞吐量和 160,000 IOPS。可以根據需求隨時增加吞吐量和效能，而無需停機。

會議還介紹了如何利用 Google 的原生解決方案，如 DataStream，將各種資料來源的資料提取到 BigQuery 中，進行進階分析。

Sabre 是一家領先的旅遊科技公司，在 Google Cloud 上運行著超過 1000 個 Oracle 資料庫。這些資料庫支援航空公司的報到、票務、預訂等高效能工作負載。Sabre 透過建立完整的 CI/CD 流程，實現了快速部署和迭代。他們還利用 Looker 對資料庫進行營運分析，隨時了解資料庫的狀態。

## 3. 重要結論

本次會議強調了結合 Oracle 資料庫和 Google Cloud 的分析和 AI 能力，可以為企業帶來更深入的商業洞察和競爭優勢。Google Cloud 提供了靈活、高效、可擴展的平台，可以滿足各種規模的 Oracle 資料庫需求。Sabre 的成功案例證明了這種結合的可行性和優勢。企業可以利用 Google Cloud 的原生解決方案，如 DataStream 和 BigQuery，進行進階分析，並利用 CI/CD 流程和 Looker 進行營運分析，從而提高效率和降低成本。
