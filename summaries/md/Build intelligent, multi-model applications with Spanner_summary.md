Build intelligent, multi-model applications with Spanner

[會議影片連結](https://www.youtube.com/watch?v=8RXnNgcEp3o)
使用 Spanner 構建智慧型、多模型應用程式

## 1. 核心觀點

本次會議主要介紹了 Google Spanner 如何幫助客戶構建智慧型、多模型應用程式，解決傳統資料庫在擴展性、一致性和多樣性方面的挑戰。核心觀點包括：

*   Spanner 是一個無須妥協的現代應用程式資料庫，提供高可用性、無限擴展性和全球一致性。
*   Spanner 已經發展成為一個多模型平台，支援關聯式、圖形、向量和全文檢索等多種資料模型，並且可以無縫協同工作。
*   Spanner 提供了彈性定價和地理分區等功能，以滿足不同客戶的需求。
*   Goldman Sachs 和 Walmart 分享了他們使用 Spanner 的實際案例，展示了 Spanner 在金融服務和零售行業的應用。
*   Spanner 提供了豐富的工具和服務，簡化了應用程式和資料的遷移過程。

## 2. 詳細內容

*   **傳統資料庫的挑戰：** 傳統資料庫在應用程式需要大規模擴展時面臨瓶頸，擴展成本高昂，且存在單點故障的風險。複製和分片等方法增加了操作複雜性，並可能導致資料不一致。
*   **Spanner 的解決方案：** Spanner 是一個全球分散式、可擴展且具備強一致性的資料庫，可以解決傳統資料庫的挑戰。它提供 5 個 9 的可用性，並且可以線性擴展，沒有任何操作負擔。
*   **Spanner 的多模型能力：** Spanner 不僅僅是一個關聯式資料庫，它還支援圖形、向量和全文檢索等多種資料模型。這些資料模型可以無縫協同工作，使得開發者可以輕鬆構建智慧型應用程式。
*   **Goldman Sachs 的案例：** Goldman Sachs 使用 Spanner 來構建其全球交易分類帳，該分類帳需要處理複雜的交易關係和一致性要求。Spanner 能夠提供全球一致的交易視圖，並簡化操作。
*   **Walmart 的案例：** Walmart 使用 Spanner 來支援其支付平台，該平台需要處理大量的交易並保證低延遲。Spanner 能夠提供高可用性和可擴展性，滿足 Walmart 的需求。
*   **Spanner 的新功能：** Spanner 最近推出了許多新功能，包括彈性自動擴展、分層儲存、地理分區和與 BigQuery 的整合。這些功能使得 Spanner 更加靈活和強大。
*   **Spanner 的遷移工具：** Spanner 提供了豐富的工具和服務，簡化了應用程式和資料的遷移過程。這些工具包括模式轉換工具、資料驗證工具和零停機遷移工具。
*   **Spanner 與 MySQL 和 Cassandra 的遷移：** Spanner 提供了專門的工具和服務，簡化了從 MySQL 和 Cassandra 遷移到 Spanner 的過程。這些工具包括 MySQL 相容的方言和 Cassandra 相容的 API。

## 3. 重要結論

Spanner 是一個功能強大且靈活的資料庫，可以幫助客戶構建智慧型、多模型應用程式。它提供了高可用性、無限擴展性和全球一致性，並且支援多種資料模型。Goldman Sachs 和 Walmart 等公司已經成功地使用 Spanner 來解決其複雜的資料管理問題。Spanner 正在成為現代應用程式的首選資料庫。
