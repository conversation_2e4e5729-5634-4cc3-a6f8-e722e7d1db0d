# What’s new in data and AI governance
[會議影片連結](https://www.youtube.com/watch?v=yrKez5h4PWY)
數據和AI治理的新趨勢

## 1. 核心觀點

本次會議主要探討了數據和AI治理的最新趨勢，強調了AI時代下治理的重要性，以及Google Cloud在治理方面的創新。核心觀點包括：

*   AI正在快速改變世界，但AI專案的失敗率很高，治理是成功的關鍵。
*   數據孤島、快速變化和文化差異是治理面臨的主要挑戰。
*   治理的目的是理解、信任和發現數據，從而簡化數據管理、加速洞察、符合法規並支持AI/ML專案。
*   Google認為治理應該是普遍的、智能的、開放的。
*   BigQuery Universal Catalog和Knowledge Engine是Google在治理方面的重要創新。

## 2. 詳細內容

*   **AI時代的治理挑戰：** 傳統的數據治理方法已經無法滿足AI時代的需求。現在，任何人都可以使用自然語言與複雜的數據管理系統互動，這使得確保數據安全、權限控制和合規性變得更加困難。

*   **治理的重要性：** 如果企業無法理解、信任和發現自己的數據，那麼即使採用了最新的AI技術，也無法做出明智的決策。良好的治理可以簡化數據管理，加速洞察，並確保企業符合不斷變化的法規。

*   **Google的治理願景：** Google認為，治理應該是普遍的，無縫集成到數據和AI平台中；應該是智能的，自動化元數據提取、數據解釋、數據安全和數據共享等過程；應該是開放的，支持各種格式和互操作性，避免廠商鎖定。

*   **BigQuery Universal Catalog：** BigQuery Universal Catalog是Google在BigQuery平台中引入的治理功能，它整合了Dataplex目錄和運行時元數據，為數據分析和治理提供了一個中心化的視圖。它包含三種類型的元數據：信息物理元數據（表名、列名、Profiling信息等）、業務元數據（對物理元數據的業務解釋）和運行時元數據（如Iceberg格式）。

*   **Knowledge Engine：** Knowledge Engine是BigQuery的一個核心組件，它可以自動收集和增強元數據，並使用自然語言生成數據洞察。它可以幫助用戶理解數據之間的關係，並通過自然語言提問來加速洞察。Google還將推出基於Knowledge Engine的語義搜索，以及數據質量建議等功能。

*   **Levi's的數據平台之旅：** Levi's分享了他們如何使用Google Cloud構建現代化數據平台的經驗。他們將數據視為產品，並使用Dataplex進行數據治理，Analytics Hub作為數據市場。他們還強調了數據網格的重要性，以及如何通過數據域來組織數據。Levi's的數據平台比以前快50倍，擁有700多個用戶，並發布了45個數據產品。

*   **Verizon的AI願景：** Verizon分享了他們的人性化AI願景，即利用AI來賦能員工和客戶。他們正在將數據從傳統的數據倉庫遷移到BigQuery，並構建一個統一的數據湖倉架構。Verizon強調了數據治理的重要性，並正在利用Google Cloud的治理功能來確保數據質量、數據沿襲、隱私控制和負責任的AI。

## 3. 重要結論

AI時代下，數據治理變得比以往任何時候都更加重要。Google Cloud正在通過BigQuery Universal Catalog和Knowledge Engine等創新功能，幫助企業構建現代化的數據治理體系。Levi's和Verizon的案例表明，通過採用正確的策略和工具，企業可以成功地利用數據來推動業務增長。
