# The future of app development with Firebase
[會議影片連結](https://www.youtube.com/watch?v=iP6yZEGYOSw)
Firebase 應用程式開發的未來

## 1. 核心觀點

本次會議主要探討 Firebase 如何演進以協助開發者更輕鬆地建構新一代的應用程式，特別是結合生成式 AI 的應用。重點包括：

*   **Firebase Studio 的推出：** 一個整合的開發環境，利用 AI 簡化應用程式的建立、執行和部署流程。
*   **App Hosting 正式發布 (GA)：** 提供 Git 中心化的伺服器端無伺服器託管解決方案，加速現代全端 Web 應用程式的開發和部署。
*   **Data Connect 正式發布 (GA)：** 為應用程式提供安全且可擴展的 SQL 資料庫解決方案，並支援向量嵌入 (vector embeddings)。
*   **GenKit 的持續發展：** 提供 SDK、外掛程式和開發工具，協助開發者建構 AI 驅動的應用程式。
*   **Google 開發者計畫 (GDP) 的新優惠：** 提供 AI 增強的工具、資源和技能提升，協助開發者規模化其應用程式。

## 2. 詳細內容

*   **Firebase Studio：**
    *   是一個基於 VS Code 的整合開發環境，提供 AI 驅動的程式碼生成、編輯和部署功能。
    *   允許開發者使用自然語言提示快速建立應用程式原型。
    *   支援多種框架，包括 Next.js、Android 和 Flutter。
    *   與 App Hosting 無縫整合，方便應用程式的部署和擴展。

*   **App Hosting：**
    *   是一個 Git 中心化的伺服器端無伺服器託管解決方案，簡化了現代 Web 應用程式的部署流程。
    *   支援 Angular 和 Next.js，並預覽支援 Astro 和 Nuxt。
    *   提供完整的 CI/CD、CDN 網路和伺服器端渲染。
    *   基於 Google Cloud 服務，提供 scale-to-zero 定價、DDoS 保護和企業級安全性。

*   **Data Connect：**
    *   為應用程式提供安全且可擴展的 SQL 資料庫解決方案，基於 Cloud SQL 上的 Postgres。
    *   允許開發者使用 GraphQL 定義資料庫結構和查詢。
    *   自動生成型別安全的 SDK，方便應用程式與資料庫互動。
    *   支援向量嵌入，簡化了檢索增強生成 (RAG) 的實作。

*   **GenKit：**
    *   提供 SDK、外掛程式和開發工具，協助開發者建構 AI 驅動的應用程式。
    *   支援 Node.js (穩定版) 和 Go (Beta)，並積極開發 Python 版本。
    *   提供 Google 官方外掛程式，用於 Gemini、Google AI 和 Vertex Model Gardens。
    *   提供 CLI 和開發者 UI，方便測試、除錯和評估 AI 函數和提示。
    *   Firebase AI 監控即將推出，提供 AI 追蹤和指標的彙總視圖。

*   **Google 開發者計畫 (GDP)：**
    *   提供 AI 增強的 Premium 和企業級優惠，協助開發者規模化其應用程式。
    *   包括 Gemini Code Assist、Google Cloud 開發者沙箱和 Google Cloud Skills Boost。

## 3. 重要結論

Firebase 持續進化，透過 Firebase Studio、App Hosting、Data Connect 和 GenKit 等工具，簡化應用程式開發流程，並協助開發者建構 AI 驅動的應用程式。Google 開發者計畫 (GDP) 提供額外的資源和支援，協助開發者規模化其應用程式。Firebase 致力於成為開發者建構未來應用程式的首選平台。
