# Search and Agents to drive site and app monetization
[會議影片連結](https://www.youtube.com/watch?v=tr_bv8zJhGY)
Search and Agents to drive site and app monetization

## 1. 核心觀點

本次會議主要探討如何利用搜尋和代理（Agents）來提升網站和應用程式的營利能力。核心觀點包括：

*   現代客戶期望更高的個人化搜尋和導航體驗。
*   Vertex AI Search 能夠提供更強大的搜尋功能、AI模式、個人化瀏覽和生成式模式，以滿足客戶需求。
*   Vertex AI Search 平台具有高度的客製化能力，讓使用者能夠快速上手並持續優化。
*   代理模式（Agentic mode）結合了搜尋、外部資料來源和API，能夠實現更複雜的任務，例如行程規劃和預約服務。

## 2. 詳細內容

會議首先指出，現今的客戶已經習慣了Google Search、Google Maps和YouTube等平台所提供的直觀和個人化體驗，因此對網站和應用程式也有更高的期望。如果網站或應用程式無法滿足客戶的特定需求，可能會導致搜尋放棄、轉換率下降、品牌忠誠度降低和客戶服務成本增加。

Vertex AI Search 旨在解決這些問題，它利用Google產品背後的AI技術，為網站和應用程式提供更強大的搜尋功能。Vertex AI Search 的主要功能包括：

*   **更強大的搜尋：** 能夠理解自然語言，提供個人化結果，並提高轉換率。
*   **AI模式：** 能夠回答使用者的問題，與使用者互動，並減少客戶服務需求。
*   **個人化瀏覽：** 能夠根據使用者的偏好客製化內容。
*   **生成式模式：** 能夠幫助使用者完成任務，例如規劃行程或預約服務。

Vertex AI Search 的優勢在於其靈活性和可客製化性。使用者可以導入任何類型的資料，包括網站資訊、目錄資訊和非結構化文件。Vertex AI Search 還會考慮使用者互動和業務規則，並利用知識圖譜、意圖理解、語義理解和深度學習模型來提供更相關和個人化的搜尋結果。

此外，Vertex AI Search 還提供了代理模式（Agentic mode），它結合了搜尋、外部資料來源（例如Google Maps和網路瀏覽）和API，能夠實現更複雜的任務。例如，代理模式可以幫助使用者研究優惠、預約服務、規劃行程或尋找房屋。

Vertex AI Search 平台讓使用者能夠快速上手，並提供高度的控制權，讓使用者能夠調整資料處理方式、排名演算法、個人化設定和AI回答。

## 3. 重要結論

Vertex AI Search 提供了一套完整的工具，可以幫助網站和應用程式提升搜尋體驗、提高客戶參與度和增加營利能力。它不僅提供了強大的搜尋功能和個人化能力，還提供了代理模式，可以實現更複雜的任務。Vertex AI Search 平台具有高度的客製化能力，讓使用者能夠快速上手並持續優化。
