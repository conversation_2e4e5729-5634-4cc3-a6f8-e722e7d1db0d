# Architectural approaches for RAG infrastructure
[會議影片連結](https://www.youtube.com/watch?v=O80umwW01oc)
RAG 基礎架構的架構方法

## 1. 核心觀點

本次會議主要探討在 Google Cloud 上建構和部署 RAG（Retrieval-Augmented Generation，檢索增強生成）應用程式的不同架構方法。會議涵蓋了 RAG 的基本概念、設計決策、影響架構選擇的因素，並介紹了四種預先封裝的架構選項，最後以一個架構選項的演示作結。

## 2. 詳細內容

*   **RAG 簡介：**
    RAG 是一種透過從可信任的資料來源檢索相關資料，並將其添加到 LLM（大型語言模型）的提示上下文中，從而產生有意義且真實的回應的技術。它有助於減少 LLM 的幻覺問題，並提供更具領域相關性和時效性的回應。

*   **RAG 的運作方式：**
    在 RAG 流程中，首先需要識別資料來源並上傳相關資料。接著，對資料進行準備、剖析和分塊，並為每個資料塊生成嵌入（embeddings），這些嵌入是資料在多維向量空間中的數值表示。這些嵌入儲存在向量資料庫或索引中。當收到提示時，也會使用相同的模型將其轉換為嵌入，並在向量資料庫中進行語義搜尋或相似性搜尋。檢索到的資料用於增強提示的上下文，然後將增強後的提示發送到 LLM，以生成具有上下文的回應。

*   **設計決策：**
    建構 RAG 應用程式時需要考慮多個設計決策，包括：
    *   資料來源：選擇哪些資料來源，以及資料的格式和準備方式。
    *   資料準備：使用哪些工具進行剖析和分塊，以及塊的大小和重疊。
    *   嵌入模型：選擇哪種嵌入模型來生成嵌入，以及如何保持資料和提示嵌入的一致性。
    *   向量儲存：選擇哪種向量資料庫或索引來儲存嵌入，例如 Vertex AI Vector Search、Cloud SQL with PGVector 或其他第三方解決方案。
    *   LLM 模型：選擇哪個 LLM 模型進行推論，以及如何託管和服務該模型。
    *   應用程式執行環境：選擇哪個執行環境來執行 RAG 應用程式，例如 Cloud Run、GKE 或 Compute Engine。

*   **架構選項：**
    會議介紹了四種不同的架構選項，涵蓋了從完全託管到高度 DIY 的範圍：
    *   **Vertex AI RAG Engine：**
        這是一個完全託管的 API，可以處理 RAG 流程的所有步驟，包括資料擷取、剖析、分塊、索引、儲存、提示準備、資料檢索、回應排序和服務。
    *   **DIY GKE 部署：**
        這種方法使用 GKE 叢集來執行推論伺服器、前端和嵌入服務。資料從 Cloud Storage 提取，使用 Cloud Storage FUSE CSI 驅動程式進行處理，嵌入儲存在 Cloud SQL 資料庫中。
    *   **Vertex AI + AlloyDB：**
        這種方法使用 Vertex AI 進行 LLM 推論和服務，使用 AlloyDB 作為向量資料庫。資料擷取流程使用 Cloud Run 和 Document AI 進行處理，嵌入儲存在 AlloyDB 中。
    *   **Vertex AI + Cloud Run：**
        這種架構使用 Vertex AI 進行所有 AI 相關操作，使用 Cloud Run 託管應用程式本身、前端、後端和資料處理作業。

*   **示範：**
    Megan 展示了一個使用 Vertex AI Vector Search、Cloud Run 和 Gemini 2.0 Flash 建構的 RAG 應用程式。該應用程式使用 Wikipedia 資料集中的量子計算文章，並允許使用者查詢有關最新量子計算發展的問題。

## 3. 重要結論

在 Google Cloud 上建構 RAG 應用程式有多種架構方法可供選擇，每種方法都有其優缺點。選擇哪種架構取決於具體的使用案例、需求和設計考量因素。建議從完全託管的選項開始，並根據需要逐步採用更 DIY 的方法。在選擇工具和模型之前，明確定義需求至關重要，並仔細評估各種選項之間的權衡。
