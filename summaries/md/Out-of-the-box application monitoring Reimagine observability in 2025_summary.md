Out-of-the-box application monitoring Reimagine observability in 2025

[會議影片連結](https://www.youtube.com/watch?v=uncLt6j1CXo)
開箱即用的應用程式監控，重新構想 2025 年的可觀測性

## 1. 核心觀點

本次會議主要介紹了 Google Cloud 中 Cloud Observability 的新功能，重點在於如何透過應用程式監控而非基礎設施監控，來提升團隊效率和應用程式效能。核心觀點包括：

*   **應用程式中心化監控：** 強調以應用程式為中心，而非傳統的基礎設施為中心，進行監控和故障排除。
*   **開箱即用的儀表板：** 提供針對應用程式、服務和工作負載的預設儀表板，方便快速了解系統健康狀況。
*   **整合的故障排除流程：** 將日誌、指標和追蹤整合在一起，加速故障排除過程。
*   **成本可視化：** 透過 Cost Explorer，將應用程式的成本與利用率數據結合，提供更全面的視角。

## 2. 詳細內容

會議首先介紹了 Cloud Hub，這是一個可以一覽應用程式健康狀況和問題的中心儀表板。透過 App Hub，可以自動發現多個專案中的服務和工作負載，並註冊額外的服務或工作負載。

Application Monitoring and Observability 提供了開箱即用的儀表板，用於監控應用程式、服務和工作負載。這些儀表板顯示了關鍵指標（黃金信號），並允許深入研究這些指標，查看正常運行時間檢查和應用程式日誌。如果日誌中出現錯誤，可以直接從日誌啟動新的調查，並自動提取相關上下文，從而加速故障排除。

Cost Explorer 是一個新功能，可以根據應用程式的概念，了解與應用程式相關的成本，並將其與應用程式的利用率數據結合。

會議強調，觀察應用程式而不是基礎設施，對團隊有實際的好處。

## 3. 重要結論

本次會議展示了 Google Cloud Observability 的新功能，這些功能旨在簡化應用程式監控和故障排除流程。透過 App Hub 註冊應用程式，可以啟用開箱即用的應用程式監控功能，並解鎖 Cost Explorer 和 Cloud Hub 等新功能。這些功能可以幫助團隊更快地了解應用程式的健康狀況、解決問題並優化成本。
