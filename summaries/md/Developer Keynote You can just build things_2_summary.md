# Developer Keynote You can just build things_2

[會議影片連結]()
Developer Keynote You can just build things_2

## 1. 核心觀點

本次會議主要圍繞著開發者如何利用 Google 的 AI 工具和平台，特別是 Gemini 模型和 Vertex AI，來構建各種應用和服務。核心觀點包括：

*   **AI 賦能開發者：** 強調 AI 技術，尤其是 Gemini 模型，在簡化開發流程、自動化任務和解決複雜問題方面的強大能力。
*   **Agent 的重要性：** 介紹 Agent 的概念，以及如何使用 Agent Development Kit (ADK) 和 Vertex AI Agent Engine 來構建和部署 Agent。
*   **多模型和 IDE 的選擇：** 強調開發者可以自由選擇最適合其需求的模型和 IDE，並展示了 Gemini 在多種 IDE 中的應用。
*   **AI 在各領域的應用：** 展示了 AI 在棒球分析、數據科學、軟體開發等領域的創新應用。
*   **未來展望：** 展望了 AI 在軟體開發中的未來，包括更強大的 Agent、更智能的工具和更簡化的開發流程。

## 2. 詳細內容

*   **Gemini 的強大功能：**
    *   展示了 Gemini 在程式碼生成、多模態推理和長文本處理方面的能力。
    *   強調 Gemini 在編碼方面的卓越表現，以及其在預訓練、後訓練和推理方面的創新。
*   **Agent 的概念和應用：**
    *   定義 Agent 為一種與 AI 模型交互以執行目標導向操作的服務。
    *   展示了如何使用 ADK 構建 Agent，包括定義指令、工具和模型。
    *   介紹了 Vertex AI Agent Engine，用於部署和運行 Agent，並提供企業級安全控制、監控和評估框架。
    *   展示了 Agent Space，一個用於 Agent 發現和共享的中心，允許開發者構建無程式碼 Agent 並註冊使用 ADK 構建的 Agent。
*   **多 Agent 系統：**
    *   介紹了如何構建多 Agent 系統，其中多個專業 Agent 協同工作以處理複雜流程。
    *   展示了如何使用 ADK 編排多 Agent 系統，並將其部署到 Vertex AI Agent Engine。
    *   介紹了 Cloud Investigations，一種用於診斷和修復 Agent 服務問題的工具。
    *   強調了 Agent-to-Agent 協議的重要性，該協議標準化了 Agent 之間的連接和通信。
*   **開發者工具和 IDE：**
    *   展示了 Gemini 在多種 IDE 中的應用，包括 Windsurf、Cursor 和 IntelliJ。
    *   介紹了 Vertex AI Model Garden，允許開發者連接到各種模型或從 Hugging Face 等註冊表中引入自己的模型。
    *   強調了 Gemini Code Assist 的可用性，以及其在 Android Studio 和 Firebase Studio 中的集成。
*   **AI 在各領域的應用案例：**
    *   展示了 MLB 如何使用 Gemini 和 Vertex AI 分析棒球運動員的表現。
    *   介紹了 Google Cloud MLB Hackathon 的獲獎者，展示了如何使用 Gemini API 和 Google Cloud 分析投球。
    *   展示了 AI 在冬季 X Games 中的應用，包括 AI 評論員和評分分析。
    *   介紹了數據科學 Agent，可以幫助用戶將原始數據轉換為數據應用。
    *   展示了軟體工程 Agent，可以自動執行重複性任務並協助開發者構建應用。
*   **VO 的應用：**
    *   展示了 VO 在娛樂領域的應用，包括在 Sphere 中呈現的《綠野仙蹤》沉浸式體驗。

## 3. 重要結論

本次會議強調了 Google 對於 AI 賦能開發者的承諾，並展示了各種工具和平台，使開發者能夠更輕鬆地構建和部署 AI 應用。會議重點介紹了 Gemini 模型、Agent 技術和 Vertex AI 平台，並展示了 AI 在各個領域的創新應用。會議還展望了 AI 在軟體開發中的未來，包括更強大的 Agent、更智能的工具和更簡化的開發流程。Google 鼓勵開發者利用這些工具和平台，積極參與到 AI 驅動的創新中。
