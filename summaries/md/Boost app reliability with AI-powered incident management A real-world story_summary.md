```
# Boost app reliability with AI-powered incident management A real-world story
[會議影片連結](https://www.youtube.com/watch?v=1j87FwgAnMU)
使用AI驅動的事件管理提升應用程式可靠性：真實案例分享

## 1. 核心觀點

本次會議主要探討雲端複雜性對故障排除流程的影響，以及如何利用AI（特別是Google的Gemini模型）來加速事件調查和根本原因分析（RCA）。Charles Schwab的SRE主管分享了他們在實踐中遇到的挑戰和解決方案，並展示了Google Cloud Assist Investigations這款新產品如何簡化故障排除流程，提升團隊效率，最終減少平均檢測時間（MTTD）和平均修復時間（MTTR）。

## 2. 詳細內容

會議首先指出，現代企業軟體系統的複雜性日益增加，導致故障難以預測和排除，尤其是在非工作時間發生的故障，對團隊成員的個人生活造成重大影響。傳統的事件響應工具和流程在自動化方面進展緩慢，需要經驗豐富的專家才能快速定位問題，這使得新員工難以快速上手，增加了培訓成本。

為了解決這些問題，Google Cloud與Charles Schwab合作，開發了Cloud Assist Investigations。這個產品利用AI技術，自動收集和分析來自不同來源的數據（例如日誌、指標、配置變更等），並生成關於潛在根本原因的假設，以及建議的修復方案。

Charles Schwab的SRE主管分享了他們使用Gemini模型構建RCA代理的經驗。該代理能夠在SRE團隊介入之前，自動分析警報和日誌，生成詳細的報告，幫助團隊更快地了解問題的根源。他們通過實際案例展示了該代理如何區分噪音和真實問題，並提供有用的修復建議。

Cloud Assist Investigations的演示展示了該產品如何從日誌條目或其他入口點啟動，自動發現應用程式和資源的拓撲結構，收集相關事件和配置信息，並利用AI引擎生成關於根本原因的假設和建議的修復方案。演示還展示了如何追溯到代碼變更，以確定導致問題的具體原因。

Waze團隊的案例表明，Cloud Assist Investigations能夠顯著縮短故障排除時間，甚至可以挽救工程師的週末。

## 3. 重要結論

AI技術在事件管理和故障排除方面具有巨大的潛力，可以幫助企業顯著提升應用程式的可靠性，減少MTTD和MTTR，並提高團隊的效率。Cloud Assist Investigations是一款有前景的產品，可以幫助企業簡化故障排除流程，並賦予經驗不足的工程師更強大的能力。然而，每個企業的數據和環境都不同，需要進行實際測試和驗證，才能充分發揮該產品的價值。
```