# The future of AI-powered data is autonomous and agent-driven_2
[會議影片連結]()
The future of AI-powered data is autonomous and agent-driven_2

## 1. 核心觀點

本次會議主要探討了人工智慧（AI）驅動的數據的未來發展方向，強調自主性和代理驅動的重要性。核心觀點包括：

*   **數據和AI的融合：** 強調數據和AI需要無縫協作，自主運行，以實現即時的業務成果。
*   **AI代理的崛起：** 認為AI代理將在2024年大放異彩，推動複雜的生產力和工作流程。
*   **即時性的關鍵作用：** 強調即時性是AI的關鍵因素，能夠將先進的智慧和代理應用於實際工作。
*   **BigQuery的演進：** 宣布BigQuery將演進為自主數據到AI平台，具備代理能力、智慧、即時性和自主性。
*   **BI的轉型：** 認為傳統BI已經過時，Looker將轉型為統一、可信和會話式的平台。
*   **解決數據挑戰：** 指出數據孤島、複雜性和缺乏即時性是組織利用數據的三大挑戰。

## 2. 詳細內容

*   **客戶案例分享：**
    *   Radisson Hotel Group透過在BigQuery數據上訓練Gemini模型，實現了廣告的規模化個人化，生產力提高了50%，AI驅動的廣告活動收入增長了20%以上。
    *   Gordon Food Service遷移到BigQuery，確保數據為AI做好準備，實現了近乎無限的擴展，並將面向客戶的應用程式的採用率提高了96%以上。
    *   JB Hunt從分散的傳統系統（包括Databricks）切換到BigQuery，以轉變其運輸和物流體驗。
*   **Google Cloud的創新：**
    *   推出了近400項新功能。
    *   多模態AI分析增加了16倍。
    *   客戶每月將數百PB的數據流式傳輸到BigQuery。
*   **BigQuery的優勢：**
    *   擁有比其他兩個企業數據平台多五倍的客戶。
    *   BigQuery和Vertex AI的成本效益比任何其他數據倉庫或AI平台高8到16倍。
*   **BigQuery的演進方向：**
    *   **代理能力（Agentic）：** 加速數據工作。
    *   **智慧（Intelligent）：** 利用AI的真實世界知識。
    *   **即時性（Real-time）：** 即時處理數據。
    *   **自主性（Autonomous）：** 自動激活數據和洞察。
*   **Looker的轉型：**
    *   將BI帶入AI時代。
    *   發展成為統一、可信和會話式的平台。
    *   融合自助服務、即時性和受治理的語義。
    *   提供新的會話式分析功能。
*   **三大核心領域的創新：**
    *   **專業代理（Specialized Agents）：** 將AI的變革力量交給每個用戶。
    *   **先進的可互操作引擎（Advanced Interoperable Engines）：** 提供創新的數據分析方法，具有無與倫比的性能、可擴展性和靈活性。
    *   **自主數據基礎（Autonomous Data Foundation）：** 自動化整個數據生命週期，從數據提取到洞察生成，讓團隊專注於創新。
*   **專業代理的應用：**
    *   **數據工程師代理：** 自動化數據準備和標準化的繁瑣任務，主動檢測異常，確保數據品質，甚至自動化元數據生成的治理。
    *   **數據科學家代理：** 提供下一代筆記本體驗，包括嵌入式數據科學代理，以即時提供高級分析。
    *   **會話式分析代理：** 透過Gemini 2.0的先進推理能力，處理最複雜的問題，並提供客製化的代理，以滿足特定角色和部門的需求。
*   **BigQuery知識引擎：**
    *   為BigQuery中的AI和代理提供關鍵的上下文，使其更有效。
    *   持續從組織的數據中學習，了解數據之間的關係，知道查詢日誌中的模式，並了解業務術語的含義。
    *   提供即時建議和上下文，將準確性和相關性提高多達50%。
*   **先進的可互操作引擎：**
    *   **AI查詢引擎：** 結合傳統SQL協同處理和AI，利用AI的理解能力來獲得更深入的洞察。
    *   **向量搜尋（Vector Search）：** 建立在Google的尖端掃描技術之上，不僅可以找到完全相同的事物，還可以找到大量數據集中相似的事物。
    *   **連續查詢（Continuous Queries）：** 不僅可以即時提取數據，還可以處理數據，使用機器學習或AI進行分析，並在數據到達後幾秒鐘內做出決策和觸發操作。
    *   **Spark引擎：** 可以在BigQuery中運行Apache Spark，無需移動數據。
    *   **Kafka引擎：** 確保數據始終是最新的。
*   **自主數據基礎：**
    *   消除數據孤島，使非結構化數據成為一等公民。
    *   透過BigQuery多模態表，可以在同一個表中儲存和查詢非結構化數據（圖像、音訊、語音和文字）以及結構化數據。
    *   提供對Apache Iceberg的最佳市場支援，這是一種完全託管且開放格式的解決方案。
    *   提供統一的治理，智慧、普遍且隱形地跨越所有數據，並擴展到BI和AI模型。
    *   透過先進的工作負載管理，可以將資源與業務優先級對齊。
    *   透過新的BigQuery支出承諾，提供跨BigQuery平台的統一支出，讓您可以靈活地在數據處理引擎（如SQL或Spark）、串流、治理等之間轉移支出。

## 3. 重要結論

本次會議強調了AI驅動的數據的未來是自主和代理驅動的，並詳細介紹了Google Cloud在BigQuery和Looker方面的創新，以實現這一願景。透過專業代理、先進的可互操作引擎和自主數據基礎，Google Cloud旨在幫助組織消除數據孤島、簡化複雜性並實現即時洞察，最終加速AI的採用，並使企業能夠比以往更快地從數據轉向AI驅動的洞察。
