# Cluster Director with GKE Optimal performance at max scale
[會議影片連結](https://www.youtube.com/watch?v=Ki3EYE8ME6Q)
適用於 GKE 的叢集管理器，在最大規模下實現最佳效能

## 1. 核心觀點

本次演講介紹了 Google Kubernetes Engine (GKE) 的 Cluster Director，旨在為大規模 AI 工作負載提供最佳效能和彈性。核心觀點包括：

*   Cluster Director 能夠部署和管理大規模的加速虛擬機器叢集，將運算、儲存和網路視為一個單一單元。
*   透過標準 Kubernetes API 進行協調，無需學習新的平台或工具。
*   提供高效能、超大規模、拓撲感知排程、易於使用、360 度可觀測性和高彈性等關鍵價值。

## 2. 詳細內容

Cluster Director for GKE 旨在解決 AI 模型訓練所需的龐大運算需求。由於訓練工作負載高度同步，且跨越數千個節點，因此單一節點的效能下降可能會影響整個工作。Cluster Director 透過以下六個關鍵價值主張來優化 AI 工作負載：

1.  **高效能：** 透過動態 ML 網路結構，將密集共置的加速器作為一個單元運行，從而降低網路延遲並優化效能。
2.  **超大規模：** GKE 支援 65,000 個節點，是託管 Kubernetes 供應商中規模最大的，讓使用者能夠自信地運行超大規模訓練工作。
3.  **拓撲感知排程：** 根據網路拓撲檢視基礎設施拓撲並排程 Pod，從而減少網路躍點數，優化頻寬利用率，並降低延遲。
4.  **易於使用：** 透過易於部署的 UI 或 Cluster Toolkit 中基於 Terraform 的可配置藍圖，輕鬆設定 AI 優化的 GKE 叢集。此外，還可以使用 XPK 的易於使用的非 Kubernetes UI。
5.  **360 度可觀測性：** 提供開箱即用的全堆疊主動基礎設施和工作負載監控，以幫助識別瓶頸、優化加速器利用率並監控叢集。
6.  **高彈性：** 確保工作負載在效能最佳的加速器上運行，並能夠報告和更換故障節點，方法是從節點上優雅地驅逐工作負載，並使用共置區域內的備用容量自動替換它們。此外，還可以管理主機維護，以便直接從 GKE API 手動啟動維護，或在使用 Kubernetes 概念排程工作負載時使用管理資訊。

所有這些功能都可以透過 GKE API 獲得。

## 3. 重要結論

Cluster Director for GKE 是一個強大的工具，可以幫助使用者在 GKE 上部署、擴展和管理 AI 優化的叢集，從而實現大規模 AI 工作負載的最佳效能和彈性。透過標準 Kubernetes API 的整合，使用者可以輕鬆地將 Cluster Director 納入現有的工作流程中。
