Shhh, my AI’s got secrets Keep them safe with confidential computing

[會議影片連結](https://www.youtube.com/watch?v=L2gQcB0M5Og)
噓，我的AI有一些秘密，用機密運算保護它們的安全

## 1. 核心觀點

本次會議主要探討了機密運算在保護人工智慧（AI）模型、資料和應用程式方面的作用。核心觀點包括：

*   機密運算有助於在雲端環境中保護資料的隱私和安全，即使是雲端供應商也無法存取。
*   隨著AI應用的普及，機密運算變得越來越重要，因為它可以保護敏感資料免受未經授權的存取和濫用。
*   NVIDIA和Google Cloud正在合作，將機密運算技術整合到其AI平台中，使開發人員能夠更輕鬆地構建和部署安全的AI應用程式。
*   機密運算不僅適用於保護資料，還適用於保護AI模型本身，防止模型被竊取或篡改。
*   未來的AI應用程式將更加依賴多個模型和資料來源，機密運算將在建立這些協作實體之間的信任方面發揮關鍵作用。

## 2. 詳細內容

*   **Vint Cerf（Google首席網路傳教士）**強調了網際網路的開放性以及由此帶來的創新，但也強調了隱私的重要性。他分享了自己使用大型語言模型（LLM）的經驗，並指出需要改進LLM的可靠性和可信度。他認為機密運算對於保護開發者的智慧財產權至關重要，並有助於建立對雲端AI系統的信任。

*   **Sam Lugani（Google Cloud機密運算產品管理負責人）**回顧了Google在機密運算領域的發展歷程，並介紹了Google Cloud提供的各種機密運算功能。他強調了機密運算在各個行業中的應用，例如金融服務、醫療保健和Web3。他還介紹了Google Cloud的Confidential Space解決方案，該解決方案允許多方在不完全信任彼此的情況下協作處理資料。此外，他宣布Gemini Cloud Assist現在支援機密運算，使開發人員能夠更輕鬆地建立和部署機密應用程式。

*   **Daniel（NVIDIA產品安全負責人）**分享了NVIDIA對AI發展的願景，以及機密運算在實現這一願景中的作用。他指出，隨著AI模型變得越來越複雜和強大，保護這些模型的安全變得越來越重要。他介紹了NVIDIA的Hopper架構，該架構支援機密運算，並宣布NVIDIA正在與Google合作，將機密運算功能整合到其Blackwell架構中。他還強調了attestation服務在建立AI系統信任方面的重要性。

*   **Joanna（Google Cloud）**展示了如何在Google Kubernetes Engine（GKE）上輕鬆建立機密Kubernetes叢集，並分享了機密GPU的效能測試結果。她指出，機密GPU的效能與非機密GPU相當，因此建議將其用於推論任務。她還介紹了OPPO等客戶如何使用機密運算來保護其使用者的隱私。最後，她展示了一個私人推論聊天機器人的UI，該聊天機器人使用機密VM和NVIDIA H100 GPU。

## 3. 重要結論

機密運算正在成為保護AI模型、資料和應用程式的關鍵技術。Google Cloud和NVIDIA正在積極投資於機密運算，並將其整合到其AI平台中。隨著AI應用的普及，機密運算將在建立對AI系統的信任和促進AI創新方面發揮越來越重要的作用。本次會議強調了機密運算在AI領域的重要性，並展示了其在各種實際應用中的潛力。
