# What’s new in Gemini Code Assist
[會議影片連結](https://www.youtube.com/watch?v=WjFJr5mLxsY)
Gemini Code Assist 最新功能

## 1. 核心觀點

本次會議主要介紹 Gemini Code Assist 的最新功能和未來發展方向，強調其在提升開發者生產力、改善程式碼品質以及簡化軟體開發生命週期中的作用。核心觀點包括：

*   **以開發者為中心：** 致力於提供快速、簡單且易於使用的開發者體驗。
*   **企業級解決方案：** 專為大型企業設計，提供安全、可靠且可擴展的服務。
*   **涵蓋完整軟體開發生命週期：** 不僅僅關注程式碼編寫，更涵蓋規劃、建構、測試、部署和監控等各個階段。
*   **情境感知程式碼輔助：** 根據企業特定的程式碼風格、標準和函式庫，提供客製化的程式碼建議和自動完成功能。
*   **品質與效能並重：** 將生產力提升作為提高軟體交付效能的關鍵指標。
*   **AI 驅動的工具與代理：** 利用 AI 技術自動化重複性任務，並提供智慧化的程式碼審查和問題解決方案。

## 2. 詳細內容

*   **Gemini Code Assist 的演進：**
    *   回顧了 Gemini Code Assist 在 2024 年的發展歷程，包括模型升級、產品分叉為標準版和企業版，以及推出 Code Assist 工具和代理。
    *   強調了模型持續改進的重要性，並預告了 Gemini 2.5 的推出。

*   **AI 在軟體開發中的影響：**
    *   引用 DORA 的研究報告，指出 AI 在軟體開發中帶來了積極的影響，例如提高生產力和改善程式碼品質。
    *   同時也指出了 AI 帶來的挑戰，例如降低程式碼穩定性和產生不信任感。

*   **Gemini Code Assist 的願景：**
    *   提供一個 AI 驅動、專為企業打造的 SaaS 產品組合，以增強整個軟體開發生命週期。
    *   透過情境感知的程式碼輔助，提高程式碼品質和效能，最終提升軟體交付效能。

*   **情境感知的重要性：**
    *   強調了情境在程式碼輔助中的重要性，將情境分為專案情境、企業情境和工程情境。
    *   專案情境包括開發者工作區中的所有內容，例如程式碼庫和應用程式。
    *   企業情境包括企業的程式碼編寫標準、函式庫和 SDK。
    *   工程情境包括設計文件、PRD、JIRA 任務和 GitHub Issue。

*   **操作控制與可見性：**
    *   提供了對情境的操作控制，例如控制哪些內容被索引，以及誰可以存取哪些索引。
    *   提供了詳細的日誌記錄和分析功能，以追蹤使用情況、採用率和程式碼品質。

*   **Code Assist 工具與代理：**
    *   Code Assist 工具是可以在聊天視窗中觸發的插件程式碼，用於修改提示或回應。
    *   Code Assist 代理是半自主的程式碼，可以執行複雜或長時間運行的任務鏈。
    *   展示了如何使用 Code Assist 工具從 Google Docs 中獲取需求，並使用 Code Assist for Apigee 建立 API 規格。
    *   展示了如何使用 Code Assist 代理進行程式碼審查，並自動修復程式碼中的錯誤。

*   **Gemini Code Assist 的版本：**
    *   **個人版：** 免費提供給學生、業餘愛好者、自由工作者和新創開發者使用，提供 IDE 插件、程式碼自動完成、單元測試生成和除錯協助等功能。
    *   **標準版：** 提供企業級的法律保障，以及 Gemini for Android Studio、Gemini in Firebase 和 Gemini in Databases 等工具。
    *   **企業版：** 提供程式碼客製化、可見性和組織策略控制等功能，以及 Gemini in BigQuery 和 Gemini in App Integration 等進階工作流程。

*   **企業程式碼客製化：**
    *   允許企業安全地索引和參考其私有程式碼庫，以提供客製化的程式碼建議。
    *   程式碼儲存在隔離的私有租戶環境中，不會被 Google 工程師看到，也不會用於模型訓練。

*   **可見性儀表板：**
    *   提供對 Gemini Code Assist 使用情況和影響的可見性，包括採用率、信任度和程式碼建議數量。
    *   允許企業將這些數據與 DORA 指標、Jira 故事點或收入等指標聯繫起來，以評估 Code Assist 的價值。

*   **Code Assist for GitHub：**
    *   提供程式碼審查代理，可以自動生成 Pull Request 摘要，並提供潛在問題和建議修復。
    *   支援程式碼風格指南，以確保程式碼的一致性。

*   **Sentry 的整合：**
    *   Sentry 是一個監控平台，可以提供有關應用程式中錯誤和效能問題的詳細資訊。
    *   Sentry 與 Gemini Code Assist 的整合，可以將 Sentry 的錯誤資訊和追蹤資料帶入 Code Assist 中，以提供更全面的情境資訊。
    *   展示了如何使用 Sentry 工具在 Code Assist 中查詢 Sentry 環境，並獲取有關組織、專案和問題的資訊。

## 3. 重要結論

Gemini Code Assist 正在不斷發展，旨在成為開發者不可或缺的工具，透過 AI 技術簡化軟體開發流程，提高程式碼品質和開發效率。無論是個人開發者還是大型企業，都可以從 Gemini Code Assist 中受益。
