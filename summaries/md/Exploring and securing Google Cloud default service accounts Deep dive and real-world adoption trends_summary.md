Exploring and securing Google Cloud default service accounts Deep dive and real-world adoption trends

[會議影片連結](https://www.youtube.com/watch?v=WOSyvCNd6yI)
Exploring and securing Google Cloud default service accounts Deep dive and real-world adoption trends

## 1. 核心觀點

本次會議主要探討 Google Cloud Platform (GCP) 中預設服務帳戶濫用的問題，這是一個主要的威脅來源。會議強調了預防此類威脅的方法，並提倡使用服務帳戶作為一種有效的安全機制。核心觀點包括：

*   預設服務帳戶雖然方便，但可能存在過度授權的風險。
*   Google 已經採取措施限制預設服務帳戶的權限，但舊組織可能仍然存在風險。
*   可以透過 IAM 權限微調、單一用途服務帳戶和 Workload Identity Federation 等方法來降低風險。

## 2. 詳細內容

會議首先解釋了服務帳戶在 GCP 中的作用，它們允許資源在不需要儲存長期憑證的情況下相互交互，並提供對資源所需權限進行微調的能力。舉例說明了 Web 伺服器如何使用服務帳戶從儲存貯體載入靜態資源，以及應用程式伺服器如何使用服務帳戶讀寫 SQL 資料庫。

接著，會議深入探討了預設服務帳戶的風險。雖然預設服務帳戶方便開發人員快速部署和執行虛擬機器，但歷史上它們通常被授予專案層級的編輯者權限。即使現在可以使用存取範圍在 VM 層級調整這些權限，仍然存在風險。Google 已經新增了一項預設啟用的限制，以防止這種情況，但對於在去年五月之前建立的組織，這些預設服務帳戶可能仍然存在過度授權的風險。

會議強調，即使預設存取範圍限制了機器上可用的 Token，但如果服務帳戶在專案層級具有角色，則仍然可以存取該專案中的任何儲存貯體。這意味著，如果攻擊者入侵了主機，他們可能能夠存取儲存敏感資料的儲存貯體，即使 Web 伺服器實際上不需要從這些儲存貯體讀取資料。如果啟用完整的 API 存取，攻擊者甚至可以建立虛擬機器、建立服務帳戶的 API 金鑰或刪除基礎架構。

為了防止預設服務帳戶被濫用，會議提出了三種方法：

1.  **使用 IAM 權限並進行微調，採用最小權限原則。** 不要使用專案層級的廣泛編輯者角色，而是根據需要微調權限。例如，Web 伺服器只需要從特定儲存貯體讀取的權限，而應用程式伺服器只需要對 Cloud SQL 中特定資料庫的讀寫權限。
2.  **使用單一用途服務帳戶。** 為每個應用程式或服務建立單獨的服務帳戶，而不是為多個應用程式或服務使用一個具有所有必要權限的服務帳戶。例如，為 Web 伺服器建立一個具有儲存貯體權限的服務帳戶，並為應用程式伺服器建立一個具有資料庫權限的單獨服務帳戶。
3.  **使用 Workload Identity Federation。** 這是 Google Cloud 提供的一項專門針對 Kubernetes Engine 的服務，它在 Google Cloud 端處理憑證和身份驗證，而不是在運算層級提供短期 Token。如果 Kubernetes 實例遭到入侵，則檢索到的任何存取 Token 預設情況下都沒有權限，因此無法被攻擊者濫用。

會議還提到了 Google 的威脅視野報告，該報告一直顯示針對 Google 專案中預設服務帳戶和編輯者角色的攻擊。

## 3. 重要結論

預設服務帳戶濫用是 Google Cloud 中的一個主要威脅，但可以透過 IAM 權限微調、單一用途服務帳戶和 Workload Identity Federation 等方法來降低風險。組織應檢查其環境，識別具有編輯者權限的預設服務帳戶，並採取措施限制這些帳戶的權限。Datadog 的部落格提供了一個關於真實攻擊的案例研究，可以作為參考。
