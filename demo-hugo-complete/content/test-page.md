---
title: "測試頁面 - Markdown SSG 渲染"
date: 2025-07-04T17:05:00Z
draft: false
type: "page"
---

# 測試頁面 - Markdown SSG 渲染

這是一個測試頁面，用來展示 Hugo 的 SSG (Static Site Generation) 如何將 Markdown 渲染成 HTML。

## 功能展示

### 1. 文字格式

- **粗體文字**
- *斜體文字*
- `程式碼`
- ~~刪除線~~

### 2. 列表

#### 無序列表
- 項目 1
- 項目 2
  - 子項目 2.1
  - 子項目 2.2
- 項目 3

#### 有序列表
1. 第一項
2. 第二項
3. 第三項

### 3. 程式碼區塊

```python
def hello_world():
    print("Hello, Hugo SSG!")
    return "Markdown 已成功渲染為 HTML"

# 呼叫函數
result = hello_world()
print(result)
```

```javascript
// JavaScript 範例
function renderMarkdown() {
    console.log("Hugo 正在將 Markdown 轉換為靜態 HTML");
    return {
        status: "success",
        message: "SSG 渲染完成"
    };
}
```

### 4. 表格

| 功能 | 狀態 | 說明 |
|------|------|------|
| Markdown 解析 | ✅ | 完成 |
| HTML 生成 | ✅ | 完成 |
| CSS 樣式 | ✅ | 完成 |
| JavaScript | ✅ | 完成 |

### 5. 引用

> 這是一個引用區塊。Hugo 會將這個 Markdown 文件轉換為靜態 HTML，
> 提供快速的載入速度和優秀的 SEO 效果。

### 6. 連結

- [Hugo 官網](https://gohugo.io/)
- [Markdown 語法指南](https://www.markdownguide.org/)
- [回到首頁](/)

### 7. 圖片 (示例)

![Hugo Logo](https://gohugo.io/images/hugo-logo-wide.svg)

## SSG 渲染說明

當您訪問這個頁面時，您看到的是：

1. **原始 Markdown**: 這個 `.md` 文件包含了結構化的文字內容
2. **Hugo 處理**: Hugo 讀取 Markdown 並應用模板
3. **HTML 輸出**: 生成靜態 HTML 文件在 `public/` 目錄
4. **瀏覽器顯示**: 您現在看到的渲染後的網頁

## 檢查渲染結果

要查看這個頁面的 SSG 渲染結果：

1. **瀏覽器**: 訪問 http://localhost:1313/test-page/
2. **HTML 文件**: 查看 `public/test-page/index.html`
3. **原始碼**: 在瀏覽器中按 F12 查看生成的 HTML

---

*這個頁面展示了 Hugo 如何將 Markdown 內容轉換為美觀的靜態網頁*
